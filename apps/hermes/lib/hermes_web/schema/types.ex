defmodule HermesWeb.Schema.Types do
  @moduledoc false

  use HermesWeb, :type

  ########################################################
  # ISO Naive DateTime Types                             #
  ########################################################
  scalar :iso_naive_datetime, description: "ISO Naive DateTime" do
    parse(&decode_naive/1)
    serialize(&encode_naive/1)
  end

  defp encode_naive(%NaiveDateTime{} = val), do: Timex.format!(val, "{ISO:Extended:Z}")

  defp decode_naive(%Absinthe.Blueprint.Input.Null{}) do
    {:ok, nil}
  end

  defp decode_naive(%Absinthe.Blueprint.Input.String{value: value}) do
    Timex.parse(value, "{ISO:Extended:Z}")
  end

  ########################################################
  # ISO DateTime Types                                   #
  ########################################################
  scalar :iso_datetime, description: "ISO DateTime with timezone" do
    parse(&decode_datetime/1)
    serialize(&encode_datetime/1)
  end

  defp encode_datetime(%DateTime{} = val), do: DateTime.to_iso8601(val)

  defp decode_datetime(%Absinthe.Blueprint.Input.Null{}) do
    {:ok, nil}
  end

  defp decode_datetime(%Absinthe.Blueprint.Input.String{value: value}) do
    case DateTime.from_iso8601(value) do
      {:ok, datetime, _offset} -> {:ok, datetime}
      error -> error
    end
  end

  ########################################################
  # Options Types                                        #
  ########################################################
  input_object :options_input do
    field(:filters, list_of(:filter_input))
    field(:orders, list_of(:order_input))
  end

  object :options do
    field(:filters, list_of(:filter))
    field(:orders, list_of(:order))
  end

  ########################################################
  # Filter Types                                         #
  ########################################################
  input_object :filter_input do
    field(:key, :string)
    field(:value, :string)
  end

  object :filter do
    field(:key, :string)
    field(:value, :string)
  end

  ########################################################
  # Order Types                                          #
  ########################################################
  input_object :order_input do
    field(:key, :string)
    field(:value, :string)
  end

  object :order do
    field(:key, :string)
    field(:value, :string)
  end

  ########################################################
  # Map Types                                            #
  ########################################################
  scalar :map, name: "Map" do
    description("""
    The `Map` scalar type represents an Elixir Map as JSON
    """)

    serialize(&to_map/1)
    parse(&parse_map/1)
  end

  defp to_map(%{} = value), do: value
  defp to_map(_), do: :error

  @spec parse_map(Absinthe.Blueprint.Input.String.t()) :: {:ok, term()} | :error
  @spec parse_map(Absinthe.Blueprint.Input.Null.t()) :: {:ok, nil}
  defp parse_map(%Absinthe.Blueprint.Input.String{value: value}) do
    case Jason.decode(value) do
      {:ok, result} -> {:ok, result}
      _ -> :error
    end
  end

  defp parse_map(%Absinthe.Blueprint.Input.Null{}) do
    {:ok, nil}
  end

  defp parse_map(_) do
    :error
  end
end
