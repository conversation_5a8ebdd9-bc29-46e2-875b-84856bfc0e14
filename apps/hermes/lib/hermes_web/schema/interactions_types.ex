defmodule HermesWeb.Schema.InteractionsTypes do
  @moduledoc false

  use HermesWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  import Absinthe.Resolution.Helpers, only: [dataloader: 1]

  alias HermesWeb.Middleware
  alias HermesWeb.Resolvers.Interactions

  connection node_type: :media_announcement do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Interactions.TotalMediaAnnouncements.resolve/3)
    end

    edge do
      field(:media_announcement, non_null(:media_announcement))
    end
  end

  enum(:media_survey_answer_type, values: Gaia.Interactions.MediaSurveyAnswer.get_answers())

  enum(:media_survey_question_type, values: Gaia.Interactions.MediaSurveyAnswer.get_questions())
  enum(:comment_source, values: Gaia.Interactions.MediaComment.get_comment_sources())

  input_object :media_comment_annotation_metadata_input do
    field(:left, non_null(:float))
    field(:page_index, non_null(:integer))
    field(:top, non_null(:float))
  end

  object :media do
    field(:id, non_null(:id))
    field(:target_date, :iso_datetime)
    field(:primary_distribution, :string)

    field(:company_profile, :company, do: resolve(dataloader(Gaia.Repo)))
    field(:media_announcement, :media_announcement, do: resolve(dataloader(Gaia.Repo)))
    field(:media_update, :media_update, do: resolve(dataloader(Gaia.Repo)))

    field(:liked, non_null(:boolean), do: resolve(&Interactions.MediaFields.liked/3))

    field(:likes_count, non_null(:integer), do: resolve(&Interactions.MediaFields.likes_count/3))
  end

  object :homepage_announcements_and_updates do
    field(:id, non_null(:id))
    field(:latest_two_announcements, non_null(list_of(non_null(:media_announcement))))
    field(:latest_update, :media_update)
    field(:latest_annual_report, :media_announcement)
    field(:latest_investor_presentation, :media_announcement)
    field(:latest_quarterly_cashflow_report, :media_announcement)
  end

  object :discover_block_announcements_and_updates do
    field(:id, non_null(:id))
    field(:latest_update, :media_update)
    field(:latest_annual_report, :media_announcement)
    field(:latest_investor_presentation, :media_announcement)
    field(:latest_quarterly_cashflow_report, :media_announcement)
  end

  object :media_announcement do
    field(:id, non_null(:id))

    field(:header, non_null(:string))
    field(:listing_key, non_null(:string))
    field(:market_key, non_null(:string))
    field(:market_sensitive, :boolean)
    field(:media_id, non_null(:id))
    field(:news_publisher, :string)
    field(:posted_at, non_null(:iso_naive_datetime))
    field(:rectype, :string)
    field(:social_video_url, :string)
    field(:subtypes, non_null(list_of(:string)))
    field(:summary, :string)
    field(:summary_ai, :string)
    field(:thumbnail_is_portrait, :boolean)
    field(:url, non_null(:string))
    field(:video_url, :string)
    field(:german_translated_url, :string)
    field(:german_translated_header, :string)
    field(:german_translated_video_url, :string)
    field(:german_translated_summary, :string)
    field(:featured_on_hub, non_null(:boolean))
    field(:redirect_to, :string)

    field(:likes, non_null(:integer)) do
      resolve(&Interactions.MediaAnnouncementFields.likes/3)
    end

    field(:public_comment_count, non_null(:integer)) do
      arg(:hub, :string)
      resolve(&Interactions.MediaAnnouncementFields.public_comment_count/3)
    end

    field(:total_parent_comments, non_null(:integer)) do
      arg(:hub, :string)
      resolve(&Interactions.MediaAnnouncementFields.total_parent_comments/3)
    end

    field(:total_view_count, non_null(:integer)) do
      resolve(&Interactions.MediaAnnouncementFields.total_view_count/3)
    end

    field(:thumbnail_url, :string) do
      resolve(&Interactions.MediaAnnouncementFields.thumbnail_url/3)
    end
  end

  connection node_type: :media_update do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      arg(:hub, :string)
      resolve(&Interactions.TotalMediaUpdates.resolve/3)
    end

    edge do
      field(:media_update, non_null(:media_update))
    end
  end

  enum(:media_update_attachment_type,
    values: Gaia.Interactions.MediaUpdateAttachment.get_types()
  )

  enum(:media_update_type, values: Gaia.Interactions.MediaUpdate.get_types())

  object :media_update_attachment do
    field(:id, non_null(:id))
    field(:url, non_null(:string))
    field(:thumbnail_url, :string)
    field(:title, :string)
    field(:description, :string)
    field(:type, :media_update_attachment_type)
    field(:order_id, non_null(:integer))

    field(:thumbnail, :string) do
      resolve(&Interactions.MediaUpdateAttachmentFields.thumbnail/3)
    end

    field(:thumbnail_is_portrait, :boolean)
  end

  object :media_update_content do
    field(:content, :string)
  end

  object :media_update do
    field(:id, non_null(:id))
    field(:content_draft, :map)
    field(:content_published, :map)
    field(:media_id, non_null(:id))
    field(:title, non_null(:string))
    field(:slug, non_null(:string))
    field(:included_types, list_of(non_null(:media_update_type)))
    field(:is_pinned, :boolean)
    field(:is_draft, non_null(:boolean))
    field(:preview_secret, :string)
    field(:posted_at, :iso_naive_datetime)

    field(:thumbnail_attachment, :media_update_attachment) do
      resolve(&Interactions.MediaUpdateFields.thumbnail_attachment/3)
    end

    field(:attachments, non_null(list_of(non_null(:media_update_attachment))), do: resolve(dataloader(Gaia.Repo)))

    field(:content, :media_update_content, do: resolve(dataloader(Gaia.Repo)))

    field(:likes, non_null(:integer)) do
      resolve(&Interactions.MediaUpdateFields.likes/3)
    end

    field(:question_count, non_null(:integer)) do
      resolve(&Interactions.MediaUpdateFields.question_count/3)
    end

    field(:answered_question_count, non_null(:integer)) do
      resolve(&Interactions.MediaUpdateFields.answered_question_count/3)
    end

    field(:total_view_count, non_null(:integer)) do
      resolve(&Interactions.MediaUpdateFields.total_view_count/3)
    end
  end

  connection node_type: :media_comment do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&Interactions.TotalInvestorMediaComments.resolve/3)
    end

    edge do
      field(:media_comment, non_null(:media_comment))
    end
  end

  object :media_comment do
    field(:id, non_null(:id))
    field(:inserted_at, non_null(:iso_naive_datetime))
    field(:updated_at, non_null(:iso_naive_datetime))

    field(:annotation_metadata, :media_comment_annotation_metadata)
    field(:children, list_of(non_null(:media_comment)))
    field(:comment_source, :comment_source)
    field(:comment_source_url, :string)
    field(:content, non_null(:string))
    field(:likes, non_null(:integer))
    field(:private, non_null(:boolean))
    field(:company_author, :company_user, do: resolve(dataloader(Gaia.Repo)))
    field(:investor_user, :investor_user_simple, do: resolve(dataloader(Gaia.Repo)))
    field(:parent, :media_comment, do: resolve(dataloader(Gaia.Repo)))
    field(:media, :media, do: resolve(dataloader(Gaia.Repo)))
    field(:use_company_as_username, non_null(:boolean))

    field(:followed, non_null(:boolean)) do
      resolve(&Interactions.MediaCommentFields.followed/3)
    end

    field(:liked, non_null(:boolean)) do
      resolve(&Interactions.MediaCommentFields.liked/3)
    end

    field(:media_announcement, :media_announcement) do
      resolve(&Interactions.MediaCommentFields.media_announcement/3)
    end
  end

  object :media_comment_annotation_metadata do
    field(:left, non_null(:float))
    field(:page_index, non_null(:integer))
    field(:top, non_null(:float))
  end

  object :media_comment_like do
    field(:id, non_null(:id))

    field(:like, non_null(:boolean))

    field(:investor_user, :investor_user_simple, do: resolve(dataloader(Gaia.Repo)))

    field(:comment, :media_comment, do: resolve(dataloader(Gaia.Repo)))
  end

  object :media_like do
    field(:id, non_null(:id))

    field(:like, non_null(:boolean))
  end

  object :media_survey_answer do
    field(:id, non_null(:id))

    field(:answer, non_null(:media_survey_answer_type))
    field(:question, non_null(:media_survey_question_type))
  end

  object :media_survey_result do
    field(:individual_answers, list_of(:media_survey_answer_count))
    field(:investor_answer, :media_survey_answer_type)
    field(:question, non_null(:media_survey_question_type))
    field(:total_responses, non_null(:integer))
  end

  object :media_survey_answer_count do
    field(:answer, non_null(:media_survey_answer_type))
    field(:count, non_null(:integer))
  end

  object :iso_naive_datetime_range do
    field(:oldest, :iso_naive_datetime)
    field(:newest, :iso_naive_datetime)
  end

  object :prepared_announcement do
    field(:media_announcement, :media_announcement,
      do: resolve(&HermesWeb.Resolvers.Interactions.PreparedAnnouncementFields.media_announcement/3)
    )
  end

  object :interactions_mutations do
    field(:answer_media_survey, :media_survey_answer) do
      arg(:answer, non_null(:media_survey_answer_type))
      arg(:media_id, non_null(:id))
      arg(:question, non_null(:media_survey_question_type))
      middleware(Middleware.Authentication)
      resolve(&Interactions.AnswerMediaSurvey.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_survey_answered"})
      middleware(Middleware.Tracker)
    end

    field :create_media_comment, :media_comment do
      arg(:annotation_metadata, :media_comment_annotation_metadata_input)
      arg(:content, non_null(:string))
      arg(:media_id, non_null(:id))
      arg(:hub, :string)
      middleware(Middleware.Authentication)
      resolve(&Interactions.CreateMediaComment.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_created"})
      middleware(Middleware.Tracker)
    end

    field :invalidate_media_comment, :media_comment do
      arg(:media_comment_id, non_null(:id))
      middleware(Middleware.Authentication)
      resolve(&Interactions.InvalidateMediaComment.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_invalidated"})
      middleware(Middleware.Tracker)
    end

    field :react_to_media, :media_like do
      arg(:like, non_null(:boolean))
      arg(:media_id, non_null(:id))
      middleware(Middleware.Authentication)
      resolve(&Interactions.ReactToMedia.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_reacted"})
      middleware(Middleware.Tracker)
    end

    field :react_to_media_comment, :media_comment_like do
      arg(:like, non_null(:boolean))
      arg(:media_comment_id, non_null(:id))
      middleware(Middleware.Authentication)
      resolve(&Interactions.ReactToMediaComment.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_reacted"})
      middleware(Middleware.Tracker)
    end

    field :update_media_comment, :media_comment do
      arg(:content, non_null(:string))
      arg(:media_comment_id, non_null(:id))
      middleware(Middleware.Authentication)
      resolve(&Interactions.UpdateMediaComment.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comment_updated"})
      middleware(Middleware.Tracker)
    end
  end

  object :interactions_queries do
    connection field(:media_announcements, node_type: :media_announcement) do
      arg(:options, :options_input)
      arg(:hub, :string)
      resolve(&Interactions.MediaAnnouncements.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_announcements_fetched"})
    end

    field :homepage_announcements_and_updates, :homepage_announcements_and_updates do
      arg(:hub, :string)
      resolve(&Interactions.HomepageAnnouncementsAndUpdates.resolve/3)
      middleware(Middleware.Analytics, %{event: "homepage_announcements_and_updates_fetched"})
    end

    field :discover_block_announcements_and_updates, :discover_block_announcements_and_updates do
      arg(:hub, :string)
      resolve(&Interactions.DiscoverBlockAnnouncementsAndUpdates.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "discover_block_announcements_and_updates_fetched"
      })
    end

    field :media_announcement, :media_announcement do
      arg(:id, non_null(:id))
      arg(:current_company_profile_id, :id)
      resolve(&Interactions.MediaAnnouncement.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_announcement_fetched"})
    end

    field :prepared_announcement, :prepared_announcement do
      arg(:hashid, non_null(:string))
      arg(:host, non_null(:string))
      resolve(&Interactions.PreparedAnnouncement.resolve/3)
      middleware(Middleware.Analytics, %{event: "prepared_announcement_fetched"})
    end

    field(:media_comments, non_null(list_of(non_null(:media_comment)))) do
      arg(:is_annotation, :boolean)
      arg(:media_id, non_null(:id))
      arg(:hub, non_null(:string))
      resolve(&Interactions.MediaComments.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_comments_fetched"})
    end

    field :media_survey_results, non_null(list_of(non_null(:media_survey_result))) do
      arg(:media_id, non_null(:id))
      resolve(&Interactions.MediaSurveyResults.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_survey_results_fetched"})
    end

    field :media_announcement_date_range, :iso_naive_datetime_range do
      arg(:ticker, non_null(:string))

      resolve(&Interactions.MediaAnnouncementDateRange.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_announcement_date_range_fetched"})
    end

    @desc "Get media update for company"
    field :media_update_by_slug, :media_update do
      arg(:hub, non_null(:string))
      arg(:slug, non_null(:string))
      arg(:preview_secret, :string)
      resolve(&Interactions.MediaUpdateBySlug.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_update_by_slug_fetched"})
    end

    @desc "Get paginated media posts"
    connection field(:media_updates, node_type: :media_update) do
      arg(:hub, non_null(:string))
      arg(:options, :options_input)
      resolve(&Interactions.MediaUpdatesCursor.cursor/3)
      middleware(Middleware.Analytics, %{event: "media_updates_fetched"})
    end

    @desc "Gets the oldest media post date"
    field(:oldest_media_update_date, :iso_naive_datetime) do
      arg(:hub, non_null(:string))
      resolve(&Interactions.OldestMediaUpdateDate.resolve/3)
      middleware(Middleware.Analytics, %{event: "oldest_media_updates_date_fetched"})
    end

    @desc "Gets the range of media update post dates"
    field(:media_update_date_range, :iso_naive_datetime_range) do
      arg(:hub, non_null(:string))
      resolve(&Interactions.MediaUpdateDateRange.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_updates_date_range_fetched"})
    end

    @desc "Get is media liked"
    field(:media_like, non_null(:boolean)) do
      arg(:media_id, non_null(:id))
      resolve(&Interactions.MediaLike.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_like_fetched"})
    end

    @desc "Get total media likes"
    field(:total_media_likes, non_null(:integer)) do
      arg(:media_id, non_null(:id))
      arg(:hub, :string)
      resolve(&Interactions.TotalMediaLikes.resolve/3)
      middleware(Middleware.Analytics, %{event: "total_media_likes_fetched"})
    end

    @desc "Get featured media announcements"
    field(:total_featured_announcements, non_null(:integer)) do
      arg(:hub, :string)
      resolve(&Interactions.TotalFeaturedAnnouncements.resolve/3)
      middleware(Middleware.Analytics, %{event: "total_featured_announcements_fetched"})
    end

    @desc "Get is media followed"
    field(:media_follow, non_null(:boolean)) do
      arg(:media_id, non_null(:id))
      resolve(&Interactions.MediaFollow.resolve/3)
      middleware(Middleware.Analytics, %{event: "media_follow_fetched"})
    end

    @desc "Get paginated investor media comments"
    connection field(:investor_media_comments, node_type: :media_comment) do
      arg(:hub, non_null(:string))
      arg(:options, :options_input)
      resolve(&Interactions.InvestorMediaComments.resolve/3)
      middleware(Middleware.Analytics, %{event: "investor_media_comments_fetched"})
    end
  end

  object :ai_retrieved_announcement do
    field(:id, non_null(:id))
    field(:title, non_null(:string))
    field(:quotes, non_null(list_of(non_null(:string))))
    field(:url, non_null(:string))
    field(:posted_at, non_null(:iso_naive_datetime))
    field(:media_announcement_id, non_null(:id))
    field(:media_id, non_null(:id))
    field(:summary, :boolean)
    field(:news_publisher, :string)
    field(:video_url, :string)
    field(:social_video_url, :string)
    field(:featured_on_hub, :boolean)
    field(:market_sensitive, :boolean)
    field(:avg_distance, non_null(:float))
    field(:total_comments, non_null(:integer))
    field(:total_likes, non_null(:integer))
  end

  object :ai_retrieved_announcement_results do
    field(:documents, list_of(:ai_retrieved_announcement))
    field(:filter_ann_types, list_of(:string))
    field(:filter_start_date, :date)
    field(:filter_end_date, :date)
  end

  object :ai_announcement_search_queries do
    @desc "Use AI smart search to find announcements"
    field :ai_retrieved_announcements, :ai_retrieved_announcement_results do
      arg(:question, non_null(:string))
      arg(:ann_types, list_of(:string))
      arg(:start_date, :date)
      arg(:end_date, :date)
      arg(:filter_price_sensitive, :boolean)
      arg(:filter_video, :boolean)

      resolve(&HermesWeb.Resolvers.Interactions.AiAnnouncementSearch.resolve/3)
      middleware(Middleware.Analytics, %{event: "ai_announcement_search_fetched"})
    end
  end
end
