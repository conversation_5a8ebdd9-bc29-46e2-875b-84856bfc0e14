## This file is a PO Template file.
##
## "msgid"s here are often extracted from source code.
## Add new translations manually only if they're dynamic
## translations that can't be statically extracted.
##
## Run "mix gettext.extract" to bring this file up to
## date. Leave "msgstr"s empty as changing them here has no
## effect: edit them in PO (.po) files instead.
msgid ""
msgstr ""

#: lib/hermes_web/resolvers/comms/mutations/upsert_notification_preference.ex:61
#: lib/hermes_web/resolvers/comms/mutations/upsert_notification_preference_eoi.ex:73
#: lib/hermes_web/resolvers/investors/mutations/self_nominate_as_hnw.ex:100
#: lib/hermes_web/resolvers/investors/mutations/self_nominate_as_shareholder_or_hnw.ex:88
#, elixir-autogen, elixir-format
msgid "Could not create notification preference in database"
msgstr ""

#: lib/hermes_web/resolvers/comms/mutations/upsert_notification_preference.ex:75
#: lib/hermes_web/resolvers/comms/mutations/upsert_notification_preference_eoi.ex:87
#: lib/hermes_web/resolvers/investors/mutations/self_nominate_as_hnw.ex:115
#: lib/hermes_web/resolvers/investors/mutations/self_nominate_as_shareholder_or_hnw.ex:102
#, elixir-autogen, elixir-format
msgid "Could not update notification preference in database"
msgstr ""

#: lib/hermes_web/resolvers/comms/mutations/upsert_notification_preference.ex:26
#: lib/hermes_web/resolvers/comms/mutations/upsert_notification_preference_eoi.ex:32
#, elixir-autogen, elixir-format
msgid "Invalid notification preference scope"
msgstr ""

#: lib/hermes_web/resolvers/comms/mutations/upsert_notification_preference.ex:31
#: lib/hermes_web/resolvers/comms/mutations/upsert_notification_preference_eoi.ex:37
#, elixir-autogen, elixir-format
msgid "Oops! Something went wrong."
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/resend_confirm_user.ex:32
#, elixir-autogen, elixir-format
msgid "Could not resend confirmation email."
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/resend_confirm_user.ex:26
#, elixir-autogen, elixir-format
msgid "Your email has been confirmed!"
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/verify_user_email_by_token.ex:46
#, elixir-autogen, elixir-format
msgid "Your verification is wrong, %{times_left} left!"
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/verify_user_email_by_token.ex:34
#: lib/hermes_web/resolvers/investors/mutations/verify_user_email_by_token.ex:54
#, elixir-autogen, elixir-format
msgid "Oops, something went wrong!"
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/resend_confirm_user.ex:37
#, elixir-autogen, elixir-format
msgid "You are unauthorised."
msgstr ""

#: lib/hermes_web/resolvers/tracking/mutations/create_tracking_event.ex:73
#: lib/hermes_web/resolvers/tracking/mutations/create_tracking_event.ex:73
#: lib/hermes_web/resolvers/tracking/mutations/create_tracking_event.ex:102
#: lib/hermes_web/resolvers/tracking/mutations/create_tracking_event.ex:102
#, elixir-autogen, elixir-format
msgid "Failed to create tracking event"
msgstr ""

#: lib/hermes_web/controllers/block_cloud_ip.ex:81
#, elixir-autogen, elixir-format
msgid "Access denied!"
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/verify_user_email_by_token.ex:10
#, elixir-autogen, elixir-format
msgid "You have tried too many times. Please resend the email."
msgstr ""

#: lib/hermes_web/resolvers/webinars/mutations/download_webinar_document.ex:19
#: lib/hermes_web/resolvers/webinars/mutations/download_webinar_document.ex:19
#, elixir-autogen, elixir-format
msgid "Document not found"
msgstr ""

#: lib/hermes_web/resolvers/webinars/mutations/ask_question.ex:29
#: lib/hermes_web/resolvers/webinars/mutations/ask_question.ex:29
#, elixir-autogen, elixir-format
msgid "Failed to ask question: %{reason}"
msgstr ""

#: lib/hermes_web/resolvers/webinars/mutations/delete_question.ex:17
#: lib/hermes_web/resolvers/webinars/mutations/delete_question.ex:17
#, elixir-autogen, elixir-format
msgid "Failed to delete question: %{reason}"
msgstr ""

#: lib/hermes_web/resolvers/webinars/mutations/edit_question.ex:17
#: lib/hermes_web/resolvers/webinars/mutations/edit_question.ex:17
#, elixir-autogen, elixir-format
msgid "Failed to edit question: %{reason}"
msgstr ""

#: lib/hermes_web/resolvers/webinars/mutations/download_webinar_document.ex:26
#: lib/hermes_web/resolvers/webinars/mutations/download_webinar_document.ex:26
#, elixir-autogen, elixir-format
msgid "Failed to record document download"
msgstr ""

#: lib/hermes_web/resolvers/webinars/mutations/register_for_webinar.ex:15
#, elixir-autogen, elixir-format
msgid "Failed to register for webinar"
msgstr ""

#: lib/hermes_web/resolvers/webinars/queries/get_webinar_data.ex:18
#, elixir-autogen, elixir-format
msgid "Webinar not found"
msgstr ""

#: lib/hermes_web/resolvers/webinars/mutations/view_recording.ex:16
#, elixir-autogen, elixir-format
msgid "Failed to view recording"
msgstr ""
