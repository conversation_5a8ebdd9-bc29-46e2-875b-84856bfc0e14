## This file is a PO Template file.
##
## "msgid"s here are often extracted from source code.
## Add new translations manually only if they're dynamic
## translations that can't be statically extracted.
##
## Run "mix gettext.extract" to bring this file up to
## date. Leave "msgstr"s empty as changing them here has no
## effect: edit them in PO (.po) files instead.
msgid ""
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/update_user.ex:14
#, elixir-autogen, elixir-format
msgctxt "Update investor user mutation resolver"
msgid "Could not find user to update."
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/update_user.ex:20
#, elixir-autogen, elixir-format
msgctxt "Update investor user mutation resolver"
msgid "Could not update user."
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/update_user.ex:26
#, elixir-autogen, elixir-format
msgctxt "Update investor user mutation resolver"
msgid "Missing input. Could not update user."
msgstr ""

#: lib/hermes_web/middleware/authentication.ex:20
#, elixir-autogen, elixir-format
msgctxt "Hermes authentication middleware"
msgid "Could not get current investor user"
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/set_username.ex:18
#, elixir-autogen, elixir-format
msgctxt "Hermes set username"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/set_username.ex:24
#, elixir-autogen, elixir-format
msgctxt "Hermes set username"
msgid "Username can only be set once."
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/verify_holding.ex:53
#: lib/hermes_web/resolvers/investors/mutations/verify_holding.ex:65
#: lib/hermes_web/resolvers/investors/mutations/verify_holding.ex:98
#, elixir-autogen, elixir-format
msgctxt "Investor verify holding"
msgid "Could not verify holdings"
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/upload_certificate.ex:44
#, elixir-autogen, elixir-format
msgctxt "Upload certificate mutation resolver"
msgid "Could not invalidate existing certificate"
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/upload_certificate.ex:55
#, elixir-autogen, elixir-format
msgctxt "Upload certificate mutation resolver"
msgid "Could not upload certificate"
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/upload_certificate.ex:74
#, elixir-autogen, elixir-format
msgctxt "Upload certificate mutation resolver"
msgid "Could not upload new certificate"
msgstr ""

#: lib/hermes_web/resolvers/interactions/mutations/answer_media_survey.ex:33
#, elixir-autogen, elixir-format
msgctxt "Answer media survey mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/hermes_web/resolvers/interactions/mutations/create_media_comment.ex:54
#: lib/hermes_web/resolvers/interactions/mutations/create_media_comment.ex:92
#, elixir-autogen, elixir-format
msgctxt "Create media comment mutation"
msgid "Could not create media comment"
msgstr ""

#: lib/hermes_web/resolvers/interactions/mutations/invalidate_media_comment.ex:22
#, elixir-autogen, elixir-format
msgctxt "Invalidate media comment mutation"
msgid "Media comment does not exist."
msgstr ""

#: lib/hermes_web/resolvers/interactions/mutations/invalidate_media_comment.ex:41
#, elixir-autogen, elixir-format
msgctxt "Invalidate media comment mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/hermes_web/resolvers/interactions/mutations/invalidate_media_comment.ex:30
#, elixir-autogen, elixir-format
msgctxt "Invalidate media comment mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/hermes_web/resolvers/interactions/mutations/react_to_media_comment.ex:39
#, elixir-autogen, elixir-format
msgctxt "React to media comment mutation"
msgid "Cannot react to media comment"
msgstr ""

#: lib/hermes_web/resolvers/interactions/mutations/react_to_media.ex:31
#, elixir-autogen, elixir-format
msgctxt "React to media mutation"
msgid "Cannot react to media"
msgstr ""

#: lib/hermes_web/resolvers/interactions/mutations/update_media_comment.ex:25
#, elixir-autogen, elixir-format
msgctxt "Update media comment mutation"
msgid "Media comment does not exist."
msgstr ""

#: lib/hermes_web/resolvers/interactions/mutations/update_media_comment.ex:52
#, elixir-autogen, elixir-format
msgctxt "Update media comment mutation"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/hermes_web/resolvers/interactions/mutations/update_media_comment.ex:41
#, elixir-autogen, elixir-format
msgctxt "Update media comment mutation"
msgid "Public comment cannot be updated."
msgstr ""

#: lib/hermes_web/resolvers/interactions/mutations/update_media_comment.ex:33
#, elixir-autogen, elixir-format
msgctxt "Update media comment mutation"
msgid "You are unauthorised."
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/self_nominate_as_hnw.ex:29
#, elixir-autogen, elixir-format
msgctxt "Self nominate as HNW"
msgid "Unable to nominate as sophisticated or professional"
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/self_nominate_as_hnw.ex:54
#, elixir-autogen, elixir-format
msgctxt "Self nominate as HNW"
msgid "Unable to remove sophisticated or professional nomination"
msgstr ""

#: lib/hermes_web/resolvers/interactions/queries/media_comments.ex:32
#: lib/hermes_web/resolvers/interactions/queries/media_comments.ex:60
#, elixir-autogen, elixir-format
msgctxt "Media comments query"
msgid "Unable to get media comments."
msgstr ""

#: lib/hermes_web/resolvers/interactions/queries/media_updates_cursor.ex:36
#, elixir-autogen, elixir-format
msgctxt "Media updates list query"
msgid "Unable to get media updates list"
msgstr ""

#: lib/hermes_web/resolvers/comms/mutations/subscribe_contact.ex:32
#: lib/hermes_web/resolvers/comms/mutations/subscribe_contact.ex:55
#: lib/hermes_web/resolvers/comms/mutations/subscribe_contact.ex:69
#, elixir-autogen, elixir-format
msgctxt "Hermes - SubscribeContact mutation"
msgid "Unfortunately we could not update the notification settings at this time, please try again later or contact us for support."
msgstr ""

#: lib/hermes_web/resolvers/comms/mutations/subscribe_contact_globally.ex:32
#: lib/hermes_web/resolvers/comms/mutations/subscribe_contact_globally.ex:55
#: lib/hermes_web/resolvers/comms/mutations/subscribe_contact_globally.ex:69
#, elixir-autogen, elixir-format
msgctxt "Hermes - SubscribeContactGlobally mutation"
msgid "Unfortunately we could not update the notification settings at this time, please try again later or contact us for support."
msgstr ""

#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact.ex:31
#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact.ex:31
#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact.ex:38
#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact.ex:38
#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact.ex:56
#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact.ex:56
#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact.ex:65
#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact.ex:65
#, elixir-autogen, elixir-format
msgctxt "Hermes - UnsubscribeContact mutation"
msgid "Unfortunately we could not update the notification settings at this time, please try again later or contact us for support."
msgstr ""

#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact_globally.ex:35
#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact_globally.ex:35
#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact_globally.ex:58
#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact_globally.ex:58
#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact_globally.ex:72
#: lib/hermes_web/resolvers/comms/mutations/unsubscribe_contact_globally.ex:72
#, elixir-autogen, elixir-format
msgctxt "Hermes - UnsubscribeContactGlobally mutation"
msgid "Unfortunately we could not update the notification settings at this time, please try again later or contact us for support."
msgstr ""

#: lib/hermes_web/resolvers/interactions/queries/homepage_announcements_and_updates.ex:29
#, elixir-autogen, elixir-format
msgctxt "Homepage Announcements and Updates query"
msgid "Unable to get homepage announcements and updates."
msgstr ""

#: lib/hermes_web/resolvers/investors/queries/user_comments.ex:26
#: lib/hermes_web/resolvers/investors/queries/user_comments.ex:50
#, elixir-autogen, elixir-format
msgctxt "User comments query"
msgid "Unable to get user comments."
msgstr ""

#: lib/hermes_web/resolvers/investors/queries/user_likes.ex:26
#, elixir-autogen, elixir-format
msgctxt "User likes query"
msgid "Unable to get user likes."
msgstr ""

#: lib/hermes_web/resolvers/investors/queries/user_likes.ex:39
#, elixir-autogen, elixir-format
msgctxt "User likes query"
msgid "Unauthorised to get user likes."
msgstr ""

#: lib/hermes_web/resolvers/investors/queries/user_surveys.ex:26
#, elixir-autogen, elixir-format
msgctxt "User surveys query"
msgid "Unable to get user surveys."
msgstr ""

#: lib/hermes_web/resolvers/investors/queries/user_surveys.ex:39
#, elixir-autogen, elixir-format
msgctxt "User surveys query"
msgid "Unauthorised to get user surveys."
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/delete_certificate.ex:26
#: lib/hermes_web/resolvers/investors/mutations/delete_certificate.ex:37
#, elixir-autogen, elixir-format
msgctxt "Delete certificate mutation resolver"
msgid "Could not delete certificate"
msgstr ""

#: lib/hermes_web/resolvers/interactions/queries/discover_block_announcements_and_updates.ex:29
#, elixir-autogen, elixir-format
msgctxt "Discover block Announcements and Updates query"
msgid "Unable to get discover section announcements and updates."
msgstr ""

#: lib/hermes_web/controllers/contact_subscriptions_controller.ex:108
#: lib/hermes_web/controllers/contact_subscriptions_controller.ex:108
#, elixir-autogen, elixir-format
msgctxt "Hermes - ContactSubscriptionsController"
msgid "Unfortunately we could not unsubscribe you at this time, please try again later or contact us for support."
msgstr ""

#: lib/hermes_web/resolvers/hubs/mutations/mark_notification_as_read.ex:33
#: lib/hermes_web/resolvers/hubs/mutations/mark_notification_as_read.ex:33
#, elixir-autogen, elixir-format
msgctxt "Mark notification as read"
msgid "Could not upsert hub follow"
msgstr ""

#: lib/hermes_web/resolvers/hubs/mutations/mark_notification_as_read.ex:27
#: lib/hermes_web/resolvers/hubs/mutations/mark_notification_as_read.ex:27
#, elixir-autogen, elixir-format
msgctxt "Mark notification as read"
msgid "You are unauthorised."
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/self_nominate_as_shareholder_or_hnw.ex:45
#, elixir-autogen, elixir-format
msgctxt "Self nominate as shareholder or HNW"
msgid "Unable to nominate as shareholder or sophisticated or professional investor"
msgstr ""

#: lib/hermes_web/resolvers/hubs/mutations/upsert_follow.ex:29
#: lib/hermes_web/resolvers/hubs/mutations/upsert_follow.ex:29
#, elixir-autogen, elixir-format
msgctxt "Upsert hub follow"
msgid "Could not upsert hub follow"
msgstr ""

#: lib/hermes_web/resolvers/interactions/queries/get_utm_link_by_hash.ex:26
#, elixir-autogen, elixir-format
msgctxt "UtmLinkByHash query resolver"
msgid "Oops! Something went wrong."
msgstr ""

#: lib/hermes_web/resolvers/webinars/queries/list_webinars.ex:27
#, elixir-autogen, elixir-format
msgctxt "List webinars query"
msgid "Unable to get webinars."
msgstr ""

#: lib/hermes_web/resolvers/webinars/mutations/create_or_update_webinar_view.ex:45
#: lib/hermes_web/resolvers/webinars/mutations/create_or_update_webinar_view.ex:75
#, elixir-autogen, elixir-format
msgctxt "CreateOrUpdateWebinarView mutation resolver"
msgid "Could not create or update webinar view"
msgstr ""

#: lib/hermes_web/resolvers/webinars/mutations/create_or_update_webinar_view.ex:37
#, elixir-autogen, elixir-format
msgctxt "CreateOrUpdateWebinarView mutation resolver"
msgid "Unauthorized to access this attendee record"
msgstr ""

#: lib/hermes_web/resolvers/webinars/mutations/create_or_update_webinar_view.ex:30
#: lib/hermes_web/resolvers/webinars/mutations/create_or_update_webinar_view.ex:67
#, elixir-autogen, elixir-format
msgctxt "CreateOrUpdateWebinarView mutation resolver"
msgid "Unauthorized to create view for this webinar"
msgstr ""

#: lib/hermes_web/resolvers/webinars/mutations/create_or_update_webinar_view.ex:23
#: lib/hermes_web/resolvers/webinars/mutations/create_or_update_webinar_view.ex:60
#, elixir-autogen, elixir-format
msgctxt "CreateOrUpdateWebinarView mutation resolver"
msgid "Webinar not found"
msgstr ""

#: lib/hermes_web/resolvers/interactions/queries/investor_media_comments.ex:40
#, elixir-autogen, elixir-format
msgctxt "Investor media comments query"
msgid "Unable to get investor media comments"
msgstr ""

#: lib/hermes_web/resolvers/investors/mutations/verify_holding.ex:130
#: lib/hermes_web/resolvers/investors/mutations/verify_holding.ex:142
#: lib/hermes_web/resolvers/investors/mutations/verify_holding.ex:175
#, elixir-autogen, elixir-format
msgctxt "Investor verify holding"
msgid "Could not add your holdings"
msgstr ""
