defmodule Googleapi.MixProject do
  use Mix.Project

  def project do
    [
      app: :googleapi,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.18",
      start_permanent: Mix.env() == :prod,
      deps: deps()
    ]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      extra_applications: [:logger]
    ]
  end

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:httpoison, "~> 1.8"},
      {:goth, "~> 1.0"},
      {:google_api_drive, "~> 0.25"},
      {:google_api_iam_credentials, "~> 0.12"},
      {:google_api_language, "~> 0.16.0"},
      {:google_api_sheets, "~> 0.29"},
      {:google_api_slides, "~> 0.20.0"},
      {:google_api_storage, "~> 0.46.1"},
      {:google_api_translate, "~> 0.15.0"},
      {:mox, "~> 1.0", only: :test}
    ]
  end
end
