## `msgid`s in this file come from POT (.pot) files.
##
## Do not add, change, or remove `msgid`s manually here as
## they're tied to the ones in the corresponding POT file
## (with the same domain).
##
## Use `mix gettext.extract --merge` or `mix gettext.merge`
## to merge POT files into PO files.
msgid ""
msgstr ""
"Language: en\n"

#: lib/public_api_web/controllers/contact_subscriptions_controller.ex:54
#: lib/public_api_web/controllers/contact_subscriptions_controller.ex:54
#, elixir-autogen, elixir-format
msgctxt "PublicApi - ContactSubscriptionsController"
msgid "Unfortunately we could not subscribe you at this time, please try again later or contact us for support."
msgstr ""

#: lib/public_api_web/controllers/licat_controller.ex:32
#, elixir-autogen, elixir-format
msgctxt "PublicApi - LicatController"
msgid "Unfortunately we could not get market data at this time, please try again later or contact us for support."
msgstr ""

#: lib/public_api_web/controllers/licat_controller.ex:68
#, elixir-autogen, elixir-format, fuzzy
msgctxt "PublicApi - LicatController"
msgid "Unfortunately we could not add the ticker at this time, please try again later or contact us for support."
msgstr ""
