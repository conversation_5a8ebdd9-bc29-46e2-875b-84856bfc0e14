defmodule EmailMarketing.MixProject do
  use Mix.Project

  def project do
    [
      app: :email_marketing,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.18",
      start_permanent: Mix.env() == :prod,
      deps: deps(),
      aliases: aliases()
    ]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      extra_applications: [:logger]
    ]
  end

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:httpoison, "~> 1.8"},
      {:poison, "~> 3.1"},
      {:premailex, "~> 0.3.0"},
      {:swoosh, "~> 1.6"},
      {:gen_smtp, "~> 1.0"},
      {:hackney, "~> 1.24"}
    ]
  end

  defp aliases do
    [
      setup: ["deps.get"]
    ]
  end
end
