defmodule Link.MixProject do
  use Mix.Project

  def project do
    [
      app: :link,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.18",
      start_permanent: Mix.env() == :prod,
      deps: deps()
    ]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      extra_applications: [:logger]
    ]
  end

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:arc_gcs, "~> 0.2"},
      {:arc, "~> 0.11.0"},
      {:csv, "~> 3.0"},
      {:goth, "~> 1.0"},
      {:helper, in_umbrella: true},
      {:jason, "~> 1.2"},
      {:sentry, "~> 8.0"},
      {:sftp_client, "~> 1.4"},
      {:timex, "~> 3.7"}
    ]
  end
end
