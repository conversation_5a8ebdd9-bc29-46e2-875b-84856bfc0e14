defmodule Neville.MixProject do
  use Mix.Project

  def project do
    [
      app: :neville,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.18",
      start_permanent: Mix.env() == :prod,
      deps: deps()
    ]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      extra_applications: [:logger]
    ]
  end

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:arc, "~> 0.11.0"},
      {:arc_gcs, "~> 0.2"},
      {:nimble_csv, "~> 1.1"},
      {:sftp_client, "~> 1.4"},
      {:timex, "~> 3.7.2"}
    ]
  end
end
