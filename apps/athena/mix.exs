defmodule Athena.MixProject do
  use Mix.Project

  def project do
    [
      app: :athena,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.18",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps()
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {Athena.Application, []},
      extra_applications: [:logger, :runtime_tools, :ueberauth_microsoft]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    [
      {:absinthe_phoenix, "~> 2.0.2"},
      {:absinthe_plug, "~> 1.5.8"},
      {:absinthe_relay, "~> 1.5.2"},
      {:absinthe, "~> 1.7.8"},
      {:absinthe_graphql_ws, "~> 0.3"},
      {:amazon_web_service, in_umbrella: true},
      {:analytics, in_umbrella: true},
      {:appsignal, "~> 2.15"},
      {:appsignal_phoenix, "~> 2.6.0"},
      {:appsignal_plug, "~> 2.0"},
      {:automic, in_umbrella: true},
      {:cloud_ip_fetcher, in_umbrella: true},
      {:cors_plug, "~> 3.0"},
      {:dataloader, "~> 1.0.10"},
      {:decimal, "~> 2.0"},
      {:email_marketing, in_umbrella: true},
      {:email_transactional, in_umbrella: true},
      {:gaia, in_umbrella: true},
      {:gcs_signed_url, "~> 0.4"},
      {:gettext, "~> 0.24"},
      {:goth, "~> 1.0"},
      {:hackney, "~> 1.24"},
      {:helper, in_umbrella: true},
      {:iptrie, "~> 0.8.0"},
      {:jason, "~> 1.2"},
      {:joken, "~> 2.6"},
      {:phoenix, "~> 1.7"},
      {:plug_cowboy, "~> 2.7"},
      {:refinitiv, in_umbrella: true},
      {:sentry, "~> 8.0"},
      {:scholar, "~> 0.2.1"},
      {:slugify, "~> 1.3"},
      {:telemetry_metrics, "~> 0.6"},
      {:telemetry_poller, "~> 1.0"},
      {:timex, "~> 3.7.2"},
      {:twilio, in_umbrella: true},
      {:vercel, in_umbrella: true},
      {:oauther, "~> 1.1"},
      {:ueberauth_google, "~> 0.10"},
      {:ueberauth_microsoft, "~> 0.23.0"},
      {:ueberauth, "~> 0.7"},
      {:xlsx_reader, "~> 0.4.0"},
      {:phoenix_html, "~> 4.0"}
    ]
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get"]
    ]
  end
end
