defmodule AthenaWeb.Resolvers.Contacts.ContactBeneficialOwnerHoldingsSummary do
  @moduledoc """
  ContactBeneficialOwnerHoldingsSummary Query Resolvers
  """

  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Contacts
  alias Gaia.Contacts.Contact

  def resolve(_, %{contact_id: contact_id, start_date: %Date{} = start_date, end_date: %Date{} = end_date}, %{
        context: %{current_company_profile_user: %ProfileUser{profile: %Profile{id: company_profile_id}}}
      }) do
    %{id: contact_id, company_profile_id: company_profile_id}
    |> Contacts.get_contact_by()
    |> case do
      %Contact{} = contact ->
        beneficial_owner_holdings =
          Gaia.BeneficialOwners.beneficial_owner_latest_holdings_by_contact(contact, start_date, end_date)

        {:ok,
         %{
           id: "#{contact_id}-#{start_date}-#{end_date}",
           beneficial_owner_holdings: beneficial_owner_holdings
         }}

      nil ->
        {:ok, nil}
    end
  end

  def resolve(_, _, _) do
    {:ok, nil}
  end
end
