defmodule AthenaWeb.Schema.BeneficialOwnersTypes do
  @moduledoc """
  Beneficial Owners GraphQL Schema
  """

  use AthenaWeb, :type
  use Absinthe.Relay.Schema.Notation, :classic

  import Absinthe.Resolution.Helpers, only: [dataloader: 1]

  alias AthenaWeb.Middleware
  alias AthenaWeb.Resolvers.BeneficialOwners

  connection node_type: :beneficial_owners_report do
    field(:options, :options)

    field :total, non_null(:integer) do
      arg(:options, :options_input)
      resolve(&BeneficialOwners.Reports.total/3)
    end
  end

  object :count_countries do
    field(:investment_manager_country, :string)
    field(:count, :integer)
  end

  object :count_states do
    field(:investment_manager_state, :string)
    field(:count, :integer)
  end

  object :beneficial_owners_overview do
    field(:previous_report, :beneficial_owners_report)
    field(:next_report, :beneficial_owners_report)
    field(:top_investors, non_null(list_of(non_null(:beneficial_owner_report_detail))))
    field(:top_movers, non_null(list_of(non_null(:beneficial_owner_report_detail))))
    field(:count_countries, list_of(non_null(:count_countries)))
    field(:count_states, list_of(non_null(:count_states)))
    field(:report_info, :latest_beneficial_owners_report_details)
  end

  object :beneficial_owners_overview_summary do
    field(:row_count, :integer)
    field(:beneficial_owner_holdings, :integer)
    field(:percentange_of_holdings, :float)
    field(:holdings_change, :integer)
    field(:absolute_change, :integer)
    field(:investment_manager, :integer)
    field(:registered_holder, :integer)
    field(:beneficial_owner, :integer)
    field(:has_past_holdings, :boolean)
  end

  object :beneficial_owners_top_investors do
    field(:top_investors, non_null(list_of(non_null(:beneficial_owner_report_detail))))

    field(
      :investors_grouped_by_holders,
      non_null(list_of(non_null(:beneficial_owner_report_detail)))
    )

    field(:top_summary, :beneficial_owners_overview_summary)
    field(:all_summary, :beneficial_owners_overview_summary)
  end

  object :beneficial_owners_report do
    field(:id, non_null(:id))
    field(:previous_report_date, :date)
    field(:report_date, :date)
    field(:inserted_at, :date)
    field(:type, non_null(:beneficial_owners_report_types))
    field(:stage, non_null(:beneficial_owners_report_stages))
    field(:is_user_uploaded, non_null(:boolean))
    field(:sync_at, :naive_datetime)
    field(:disclosed_interest_document_uploaded_at, :naive_datetime)
    field(:disclosed_interest_document_filename, :string)
    field(:disclosed_interest_document_url, :string)

    field(:metadata, :beneficial_owners_report_metadata, do: resolve(&BeneficialOwners.Report.metadata/3))

    field(
      :layer_one_participants,
      non_null(list_of(non_null(:beneficial_owner_report_participant))),
      do: resolve(dataloader(Gaia.Repo))
    )

    field(:contacts, non_null(list_of(non_null(:contact))), do: resolve(dataloader(Gaia.Repo)))
  end

  object :beneficial_owner_report_detail do
    field(:beneficial_owner_name, :string)
    field(:registered_holder_name, :string)
    field(:beneficial_owner_state, :string)
    field(:beneficial_owner_country, :string)
    field(:beneficial_owner_holdings, :integer)
    field(:investment_manager_name, :string)
    field(:investment_manager_city, :string)
    field(:investment_manager_country, :string)
    field(:investment_manager_state, :string)
    field(:percentange_of_holdings, :float)
    field(:movement_type, :string)
    field(:holdings_change, :integer)
    field(:absolute_change, :integer)
    field(:rank, :integer)
    field(:has_past_holdings, :boolean)
    # stats grouped by registered holder below
    field(:grouped_rank, :integer)
    field(:grouped_holdings_sum, :integer)
    field(:grouped_holdings_percentage, :float)
    field(:grouped_holdings_change, :integer)
  end

  object :latest_beneficial_owners_report_details do
    field(:report_id, non_null(:id))
    field(:previous_report_date, :date)
    field(:report_date, non_null(:date))
    field(:nominees_unmasked, non_null(:integer))
    field(:unique_registered_holder_names, non_null(:integer))
    field(:total_holdings, non_null(:integer))
    field(:type, non_null(:beneficial_owners_report_types))
    field(:unmasked_holdings, non_null(:integer))
    field(:disclosed_interest_document_uploaded_at, :naive_datetime)
    field(:disclosed_interest_document_filename, :string)
    field(:disclosed_interest_document_url, :string)
  end

  object :latest_completed_importing_and_processing_beneficial_owners_reports do
    field(:latest_completed_report, :latest_beneficial_owners_report_details)
    field(:has_current_processing_report, non_null(:boolean))
    field(:current_processing_report, :beneficial_owners_report)
    field(:has_current_importing_report, non_null(:boolean))
    field(:current_importing_report, :beneficial_owners_report)
  end

  object :beneficial_owner_account do
    field(:id, non_null(:id))
    field(:account_name, non_null(:string))
    field(:address_line_one, :string)
    field(:address_line_two, :string)
    field(:address_city, :string)

    field(:address_state, :string)
    field(:address_postcode, :string)
    field(:address_country, :string)
    field(:beneficial_owner_holdings, non_null(list_of(non_null(:beneficial_owner_holding))))
    field(:company_profile, non_null(:company_profile), do: resolve(dataloader(Gaia.Repo)))

    field(:contact, :contact, do: resolve(dataloader(Gaia.Repo)))

    field(:latest_holding, :beneficial_owner_holding,
      do: resolve(&BeneficialOwners.Queries.AccountsByHoldingIdsWithLatestHolding.batch_resolve_latest_holding/3)
    )
  end

  object :beneficial_owner_aggregate_holding do
    field(:id, non_null(:id))
    field(:date, non_null(:date_short))
    field(:balance, non_null(:integer))
    field(:account_name, non_null(:string))
  end

  object :beneficial_owner_holding do
    field(:id, non_null(:id))
    field(:shares, non_null(:integer))
    field(:parent, :beneficial_owner_holding, do: resolve(dataloader(Gaia.Repo)))
    field(:shareholding, :shareholding, do: resolve(dataloader(Gaia.Repo)))
    field(:report, :beneficial_owners_report, do: resolve(dataloader(Gaia.Repo)))
    field(:report_id, non_null(:id))
    field(:company_profile, non_null(:company_profile), do: resolve(dataloader(Gaia.Repo)))
    field(:updated_at, :naive_datetime)

    field(:beneficial_owner_account, :beneficial_owner_account, do: resolve(dataloader(Gaia.Repo)))

    field(:children, list_of(:beneficial_owner_holding), do: resolve(dataloader(Gaia.Repo)))
  end

  object :beneficial_owner_report_candidate do
    field(:id, non_null(:id))
    field(:account_name, non_null(:string))
    field(:shares, non_null(:integer))
    field(:layer, non_null(:integer))
    field(:type, non_null(:string))
    field(:status, non_null(:string))
    field(:address_line_one, :string)
    field(:address_line_two, :string)
    field(:address_city, :string)
    field(:address_state, :string)
    field(:address_postcode, :string)
    field(:address_country, :string)
    field(:parent_id, :id)
    field(:children, list_of(:beneficial_owner_report_candidate))
    field(:shareholding, :shareholding, do: resolve(dataloader(Gaia.Repo)))

    field(:last_report_candidate, :beneficial_owner_report_candidate, do: resolve(dataloader(Gaia.Repo)))
  end

  object :beneficial_owner_report_participant do
    field(:id, non_null(:id))
    field(:account_name, non_null(:string))
    field(:shares, non_null(:integer))
    field(:layer, non_null(:integer))
    field(:type, non_null(:string))
    field(:status, non_null(:string))
    field(:address_line_one, :string)
    field(:address_line_two, :string)
    field(:address_city, :string)
    field(:address_state, :string)
    field(:address_postcode, :string)
    field(:address_country, :string)
    field(:parent_id, :id)

    field(:children, list_of(:beneficial_owner_report_participant), do: resolve(dataloader(Gaia.Repo)))

    field(:shareholding, :shareholding, do: resolve(dataloader(Gaia.Repo)))
    field(:beneficial_owner_account, :beneficial_owner_account, do: resolve(dataloader(Gaia.Repo)))
    field(:contact, :contact, do: resolve(dataloader(Gaia.Repo)))

    field(:last_report_participant, :beneficial_owner_report_participant, do: resolve(dataloader(Gaia.Repo)))
  end

  enum(:beneficial_owners_report_types, values: Gaia.BeneficialOwners.Report.get_report_types())
  enum(:beneficial_owners_report_stages, values: Gaia.BeneficialOwners.Report.get_stage_types())

  object :beneficial_owners_report_metadata do
    field(:id, non_null(:id))
    field(:company_total_shares, :integer)
    field(:previous_report_id, :id)
    field(:previous_report_date, :date)
    field(:next_report_id, :id)
  end

  object :beneficial_owners_queries do
    field(:beneficial_owners_overview, :beneficial_owners_overview) do
      arg(:report_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&BeneficialOwners.Overview.resolve/3)
      middleware(Middleware.Analytics, %{event: "beneficial_owners_report_overview_fetched"})
    end

    field(:beneficial_owners_top_investors, :beneficial_owners_top_investors) do
      arg(:report_id, non_null(:id))
      arg(:query, :string)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&BeneficialOwners.TopInvestors.resolve/3)
      middleware(Middleware.Analytics, %{event: "beneficial_owners_report_top_investors_fetched"})
    end

    connection field(:beneficial_owners_reports, node_type: :beneficial_owners_report) do
      arg(:options, :options_input)
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&BeneficialOwners.Reports.resolve/3)
      middleware(Middleware.Analytics, %{event: "beneficial_owners_reports_fetched"})
    end

    field(
      :latest_completed_importing_and_processing_beneficial_owners_reports,
      :latest_completed_importing_and_processing_beneficial_owners_reports
    ) do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})

      resolve(&BeneficialOwners.LatestCompletedImportingAndProcessingBenficialOwnersReports.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "latest_completed_beneficial_owners_report_details_fetched"
      })
    end

    field(
      :beneficial_owner_accounts_by_holding_ids_with_latest_holding,
      list_of(non_null(:beneficial_owner_account))
    ) do
      arg(:holding_ids, non_null(list_of(non_null(:id))))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&BeneficialOwners.Queries.AccountsByHoldingIdsWithLatestHolding.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "beneficial_owners_accounts_with_latest_holding_fetched"
      })
    end

    field(
      :beneficial_owner_account,
      :beneficial_owner_account
    ) do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&BeneficialOwners.Queries.Account.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "beneficial_owners_account_fetched"
      })
    end

    field(:beneficial_owners_report, non_null(:beneficial_owners_report)) do
      arg(:report_id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&BeneficialOwners.Report.resolve/3)
      middleware(Middleware.Analytics, %{event: "beneficial_owners_report_fetched"})
    end
  end

  object :beneficial_owners_mutations do
    field :delete_beneficial_owners_report, :boolean do
      arg(:id, non_null(:id))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&BeneficialOwners.DeleteReport.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "beneficial_owners_report_deleted",
        hide_args: true
      })
    end

    field :notify_of_beneficial_owners_report_request, :boolean do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&BeneficialOwners.NotifyOfBeneficialOwnersReportRequest.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "beneficial_owners_report_requested",
        hide_args: true
      })
    end

    field :upload_beneficial_owners_report, :boolean do
      arg(:file, non_null(:upload))
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&BeneficialOwners.UploadBeneficialOwnersReport.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "user_uploaded_beneficial_owners_report",
        hide_args: true
      })
    end

    field :notify_of_beneficial_owners_report_interest, :boolean do
      middleware(Middleware.BlockCloudIP)
      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&BeneficialOwners.NotifyOfBeneficialOwnersReportInterest.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "beneficial_owners_report_interest_notified",
        hide_args: true
      })
    end

    field :generate_disclosed_interest_document_signed_url, non_null(:string) do
      arg(:report_id, non_null(:id))

      middleware(Middleware.BlockCloudIP)

      middleware(Middleware.Permission, %{permission: "registers_shareholdings.admin"})
      resolve(&BeneficialOwners.GenerateDisclosedInterestDocumentSignedUrl.resolve/3)

      middleware(Middleware.Analytics, %{
        event: "beneficial_owners_disclosed_interest_document_signed_url_generated"
      })
    end
  end
end
