defmodule Helper.MixProject do
  use Mix.Project

  def project do
    [
      app: :helper,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.18",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      deps: deps()
    ]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      extra_applications: [:logger]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:absinthe, "~> 1.7"},
      {:cachex, "~> 3.4.0"},
      {:countries, "~> 1.6"},
      {:domainatrex, "~> 3.0"},
      {:ecto, "~> 3.8"},
      {:appsignal, "~> 2.15"},
      {:google_api_secret_manager, "~> 0.19"},
      {:hackney, "~> 1.24"},
      {:httpoison, "~> 1.8"},
      {:jason, "~> 1.2"},
      {:poison, "~> 3.1"},
      {:hashids, "~> 2.0"},
      {:phoenix_html, "~> 4.0"},
      {:phoenix_live_view, "~> 1.0.3"},
      {:phoenix, "~> 1.7"},
      {:sentry, "~> 8.0"},
      {:tesla, "~> 1.4"},
      {:timex, "~> 3.7.2"},
      {:ecto_psql_extras, "~> 0.8"}
    ]
  end
end
