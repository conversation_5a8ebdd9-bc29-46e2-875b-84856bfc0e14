defmodule Refinitiv.MixProject do
  use Mix.Project

  def project do
    [
      app: :refinitiv,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.18",
      start_permanent: Mix.env() == :prod,
      deps: deps()
    ]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      mod: {Refinitiv.Application, []},
      extra_applications: [:logger]
    ]
  end

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:csv, "~> 3.0"},
      {:httpoison, "~> 1.8"},
      {:poison, "~> 3.1"},
      {:timex, "~> 3.7.2"},
      {:helper, in_umbrella: true}
    ]
  end
end
