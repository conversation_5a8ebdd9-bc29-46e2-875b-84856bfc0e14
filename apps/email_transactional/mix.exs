defmodule EmailTransactional.MixProject do
  use Mix.Project

  def project do
    [
      app: :email_transactional,
      version: "0.1.0",
      elixir: "~> 1.18",
      start_permanent: Mix.env() == :prod,
      deps: deps(),
      aliases: aliases(),
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock"
    ]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      mod: {EmailTransactional.Application, []},
      extra_applications: [:logger]
    ]
  end

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:phoenix, "~> 1.7"},
      {:phoenix_html, "~> 4.0"},
      {:phoenix_swoosh, "~> 1.0"},
      {:phoenix_live_reload, "~> 1.2", only: :dev},
      {:gen_smtp, "~> 1.0"},
      {:goth, "~> 1.0"},
      {:hackney, "~> 1.24"},
      {:helper, in_umbrella: true},
      {:number, "~> 1.0.1"},
      {:plug_cowboy, ">= 1.0.0"},
      {:premailex, "~> 0.3.0"},
      {:swoosh, "~> 1.6"},
      {:phoenix_html_helpers, "~> 1.0"},
      {:slugify, "~> 1.3"}
    ]
  end

  defp aliases do
    [
      setup: ["deps.get"]
    ]
  end
end
