defmodule Hades.MixProject do
  use Mix.Project

  def project do
    [
      app: :hades,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.18",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps()
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {Hades.Application, []},
      extra_applications: [:logger, :runtime_tools]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    [
      {:automic, in_umbrella: true},
      {:boardroom, in_umbrella: true},
      {:email_transactional, in_umbrella: true},
      {:esbuild, "~> 0.3", runtime: Mix.env() == :dev},
      {:floki, ">= 0.30.0"},
      {:gaia, in_umbrella: true},
      {:openai, in_umbrella: true},
      {:goth, "~> 1.0"},
      {:hackney, "~> 1.24"},
      {:helper, in_umbrella: true},
      {:jason, "~> 1.2"},
      {:phoenix_ecto, "~> 4.4"},
      {:phoenix_html, "~> 4.0"},
      {:phoenix_live_dashboard, "~> 0.8"},
      {:phoenix_live_reload, "~> 1.2", only: :dev},
      {:phoenix_live_view, "~> 1.0.3"},
      {:phoenix, "~> 1.7"},
      {:plug_cowboy, "~> 2.7"},
      {:refinitiv, in_umbrella: true},
      {:sentry, "~> 8.0"},
      {:telemetry_metrics, "~> 0.6"},
      {:telemetry_poller, "~> 1.0"},
      {:twilio, in_umbrella: true},
      {:ueberauth_google, "~> 0.10"},
      {:ueberauth, "~> 0.7"},
      {:oban_web, "~> 2.11.0"},
      {:timex, "~> 3.7.2"},
      {:petal_components, "~> 2.8.1"},
      {:phoenix_html_helpers, "~> 1.0"},
      {:heroicons, github: "tailwindlabs/heroicons", tag: "v2.1.5", app: false, compile: false, sparse: "optimized"}
    ]
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get"],
      "assets.deploy": [
        "cmd --cd assets npm run deploy",
        "esbuild default --minify",
        "phx.digest"
      ]
    ]
  end
end
