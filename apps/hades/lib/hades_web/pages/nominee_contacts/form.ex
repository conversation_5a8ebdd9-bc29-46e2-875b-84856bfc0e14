defmodule HadesWeb.NomineeContactsLive.Form do
  @moduledoc false
  use HadesWeb, :live_view

  alias Gaia.BeneficialOwners
  alias Gaia.BeneficialOwners.NomineeContact

  @impl true
  def render(assigns) do
    ~H"""
    <div class="max-w-3xl mr-auto w-full">
      <div class="w-full mb-4 flex flex-col md:flex-row md:items-center md:justify-between">
        <% links = build_breadcrumb_links(@short_name, @nominee_contact) %>

        <PC.breadcrumbs links={links} />
        <div class="flex items-center gap-2">
          <%= if @short_name do %>
            <PC.button
              to={~p"/beneficial_owners/nominee_contacts/#{@nominee_contact}"}
              link_type="live_redirect"
              variant="primary"
              size="xs"
            >
              <PC.icon name="hero-arrow-left-mini" />
            </PC.button>
          <% end %>
        </div>
      </div>

      <div class="max-w-xl">
        <.form for={@form} id="nominee_contact-form" phx-change="validate" phx-submit="save">
          <PC.field field={@form[:account_name]} type="text" />
          <PC.field field={@form[:contact_name]} type="text" />
          <PC.field field={@form[:email]} type="email" />
          <div id="cc-emails" class="w-full mb-4">
            <div class="flex items-center justify-between w-full mb-3">
              <PC.h4 class="mb-0">CC Emails</PC.h4>
              <.link phx-click="add-cc-email">
                <PC.button variant="primary" size="xs">
                  <PC.icon name="hero-plus-micro" /> Add
                </PC.button>
              </.link>
            </div>
            <% cc_emails = cc_emails_from_assigns(assigns) %>
            <% cc_email_errors =
              case @form.errors do
                %{} = errors_map -> Map.get(errors_map, :cc_emails, [])
                _ -> []
              end %>
            <% cc_email_errors = if is_list(cc_email_errors), do: cc_email_errors, else: [] %>
            <%= for {cc_email, index} <- Enum.with_index(cc_emails) do %>
              <% msg = Enum.at(cc_email_errors, index) %>
              <div class="flex items-start gap-2 mb-2">
                <div class="flex-1">
                  <div class={if msg, do: "pc-form-field-wrapper--error", else: ""}>
                    <PC.input
                      name={"nominee_contact[cc_emails][#{index}]"}
                      id={"nominee_contact_cc_emails_#{index}"}
                      value={cc_email}
                      type="email"
                      placeholder="Enter email address"
                      class="pc-text-input mb-0.5"
                    />
                    <%= if msg do %>
                      <p class="pc-form-field-error">{msg}</p>
                    <% end %>
                  </div>
                </div>
                <.link
                  phx-click="remove-cc-email"
                  phx-value-index={index}
                  class="text-xs flex items-center gap-1 text-red-700 dark:text-red-300 mb-2"
                >
                  <PC.icon name="hero-minus-micro" /> Remove
                </.link>
              </div>
            <% end %>
          </div>
          <PC.field field={@form[:address_line_one]} type="text" label="Address Line 1" />
          <PC.field field={@form[:address_line_two]} type="text" label="Address Line 2" />
          <PC.field field={@form[:address_city]} type="text" label="City" />
          <PC.field field={@form[:address_state]} type="text" label="State" />
          <PC.field field={@form[:address_postcode]} type="text" label="Postcode" />
          <PC.field field={@form[:address_country]} type="text" label="Country" />
          <PC.field field={@form[:notes]} type="textarea" label="Notes" />
          <PC.field field={@form[:encrypted_password]} type="text" label="Password" />
          <PC.field
            field={@form[:custom_prompt]}
            type="textarea"
            label="Custom prompt"
            placeholder="Enter a custom prompt for the nominee contact."
          />
          <PC.p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
            To make the best of the AI please provide an example of your request. <br />
            Something like: <br />
            "The account_name field should be the concatenation of the CLIENT NAME and FUND NAME columns. e.g., (The NT Company - NT AU BANK HELD)"
          </PC.p>
          <div id="alias-names" class="w-full mb-4">
            <div class="flex items-center justify-between w-full mb-3">
              <PC.h4 class="mb-0">Alias names</PC.h4>
              <.link phx-click="add-alias-name">
                <PC.button variant="primary" size="xs" class="flex items-center gap-2">
                  <PC.icon name="hero-plus-micro" /> Add alias name
                </PC.button>
              </.link>
            </div>
            <% alias_values =
              case @form[:alias_names].value do
                %{} = map -> Map.values(map)
                list when is_list(list) -> list
                _ -> []
              end %>
            <%= if Enum.empty?(alias_values) do %>
              <div class="p-3 bg-slate-200 dark:bg-slate-800 rounded text-center">
                <PC.p class="text-gray-500 dark:text-gray-400 mb-0">No alias names added</PC.p>
              </div>
            <% end %>
            <% errors = Keyword.get_values(@form.errors, :alias_names) %>
            <%= for {alias, index} <- Enum.with_index(alias_values) do %>
              <div id={"alias-name-#{index}"}>
                <PC.form_label>{"Alias name #{index + 1}"}</PC.form_label>
                <% {msg, _} =
                  Enum.find(errors, {nil, nil}, fn {_msg, [index: idx]} -> idx == index end) %>

                <div class={if msg, do: "pc-form-field-wrapper--error", else: ""}>
                  <PC.input
                    name={"nominee_contact[alias_names][#{index}]"}
                    id={"nominee_contact_alias_names_#{index}"}
                    value={alias}
                    type="text"
                    class="pc-text-input mb-0.5"
                  />
                  <%= if msg do %>
                    <p class="pc-form-field-error">{msg}</p>
                  <% end %>
                </div>
                <.link
                  phx-click="remove-alias-name"
                  phx-value-index={index}
                  class="text-xs flex items-center gap-1 text-red-700 dark:text-red-300 mb-2"
                >
                  <PC.icon name="hero-minus-micro" /> Remove
                </.link>
              </div>
            <% end %>
          </div>
          <footer class="flex items-center gap-2">
            <PC.button phx-disable-with="Saving...">Save</PC.button>
            <.link navigate={return_path(@return_to, @nominee_contact)}>
              <PC.button variant="primary">Cancel</PC.button>
            </.link>
          </footer>
        </.form>
      </div>
    </div>
    """
  end

  @impl true
  def mount(params, _session, socket) do
    {:ok,
     socket
     |> assign(:menu, :nominees)
     |> assign(:return_to, return_to(params["return_to"]))
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp return_to("index"), do: "index"
  defp return_to(_), do: "show"

  defp apply_action(socket, :edit, %{"id" => id}) do
    nominee_contact = BeneficialOwners.get_nominee_contact!(id)

    decrypted_password =
      case nominee_contact.encrypted_password do
        nil ->
          ""

        encrypted_password ->
          BeneficialOwners.CandidateImporterAi.decode_password(encrypted_password)
      end

    nominee_contact = %{nominee_contact | encrypted_password: decrypted_password}

    short_name = truncate(nominee_contact.account_name, 25)

    socket
    |> assign(:page_title, "Edit Nominee contact")
    |> assign(:nominee_contact, nominee_contact)
    |> assign(:short_name, short_name)
    |> assign(:form, to_form(BeneficialOwners.change_nominee_contact(nominee_contact)))
  end

  defp apply_action(socket, :new, _params) do
    nominee_contact = %NomineeContact{}

    socket
    |> assign(:page_title, "New nominee contact")
    |> assign(:nominee_contact, nominee_contact)
    |> assign(:short_name, nil)
    |> assign(:form, to_form(BeneficialOwners.change_nominee_contact(nominee_contact)))
  end

  @impl true
  def handle_event(
        "validate",
        %{"nominee_contact" => nominee_contact_params, "_target" => ["nominee_contact", "alias_names", _]},
        socket
      ) do
    updated_params =
      update_params_with_alias_names(nominee_contact_params)

    changeset =
      BeneficialOwners.change_nominee_contact(socket.assigns.nominee_contact, updated_params)

    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event(
        "validate",
        %{"nominee_contact" => nominee_contact_params, "_target" => ["nominee_contact", "cc_emails", _]},
        socket
      ) do
    updated_params =
      update_params_with_alias_names(nominee_contact_params)

    changeset =
      BeneficialOwners.change_nominee_contact(socket.assigns.nominee_contact, updated_params)

    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("validate", %{"nominee_contact" => nominee_contact_params}, socket) do
    updated_params =
      update_params_with_alias_names_validate(nominee_contact_params)

    changeset =
      BeneficialOwners.change_nominee_contact(socket.assigns.nominee_contact, updated_params)

    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"nominee_contact" => nominee_contact_params}, socket) do
    updated_params =
      nominee_contact_params
      |> update_params_with_alias_names()
      |> encrypt_password_if_present()

    save_nominee_contact(socket, socket.assigns.live_action, updated_params)
  end

  def handle_event("add-alias-name", _, socket) do
    new_aliases = alias_names_from_assigns(socket.assigns) ++ [nil]
    params = Map.put(socket.assigns.form.params, "alias_names", new_aliases)
    changeset = BeneficialOwners.change_nominee_contact(socket.assigns.nominee_contact, params)
    {:noreply, assign(socket, form: to_form(changeset))}
  end

  def handle_event("remove-alias-name", %{"index" => index}, socket) do
    updated_aliases =
      socket.assigns
      |> alias_names_from_assigns()
      |> List.delete_at(String.to_integer(index))

    params = Map.put(socket.assigns.form.params, "alias_names", updated_aliases)
    changeset = BeneficialOwners.change_nominee_contact(socket.assigns.nominee_contact, params)
    {:noreply, assign(socket, form: to_form(changeset))}
  end

  def handle_event("add-cc-email", _, socket) do
    new_cc_emails = cc_emails_from_assigns(socket.assigns) ++ [nil]
    params = Map.put(socket.assigns.form.params, "cc_emails", new_cc_emails)
    changeset = BeneficialOwners.change_nominee_contact(socket.assigns.nominee_contact, params)
    {:noreply, assign(socket, form: to_form(changeset))}
  end

  def handle_event("remove-cc-email", %{"index" => index}, socket) do
    updated_cc_emails =
      socket.assigns
      |> cc_emails_from_assigns()
      |> List.delete_at(String.to_integer(index))

    params = Map.put(socket.assigns.form.params, "cc_emails", updated_cc_emails)
    changeset = BeneficialOwners.change_nominee_contact(socket.assigns.nominee_contact, params)
    {:noreply, assign(socket, form: to_form(changeset))}
  end

  defp save_nominee_contact(socket, :edit, nominee_contact_params) do
    case BeneficialOwners.update_nominee_contact(
           socket.assigns.nominee_contact,
           nominee_contact_params
         ) do
      {:ok, nominee_contact} ->
        {:noreply,
         socket
         |> put_flash(:info, "Nominee contact updated successfully")
         |> push_navigate(to: return_path(socket.assigns.return_to, nominee_contact))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}

      {:error, %Postgrex.Error{} = error} ->
        error_message =
          case error do
            %{postgres: %{message: message}} when is_binary(message) -> message
            _ -> "An error occurred while saving the nominee contact"
          end

        {:noreply, put_flash(socket, :error, error_message)}
    end
  end

  defp save_nominee_contact(socket, :new, nominee_contact_params) do
    case BeneficialOwners.create_nominee_contact(nominee_contact_params) do
      {:ok, nominee_contact} ->
        {:noreply,
         socket
         |> put_flash(:info, "Nominee contact created successfully")
         |> push_navigate(to: return_path(socket.assigns.return_to, nominee_contact))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp encrypt_password_if_present(nominee_contact) do
    case nominee_contact["encrypted_password"] do
      "" ->
        nominee_contact

      encrypted_password ->
        %{
          nominee_contact
          | "encrypted_password" => BeneficialOwners.CandidateImporterAi.encode_password(encrypted_password)
        }
    end
  end

  defp update_params_with_alias_names(nominee_contact_params) do
    cleaned_alias_names =
      nominee_contact_params
      |> Map.get("alias_names", %{})
      |> Map.values()

    nominee_contact_params
    |> Map.put("alias_names", cleaned_alias_names)
    |> update_params_with_cc_emails()
  end

  defp update_params_with_alias_names_validate(nominee_contact_params) do
    cleaned_alias_names =
      nominee_contact_params
      |> Map.get("alias_names", %{})
      |> Map.values()
      |> Enum.filter(fn name ->
        String.trim(name) != ""
      end)

    nominee_contact_params
    |> Map.put("alias_names", cleaned_alias_names)
    |> update_params_with_cc_emails_validate()
  end

  defp update_params_with_cc_emails(nominee_contact_params) do
    cleaned_cc_emails =
      nominee_contact_params
      |> Map.get("cc_emails", %{})
      |> Map.values()

    Map.put(nominee_contact_params, "cc_emails", cleaned_cc_emails)
  end

  defp update_params_with_cc_emails_validate(nominee_contact_params) do
    cleaned_cc_emails =
      nominee_contact_params
      |> Map.get("cc_emails", %{})
      |> Map.values()
      |> Enum.filter(fn email ->
        String.trim(email) != ""
      end)

    Map.put(nominee_contact_params, "cc_emails", cleaned_cc_emails)
  end

  defp alias_names_from_assigns(assigns) do
    case assigns.form.params["alias_names"] do
      nil -> assigns.nominee_contact.alias_names || []
      %{} = map -> Map.values(map)
      list when is_list(list) -> list
      _ -> []
    end
  end

  defp cc_emails_from_assigns(assigns) do
    case assigns.form.params["cc_emails"] do
      nil -> assigns.nominee_contact.cc_emails || []
      %{} = map -> Map.values(map)
      list when is_list(list) -> list
      _ -> []
    end
  end

  defp return_path("index", _nominee_contact), do: ~p"/beneficial_owners/nominee_contacts"

  defp return_path("show", %NomineeContact{id: id} = nominee_contact) when not is_nil(id),
    do: ~p"/beneficial_owners/nominee_contacts/#{nominee_contact}"

  defp return_path(_, nominee_contact), do: return_path("index", nominee_contact)

  defp build_breadcrumb_links(short_name, nominee_contact) do
    base_links = [%{label: "Nominee contacts", to: ~p"/beneficial_owners/nominee_contacts/"}]

    if short_name do
      base_links ++
        [
          %{label: short_name, to: ~p"/beneficial_owners/nominee_contacts/#{nominee_contact}"},
          %{label: "Edit", to: "#"}
        ]
    else
      base_links ++ [%{label: "New", to: "#"}]
    end
  end
end
