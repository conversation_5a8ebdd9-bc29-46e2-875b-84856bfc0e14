defmodule HadesWeb.NomineeContactsLive.Show do
  @moduledoc false
  use <PERSON>Web, :live_view

  alias <PERSON>aia.BeneficialOwners

  @impl true
  def render(assigns) do
    ~H"""
    <div class="flex flex-col gap-4 w-full mr-auto">
      <div class="w-full mb-4 flex flex-col md:flex-row md:items-center md:justify-between">
        <PC.breadcrumbs links={[
          %{label: "Nominee contacts", to: ~p"/beneficial_owners/nominee_contacts/"},
          %{label: @short_name, to: "#"}
        ]} />
        <div class="flex items-center gap-2">
          <PC.button
            to={~p"/beneficial_owners/nominee_contacts"}
            link_type="live_redirect"
            variant="primary"
            size="xs"
          >
            <PC.icon name="hero-arrow-left-mini" />
          </PC.button>
          <.link navigate={
            ~p"/beneficial_owners/nominee_contacts/#{@nominee_contact}/edit?return_to=show"
          }>
            <PC.button variant="primary" size="xs">
              <PC.icon name="hero-pencil-square-mini" /><span class="ml-2">Edit</span>
            </PC.button>
          </.link>
        </div>
      </div>

      <div class="w-full flex flex-col gap-8 mr-auto">
        <div>
          <PC.h3 class="mb-4">{@nominee_contact.account_name}</PC.h3>
          <div class="grid grid-cols-2 gap-2">
            <PC.p>Contact name</PC.p>
            <PC.p>{@nominee_contact.contact_name || "-"}</PC.p>
            <PC.p>Email</PC.p>
            <PC.p>{@nominee_contact.email || "-"}</PC.p>
            <PC.p>CC Emails</PC.p>
            <PC.p>
              <%= if @nominee_contact.cc_emails && !Enum.empty?(@nominee_contact.cc_emails) do %>
                {Enum.join(@nominee_contact.cc_emails, ", ")}
              <% else %>
                -
              <% end %>
            </PC.p>
          </div>
          <PC.h4 class="mt-4 mb-2">Address Information</PC.h4>
          <%= if String.length(@address) > 0 do %>
            <PC.p>{@address}</PC.p>
          <% else %>
            <PC.p class="text-gray-500 dark:text-gray-400 mb-0">No address information added</PC.p>
          <% end %>

          <PC.h4 class="mt-4 mb-2">Notes</PC.h4>
          <%= if @nominee_contact.notes && String.length(@nominee_contact.notes) > 0 do %>
            <PC.p class="whitespace-pre-line">{@nominee_contact.notes}</PC.p>
          <% else %>
            <PC.p class="text-gray-500 dark:text-gray-400 mb-0">No notes added</PC.p>
          <% end %>
          <PC.h4 class="mt-4 mb-2">Password</PC.h4>
          <%= if @nominee_contact.encrypted_password do %>
            <PC.p>
              <PC.icon name="hero-check-circle" class="text-green-500 inline-block mr-1" />
              Password saved.
            </PC.p>
          <% else %>
            <PC.p class="text-gray-500 dark:text-gray-400 mb-0">No password added</PC.p>
          <% end %>
          <PC.h4 class="mt-4 mb-2">Custom prompt</PC.h4>
          <%= if @nominee_contact.custom_prompt && String.length(@nominee_contact.custom_prompt) > 0 do %>
            <PC.p class="whitespace-pre-line">{@nominee_contact.custom_prompt}</PC.p>
          <% else %>
            <PC.p class="text-gray-500 dark:text-gray-400 mb-0">No custom prompt added</PC.p>
          <% end %>
        </div>
        <div class="flex flex-col gap-2">
          <PC.h4>Alias names</PC.h4>
          <div id="alias_names">
            <PC.p :for={item <- @nominee_contact.alias_names || []}>{item}</PC.p>
            <%= if !@nominee_contact.alias_names || Enum.empty?(@nominee_contact.alias_names) do %>
              <div class="p-3 bg-slate-200 dark:bg-slate-800 rounded text-center">
                <PC.p class="text-gray-500 dark:text-gray-400 mb-0">No alias names added</PC.p>
              </div>
            <% end %>
          </div>
        </div>

        <div id="similar_names" class="flex flex-col gap-2">
          <div class="flex flex-col gap-2">
            <PC.h4>Similar nominee contacts</PC.h4>
            <.form
              for={%{}}
              id="search-nominee-contacts-form"
              phx-change="search_nominee_contacts"
              phx-debounce="300"
            >
              <PC.input
                name="search"
                type="text"
                placeholder="Or search all nominees..."
                value={@search_value || ""}
                class="pc-text-input mb-4 max-w-[500px]"
              />
              <%= if length(@filtered_nominee_contacts) > 0 do %>
                <PC.p class="text-sm text-gray-500 mb-2">
                  Found {length(@filtered_nominee_contacts)} matching nominees
                </PC.p>
              <% end %>
            </.form>
          </div>
          <.form for={%{}} id="merge-similar-contacts-form" phx-change="update_merge_ids">
            <PC.table id="similar_nominee_contacts" rows={@filtered_nominee_contacts}>
              <:col :let={contact} label="" class="w-[40px]">
                <%= if Enum.member?(@merge_ids, "#{contact.id}") do %>
                  <input
                    name={contact.id}
                    value={contact.id}
                    type="checkbox"
                    checked="checked"
                    id={"merge-#{contact.id}-checked"}
                  />
                <% else %>
                  <PC.input
                    name={contact.id}
                    value={contact.id}
                    type="checkbox"
                    id={"merge-#{contact.id}"}
                  />
                <% end %>
              </:col>
              <:col :let={contact} label="ID" class="lg:w-[40px]">
                <div class="block whitespace-nowrap">
                  <span class="font-bold">{contact.id}</span>
                </div>
              </:col>
              <:col :let={contact} label="Account" class="lg:w-[550px]">
                <div class="block whitespace-nowrap">
                  {truncate(contact.account_name, 100)}
                  <.link navigate={~p"/beneficial_owners/nominee_contacts/#{contact}"}>
                    <PC.icon name="hero-arrow-right" class="h-3" />
                  </.link>
                </div>
              </:col>
              <:col :let={contact} label="Contact name/email" class="lg:w-[200px]">
                {Enum.reject(
                  [contact.contact_name, contact.email],
                  &is_nil/1
                )
                |> Enum.join(", ")}
              </:col>
              <:col :let={contact} label="Aliases" class="lg:w-[350px]">
                {(contact.alias_names || [])
                |> Enum.map(&truncate(&1, 60))
                |> Enum.join(", ")}
              </:col>
            </PC.table>
          </.form>
          <%= if !Enum.empty?(@filtered_nominee_contacts) do %>
            <PC.button
              phx-click="merge"
              phx-value-target_id={@nominee_contact.id}
              data-confirm="Are you sure you want to merge these contacts? This action cannot be undone."
              variant="primary"
              disabled={@merge_ids == []}
              class="max-w-[500px]"
            >
              Merge selected
            </PC.button>
          <% else %>
            <div class="p-3 bg-slate-200 dark:bg-slate-800 rounded text-center">
              <PC.p class="text-gray-500 dark:text-gray-400 mb-0">No similar nominees found</PC.p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def mount(%{"id" => id}, _session, socket) do
    contact = BeneficialOwners.get_nominee_contact!(id)

    similar_contacts =
      BeneficialOwners.find_similar_nominee_contacts(contact.account_name, contact.id)

    address =
      [
        contact.address_line_one,
        contact.address_line_two,
        contact.address_city,
        contact.address_state,
        contact.address_postcode,
        contact.address_country
      ]
      |> Enum.reject(&is_nil/1)
      |> Enum.join(", ")

    short_name = truncate(contact.account_name, 25)

    {:ok,
     socket
     |> assign(:menu, :nominees)
     |> assign(:page_title, contact.account_name)
     |> assign(:nominee_contact, contact)
     |> assign(:merge_ids, [])
     |> assign(:address, address)
     |> assign(:short_name, short_name)
     |> assign(:similar_nominee_contacts, similar_contacts)
     |> assign(:filtered_nominee_contacts, similar_contacts)
     |> assign(:search_value, nil)}
  end

  @impl true
  def handle_event("update_merge_ids", params, socket) do
    merge_ids = Enum.map(params, fn {k, _v} -> k end)
    merge_ids = Enum.filter(merge_ids, fn id -> id != "_target" end)

    {:noreply, assign(socket, :merge_ids, merge_ids)}
  end

  def handle_event("merge", %{"target_id" => target_id}, socket) do
    # ids: ["473", "506", "_unused_506"]
    # Filter out those with "_unused_" prefix
    ids = Enum.filter(socket.assigns.merge_ids, fn id -> !String.starts_with?(id, "_unused_") end)

    if Enum.empty?(ids) do
      {:noreply, put_flash(socket, :error, "No contacts selected for merging")}
    else
      exec_merge(target_id, ids, socket)
    end
  end

  def handle_event("merge", _, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("search_nominee_contacts", %{"search" => search_value}, socket) do
    selected_ids = socket.assigns.merge_ids
    account_name = socket.assigns.nominee_contact.account_name

    filtered_contacts =
      case {search_value, selected_ids} do
        {"", selected} when is_list(selected) and selected != [] ->
          BeneficialOwners.find_similar_nominee_contacts(
            account_name,
            socket.assigns.nominee_contact.id,
            selected
          )

        {"", _} ->
          socket.assigns.similar_nominee_contacts

        {search_value, _} when search_value != "" ->
          BeneficialOwners.search_nominee_contacts_excluding_current(
            search_value,
            socket.assigns.nominee_contact.id,
            selected_ids
          )
      end

    {:noreply,
     socket
     |> assign(:search_value, search_value)
     |> assign(:filtered_nominee_contacts, filtered_contacts)}
  end

  defp exec_merge(target_id, ids, socket) do
    case BeneficialOwners.merge_nominee_contacts(target_id, ids) do
      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Error merging nominee contacts")}

      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "#{Enum.count(ids)} nominee contacts merged")
         |> push_navigate(to: ~p"/beneficial_owners/nominee_contacts/#{target_id}")}
    end
  end
end
