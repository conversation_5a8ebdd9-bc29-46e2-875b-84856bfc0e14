defmodule HadesWeb.Pages.Organisations.BoReport.ReportCandidate.EmailForm do
  @moduledoc """
  Email form component for sending disclosure requests to beneficial owners
  """

  use HadesWeb, :live_component

  import PetalComponents.Button

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign(:authorisation_letter, Enum.at(assigns.authorisation_letters, 0))
      |> assign(
        :is_uk_company,
        Enum.at(assigns.authorisation_letters, 0).company_profile.ticker.market_key in [
          :lse,
          :aqse
        ]
      )

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <tr>
      <td colspan={9} class="px-6 py-4">
        <div class="border border-blue-200 dark:border-blue-700 rounded-md p-4 bg-white dark:bg-gray-800 shadow-sm">
          <div class="flex items-center gap-4 mb-4">
            <h4 class="text-sm font-medium dark:text-gray-200">Send Disclosure Request Email</h4>
            <%= if @candidate.last_contact_at do %>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Last contacted: {@candidate.last_contact_at
                |> DateTime.from_naive!("UTC")
                |> DateTime.shift_zone!(@authorisation_letter.company_profile.timezone)
                |> Calendar.strftime("%Y-%m-%d")} {@authorisation_letter.company_profile.timezone}
              </p>
            <% end %>
          </div>
          <form phx-submit="send_email_submit" phx-target={@myself}>
            <input type="hidden" name="candidate_id" value={@candidate.id} />
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <div class="mb-4">
                  <label
                    for="recipient_name"
                    class="block text-sm font-medium mb-1 dark:text-gray-300"
                  >
                    Recipient Name*
                  </label>
                  <input
                    type="text"
                    required
                    name="recipient_name"
                    id="recipient_name"
                    value={@candidate.account_name || ""}
                    placeholder="Enter recipient name"
                    class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  />
                </div>
              </div>
              <div>
                <div class="mb-4">
                  <label
                    for="recipient_email"
                    class="block text-sm font-medium mb-1 dark:text-gray-300"
                  >
                    Recipient Email*
                  </label>
                  <input
                    type="email"
                    name="recipient_email"
                    id="recipient_email"
                    required
                    value={@candidate.nominee_contact.email || ""}
                    placeholder="Enter email address"
                    class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  />
                </div>
              </div>
              <div class="col-span-2">
                <div class="mb-4">
                  <label for="cc_emails" class="block text-sm font-medium mb-1 dark:text-gray-300">
                    CC Emails
                  </label>
                  <input
                    type="text"
                    name="cc_emails"
                    id="cc_emails"
                    value={Enum.join(@candidate.nominee_contact.cc_emails || [], ", ")}
                    placeholder="Enter CC email addresses separated by commas"
                    class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  />
                  <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    You can edit these emails for this send only. To permanently update CC emails, edit the nominee contact.
                  </p>
                </div>
              </div>
              <div class={["mb-4", @is_uk_company && "hidden"]}>
                <label
                  for="recipient_address_one"
                  class="block text-sm font-medium mb-1 dark:text-gray-300"
                >
                  Recipient Address Line 1*
                </label>
                <input
                  type="text"
                  name="recipient_address_one"
                  id="recipient_address_one"
                  required={!@is_uk_company}
                  value={@candidate.address_line_one || ""}
                  placeholder="Enter address line one"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                />
              </div>
              <div class={["mb-4", @is_uk_company && "hidden"]}>
                <label
                  for="recipient_address_two"
                  class="block text-sm font-medium mb-1 dark:text-gray-300"
                >
                  Recipient Address Line 2
                </label>
                <input
                  type="text"
                  name="recipient_address_two"
                  id="recipient_address_two"
                  value={@candidate.address_line_two || ""}
                  placeholder="Enter address line two"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                />
              </div>

              <div class={["mb-4", @is_uk_company && "hidden"]}>
                <label for="recipient_city" class="block text-sm font-medium mb-1 dark:text-gray-300">
                  Recipient City
                </label>
                <input
                  type="text"
                  name="recipient_city"
                  id="recipient_city"
                  value={@candidate.address_city || ""}
                  placeholder="Enter city"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                />
              </div>

              <div class={["mb-4", @is_uk_company && "hidden"]}>
                <label for="recipient_state" class="block text-sm font-medium mb-1 dark:text-gray-300">
                  Recipient State
                </label>
                <input
                  type="text"
                  name="recipient_state"
                  id="recipient_state"
                  value={@candidate.address_state || ""}
                  placeholder="Enter state"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                />
              </div>

              <div class={["mb-4", @is_uk_company && "hidden"]}>
                <label
                  for="recipient_postcode"
                  class="block text-sm font-medium mb-1 dark:text-gray-300"
                >
                  Recipient Postcode
                </label>
                <input
                  type="text"
                  name="recipient_postcode"
                  id="recipient_postcode"
                  value={@candidate.address_postcode || ""}
                  placeholder="Enter postcode"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                />
              </div>

              <div class={["mb-4", @is_uk_company && "hidden"]}>
                <label
                  for="recipient_country"
                  class="block text-sm font-medium mb-1 dark:text-gray-300"
                >
                  Recipient Country
                </label>
                <input
                  type="text"
                  name="recipient_country"
                  id="recipient_country"
                  value={@candidate.address_country || ""}
                  placeholder="Enter country"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                />
              </div>
              <div class={["mb-4"]}>
                <label
                  for="request_holding_date"
                  class="block text-sm font-medium mb-1 dark:text-gray-300"
                >
                  Request Holding Date*
                </label>
                <input
                  type="date"
                  name="request_holding_date"
                  id="request_holding_date"
                  required
                  value={Date.utc_today() |> Calendar.strftime("%Y-%m-%d")}
                  placeholder="Enter request holding date"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                />
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  This will be used in the email attachement to request the holding date.
                </p>
              </div>
            </div>

            <div class="mb-4">
              <h4 class="text-sm font-medium mb-1 dark:text-gray-200">
                Company Information
              </h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <div class={["mb-4", !@is_uk_company && "hidden"]}>
                    <label
                      for="company_sedol"
                      class="block text-sm font-medium mb-1 dark:text-gray-300"
                    >
                      Company SEDOL
                    </label>
                    <input
                      type="text"
                      name="company_sedol"
                      id="company_sedol"
                      value={@authorisation_letter.company_profile.sedol || ""}
                      placeholder="Enter SEDOL or leave blank if not applicable"
                      class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                    />
                  </div>

                  <div class={["mb-4", @is_uk_company && "hidden"]}>
                    <label for="company_acn" class="block text-sm font-medium mb-1 dark:text-gray-300">
                      Company ACN*
                    </label>
                    <input
                      type="text"
                      name="company_acn"
                      id="company_acn"
                      value={@authorisation_letter.company_profile.acn || ""}
                      required={!@is_uk_company}
                      placeholder="Enter ACN"
                      class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                    />
                  </div>
                </div>
                <div>
                  <div class="mb-4">
                    <label
                      for="company_isin"
                      class="block text-sm font-medium mb-1 dark:text-gray-300"
                    >
                      Company ISIN*
                    </label>
                    <input
                      type="text"
                      name="company_isin"
                      id="company_isin"
                      value={@authorisation_letter.company_profile.isin || ""}
                      required
                      placeholder="Enter ISIN"
                      class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="mb-4">
              <h4 class="text-sm font-medium mb-1 dark:text-gray-200">
                Email Requirements
              </h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="mb-4">
                  <label for="schedule_date" class="block text-sm font-medium mb-1 dark:text-gray-300">
                    Schedule Date*
                  </label>
                  <input
                    type="date"
                    name="schedule_date"
                    id="schedule_date"
                    required
                    value={Date.utc_today() |> Calendar.strftime("%Y-%m-%d")}
                    min={Date.utc_today() |> Calendar.strftime("%Y-%m-%d")}
                    class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  />
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    The scheduled date will be the date on the letter header to the nominee.
                  </p>
                </div>

                <div class={["mb-4", @is_uk_company && "hidden"]}>
                  <label for="deadline_date" class="block text-sm font-medium mb-1 dark:text-gray-300">
                    Response Deadline*
                  </label>
                  <input
                    type="date"
                    name="deadline_date"
                    id="deadline_date"
                    required={!@is_uk_company}
                    value={
                      Date.utc_today()
                      |> Date.add(2)
                      |> Calendar.strftime("%Y-%m-%d")
                    }
                    min={Date.utc_today() |> Calendar.strftime("%Y-%m-%d")}
                    class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  />
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    The deadline date should be 2 business days after the schedule date.
                  </p>
                </div>
                <div class={["mb-4", @is_uk_company && "hidden"]}>
                  <label for="sender_name" class="block text-sm font-medium mb-1 dark:text-gray-300">
                    Sender Name*
                  </label>
                  <input
                    type="text"
                    name="sender_name"
                    id="sender_name"
                    required={!@is_uk_company}
                    value="Disclosure Team"
                    placeholder="Enter sender name"
                    class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  />
                </div>

                <div class="mb-4">
                  <label
                    for="authorisation_letter_id"
                    class="block text-sm font-medium mb-1 dark:text-gray-300"
                  >
                    Authorisation Letter*
                  </label>
                  <select
                    name="authorisation_letter_id"
                    id="authorisation_letter_id"
                    required
                    class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  >
                    <option value="">Select an authorisation letter</option>
                    <%= for letter <- @authorisation_letters do %>
                      <option value={letter.id} selected={letter.id == @authorisation_letter.id}>
                        {letter.name}.pdf
                      </option>
                    <% end %>
                  </select>
                </div>
              </div>
            </div>
            <div class="flex justify-end space-x-2">
              <.button
                color="gray"
                variant="outline"
                type="button"
                phx-click="close_form"
                phx-target={@parent_id}
              >
                Cancel
              </.button>
              <.button
                color="primary"
                type="submit"
                data-confirm="Are you sure you want to send this disclosure request email?"
              >
                Send Email
              </.button>
            </div>
          </form>
        </div>
      </td>
    </tr>
    """
  end

  @impl true
  def handle_event("send_email_submit", params, socket) do
    args = %{
      "company" => %{
        "profile_id" => socket.assigns.authorisation_letter.company_profile.id,
        "acn" => params["company_acn"],
        "sedol" => params["company_sedol"],
        "isin" => params["company_isin"]
      },
      "authorisation_letter_id" => params["authorisation_letter_id"],
      "schedule_date" => params["schedule_date"],
      "deadline_date" => params["deadline_date"],
      "request_holding_date" => params["request_holding_date"],
      "candidate" => %{
        "id" => socket.assigns.candidate.id,
        "name" => params["recipient_name"],
        "email" => params["recipient_email"],
        "cc_emails" => parse_cc_emails(params["cc_emails"]),
        "account_name" => socket.assigns.candidate.nominee_contact.account_name,
        "address" => %{
          "line_one" => params["recipient_address_one"],
          "line_two" => params["recipient_address_two"],
          "city" => params["recipient_city"],
          "state" => params["recipient_state"],
          "postcode" => params["recipient_postcode"],
          "country" => params["recipient_country"]
        }
      },
      "sender_name" => params["sender_name"]
    }

    days_to_schedule =
      case params["schedule_date"] do
        nil ->
          0

        _ ->
          Date.diff(Date.from_iso8601!(params["schedule_date"]), Date.utc_today())
      end

    case Gaia.Jobs.EmailDisclosureNominee.enqueue(args, schedule_in: {days_to_schedule, :day}) do
      {:ok, _} ->
        # Send parent to close the form
        send_update(
          socket.assigns.parent_id,
          action: :close_form
        )

        # Wait for the email to be sent
        Process.send_after(self(), {:refresh_candidates}, 5_000)

        {:noreply, socket}

      {:error, _} ->
        send(self(), {:flash, :error, "Failed to enqueue email"})

        {:noreply, socket}
    end
  end

  defp parse_cc_emails(cc_emails_string) when is_binary(cc_emails_string) do
    cc_emails_string
    |> String.split(",")
    |> Enum.map(&String.trim/1)
    |> Enum.filter(&(&1 != ""))
  end

  defp parse_cc_emails(_), do: []
end
