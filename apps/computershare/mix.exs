defmodule Computershare.MixProject do
  use Mix.Project

  def project do
    [
      app: :computershare,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.18",
      start_permanent: Mix.env() == :prod,
      deps: deps()
    ]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      mod: {Computershare.Application, []},
      extra_applications: [:logger]
    ]
  end

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:floki, ">= 0.30.0"},
      {:timex, "~> 3.7.2"},
      {:httpoison, "~> 1.8"},
      {:poison, "~> 3.1"},
      {:plug, "~> 1.14"},
      {:xlsx_reader, "~> 0.4.0"},
      {:helper, in_umbrella: true}
    ]
  end
end
