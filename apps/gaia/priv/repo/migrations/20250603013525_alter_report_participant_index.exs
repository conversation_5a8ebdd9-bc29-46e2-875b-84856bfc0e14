defmodule Gaia.Repo.Migrations.AlterReportParticipantIndex do
  use Ecto.Migration

  def up do
    drop_if_exists unique_index(:beneficial_owner_report_participants, [:report_id, :account_name, :parent_id, :layer], name: :beneficial_owner_report_participants_account_name_report_id_paren)

    execute "CREATE UNIQUE INDEX beneficial_owner_report_participants_unique_idx ON beneficial_owner_report_participants (report_id, account_name, COALESCE(address_line_one, ''), COAL<PERSON>CE(address_postcode, ''), parent_id, layer)"
  end

  def down do
    execute "DROP INDEX IF EXISTS beneficial_owner_report_participants_unique_idx"

    create unique_index(
      :beneficial_owner_report_participants,
      [:report_id, :account_name, :parent_id, :layer],
      name: :beneficial_owner_report_participants_account_name_report_id_paren
    )
  end
end
