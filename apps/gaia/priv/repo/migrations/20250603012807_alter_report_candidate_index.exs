defmodule Gaia.Repo.Migrations.AlterReportCandidateIndex do
  use Ecto.Migration

  def up do
    drop_if_exists unique_index(:beneficial_owner_report_candidates, [:account_name, :report_id, :parent_id, :layer])

    execute "CREATE UNIQUE INDEX beneficial_owner_report_candidates_unique_idx ON beneficial_owner_report_candidates (report_id, account_name, COALESCE(address_line_one, ''), COALESCE(address_postcode, ''), parent_id, layer)"
  end

  def down do
    execute "DROP INDEX IF EXISTS beneficial_owner_report_candidates_unique_idx"

    create unique_index(:beneficial_owner_report_candidates, [:account_name, :report_id, :parent_id, :layer])
  end
end
