defmodule Gaia.Repo.Migrations.FixPrimaryDistributionForAllMedias do
  use Ecto.Migration

  def up do
    # Reset all primary_distribution values to NULL first
    execute("UPDATE interactions_medias SET primary_distribution = NULL")

    # Update primary_distribution using a single efficient query with proper priority order
    # Priority: announcement > update > email > linkedin > twitter
    execute("""
    UPDATE interactions_medias
    SET primary_distribution = (
      CASE
        -- Check for announcement (highest priority)
        WHEN EXISTS (
          SELECT 1 FROM interactions_media_announcements
          WHERE interactions_media_announcements.media_id = interactions_medias.id
        ) THEN 'announcement'

        -- Check for update (second priority)
        WHEN EXISTS (
          SELECT 1 FROM interactions_media_updates
          WHERE interactions_media_updates.media_id = interactions_medias.id
        ) THEN 'update'

        -- Check for email (third priority)
        WHEN EXISTS (
          SELECT 1 FROM comms_emails
          WHERE comms_emails.media_id = interactions_medias.id
        ) THEN 'email'

        -- Check for LinkedIn social post (fourth priority)
        WHEN EXISTS (
          SELECT 1 FROM interactions_social_posts
          WHERE interactions_social_posts.media_id = interactions_medias.id
          AND interactions_social_posts.platform = 'linkedin'
        ) THEN 'linkedin'

        -- Check for Twitter social post (fifth priority)
        WHEN EXISTS (
          SELECT 1 FROM interactions_social_posts
          WHERE interactions_social_posts.media_id = interactions_medias.id
          AND interactions_social_posts.platform = 'twitter'
        ) THEN 'twitter'

        -- Fallback to enabled flags if no actual distributions exist
        WHEN distribution_announcement_enabled THEN 'announcement'
        WHEN distribution_update_enabled THEN 'update'
        WHEN distribution_email_enabled THEN 'email'
        WHEN distribution_linkedin_enabled THEN 'linkedin'
        WHEN distribution_twitter_enabled THEN 'twitter'

        ELSE NULL
      END
    )
    """)
  end

  def down do

  end
end
