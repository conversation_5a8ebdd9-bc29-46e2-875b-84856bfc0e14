defmodule Gaia.Repo.Migrations.AddCachedStatsToMedias do
  use Ecto.Migration

  def change do
    alter table(:interactions_medias) do
      add :cached_stats_total_impressions, :integer, default: 0
      add :cached_stats, :map
      add :cached_stats_last_updated, :utc_datetime
    end

    # Add index for sorting by impressions
    create index(:interactions_medias, [:cached_stats_total_impressions])
  end
end
