defmodule Gaia.Repo.Migrations.AddTargetDateToMedias do
  use Ecto.Migration

  def change do
    alter table(:interactions_medias) do
      add :target_date, :utc_datetime
    end

    # Create an index for better performance
    create index(:interactions_medias, [:target_date])

    # Execute migration function to convert existing data
    execute """
    UPDATE interactions_medias
    SET target_date = (
      CASE
        WHEN content_calendar_date IS NOT NULL
        THEN (content_calendar_date || ' 00:00:00')::timestamp at time zone 'UTC'
        ELSE NULL
      END
    )
    """
  end
end
