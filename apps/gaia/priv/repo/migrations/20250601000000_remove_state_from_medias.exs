defmodule Gaia.Repo.Migrations.RemoveStateFromMedias do
  use Ecto.Migration

  def up do
    # Remove the state column
    alter table(:interactions_medias) do
      remove :state
    end
  end

  def down do
    # Add the state column back
    alter table(:interactions_medias) do
      add :state, :string
    end

    # Restore state values based on primary_distribution and associated data
    execute """
    UPDATE interactions_medias m
    SET state =
      CASE
        WHEN EXISTS (
          SELECT 1 FROM interactions_media_announcements a WHERE a.media_id = m.id AND a.posted_at IS NOT NULL
        ) OR EXISTS (
          SELECT 1 FROM interactions_media_updates u WHERE u.media_id = m.id AND u.is_draft = false
        ) OR EXISTS (
          SELECT 1 FROM comms_emails e WHERE e.media_id = m.id AND e.sent_at IS NOT NULL
        ) OR EXISTS (
          SELECT 1 FROM interactions_social_posts sp WHERE sp.media_id = m.id AND sp.published_at IS NOT NULL
        ) THEN 'published'
        WHEN m.target_date IS NOT NULL AND m.target_date > NOW() THEN 'scheduled'
        ELSE 'draft'
      END
    """
  end
end
