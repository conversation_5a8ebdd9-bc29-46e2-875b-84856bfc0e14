defmodule Gaia.Repo.Migrations.RemoveCalendarFieldsFromMedias do
  use Ecto.Migration

  def change do
    # Make sure all data has been migrated to target_date before removing columns
    execute """
    UPDATE interactions_medias
    SET target_date = (
      CASE
        WHEN content_calendar_date IS NOT NULL
        THEN (content_calendar_date || ' 00:00:00')::timestamp at time zone 'UTC'
        ELSE NULL
      END
    )
    WHERE target_date IS NULL AND content_calendar_date IS NOT NULL
    """

    # Remove the old columns
    alter table(:interactions_medias) do
      remove :content_calendar_date
      remove :content_calendar_position
    end
  end
end
