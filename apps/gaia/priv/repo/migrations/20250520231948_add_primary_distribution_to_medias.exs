defmodule Gaia.Repo.Migrations.AddPrimaryDistributionToMedias do
  use Ecto.Migration

  def up do
    # Add the primary_distribution column
    alter table(:interactions_medias) do
      add :primary_distribution, :string, comment: "The primary distribution type for this media item (e.g., 'announcement', 'update', 'email', 'linkedin', 'twitter')"
    end

    # Update existing media items with primary_distribution based on priority:
    # 1. announcement
    # 2. update
    # 3. email
    # 4. linkedin
    # 5. twitter

    # First, set primary_distribution to 'announcement' for media items with an announcement
    execute """
    UPDATE interactions_medias
    SET primary_distribution = 'announcement'
    FROM interactions_media_announcements
    WHERE interactions_medias.id = interactions_media_announcements.media_id
    """

    # Next, set primary_distribution to 'update' for media items with an update (if not already set)
    execute """
    UPDATE interactions_medias
    SET primary_distribution = 'update'
    FROM interactions_media_updates
    WHERE interactions_medias.id = interactions_media_updates.media_id
    AND interactions_medias.primary_distribution IS NULL
    """

    # Next, set primary_distribution to 'email' for media items with an email (if not already set)
    execute """
    UPDATE interactions_medias
    SET primary_distribution = 'email'
    FROM comms_emails
    WHERE interactions_medias.id = comms_emails.media_id
    AND interactions_medias.primary_distribution IS NULL
    """

    # Next, set primary_distribution to 'linkedin' for media items with a LinkedIn social post (if not already set)
    execute """
    UPDATE interactions_medias
    SET primary_distribution = 'linkedin'
    FROM interactions_social_posts
    WHERE interactions_medias.id = interactions_social_posts.media_id
    AND interactions_social_posts.platform = 'linkedin'
    AND interactions_medias.primary_distribution IS NULL
    """

    # Finally, set primary_distribution to 'twitter' for media items with a Twitter social post (if not already set)
    execute """
    UPDATE interactions_medias
    SET primary_distribution = 'twitter'
    FROM interactions_social_posts
    WHERE interactions_medias.id = interactions_social_posts.media_id
    AND interactions_social_posts.platform = 'twitter'
    AND interactions_medias.primary_distribution IS NULL
    """

    # For any remaining media items without a primary_distribution, set based on enabled flags
    execute """
    UPDATE interactions_medias
    SET primary_distribution =
      CASE
        WHEN distribution_announcement_enabled THEN 'announcement'
        WHEN distribution_update_enabled THEN 'update'
        WHEN distribution_email_enabled THEN 'email'
        WHEN distribution_linkedin_enabled THEN 'linkedin'
        WHEN distribution_twitter_enabled THEN 'twitter'
        ELSE NULL
      END
    WHERE primary_distribution IS NULL
    """
  end

  def down do
    alter table(:interactions_medias) do
      remove :primary_distribution
    end
  end
end
