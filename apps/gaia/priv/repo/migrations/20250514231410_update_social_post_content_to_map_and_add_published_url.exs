defmodule Gaia.Repo.Migrations.UpdateSocialPostContentToMapAndAddPublishedUrl do
  use Ecto.Migration

  def change do
    # First add the published_url field
    alter table(:interactions_social_posts) do
      add :published_url, :string
    end

    # Then execute raw SQL to convert content from text to jsonb
    execute """
    ALTER TABLE interactions_social_posts
    ALTER COLUMN content TYPE jsonb
    USING CASE
      WHEN content IS NULL THEN NULL
      WHEN content = '' THEN '{}'::jsonb
      ELSE json_build_object(
        'type', 'doc',
        'content', json_build_array(
          json_build_object(
            'type', 'paragraph',
            'content', json_build_array(
              json_build_object(
                'type', 'text',
                'text', content
              )
            )
          )
        )
      )::jsonb
    END
    """
  end
end
