defmodule Gaia.Application do
  @moduledoc false
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications

  use Application

  require Ecto.Query
  require Logger

  @impl true
  def start(_type, _args) do
    Logger.add_backend(Sentry.LoggerBackend)

    if Application.get_env(:helper, :runtime_env) in ["development", "test"] do
      save_apps_secrets_as_env_vars()
    end

    if Application.get_env(:helper, :runtime_env) in ["production", "staging"] do
      initialize_ssh_and_gpg_config()
    end

    initialize_ets_named_table()

    children = [
      # Starts a worker by calling: Gaia.Worker.start_link(arg)
      Gaia.Repo,
      {Gaia.Vault,
       ciphers: [
         default: {
           Cloak.Ciphers.AES.GCM,
           tag: "AES.GCM.V1", key: :gaia |> Application.fetch_env!(:vault_key) |> Base.decode64!(), iv_length: 12
         }
       ]},
      {<PERSON><PERSON>, Application.fetch_env!(:gaia, <PERSON><PERSON>)},
      {Phoenix.PubSub, name: Gaia.PubSub},
      FunWithFlags.Supervisor
    ]

    children =
      if Application.get_env(:helper, :runtime_env) in ["production", "staging"] do
        children ++
          [
            {ChromicPDF, chromic_pdf_opts()}
          ]
      else
        children
      end

    topologies = Application.get_env(:libcluster, :topologies)

    children =
      if is_nil(topologies) do
        children
      else
        children ++ [{Cluster.Supervisor, [topologies, [name: Gaia.ClusterSupervisor]]}]
      end

    :telemetry.attach(
      "appsignal-ecto",
      [:gaia, :repo, :query],
      &Appsignal.Ecto.handle_event/4,
      nil
    )

    :telemetry.attach(
      "oban-errors",
      [:oban, :job, :exception],
      &Gaia.ObanHelper.handle_error/4,
      []
    )

    check_migrations_and_start_server(children)
  end

  @ets_named_tables [
    Gaia.MarketData.get_ets_table(),
    AmazonWebService.SES.get_ets_table()
  ]
  defp initialize_ets_named_table do
    Enum.each(@ets_named_tables, &:ets.new(&1, [:named_table, :set, :public]))
  end

  # To calculate size pools: (the number of rns oban queue + rns_high_priority queue) * num_of_instaces + buffer
  defp chromic_pdf_opts do
    [
      no_sandbox: true,
      disable_scripts: true,
      session_pool: [
        checkout_timeout: 60_000,
        max_uses: 1_000,
        size: 25,
        timeout: 90_000
      ],
      # Opt in for raise on exception instead of logging to telemetry and handle the exception on the caller side
      # https://hexdocs.pm/chromic_pdf/ChromicPDF.html#module-debugging-javascript-errors-warnings
      unhandled_runtime_exceptions: :raise
    ]
  end

  defp check_migrations_and_start_server(children) do
    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: Gaia.Supervisor]

    # Don't let app start if migrations are pending
    # Also run the oban job rescue function
    with {:ok, pid} <- Supervisor.start_link(children, opts),
         {:migrations_loaded?, true} <- {:migrations_loaded?, Code.ensure_loaded?(Ecto.Migrator)},
         {:pending_migrations?, false} <-
           {:pending_migrations?,
            Gaia.Repo
            |> Ecto.Migrator.migrations()
            |> Enum.any?(fn {status, _version, _migration} -> status == :down end)},
         {:ok, _, _} <- save_executing_oban_jobs() do
      {:ok, pid}
    else
      {:pending_migrations?, true} ->
        Logger.info("Pending migrations are being run")
        Ecto.Migrator.with_repo(Gaia.Repo, &Ecto.Migrator.run(&1, :up, all: true))
        check_migrations_and_start_server(children)

      {:error, {:already_started, pid}} ->
        {:ok, pid}

      error ->
        error
    end
  end

  defp save_executing_oban_jobs do
    # Oban jobs that are running when the server is cut off due to deployment
    # remain in a "executing" state. Change these to "available" so that can run again.
    Logger.info("Running save executing oban jobs")

    Ecto.Migrator.with_repo(Gaia.Repo, fn _ ->
      Oban.Job
      |> Ecto.Query.where(state: "executing")
      |> Gaia.Repo.all([])
      |> Enum.each(fn job ->
        Oban.cancel_job(job.id)

        # Reschedule in 15 minutes (allow time for old server to shut down so job does not run there and get cut off)
        job.args
        |> Oban.Job.new(
          max_attempts: job.max_attempts,
          meta: job.meta,
          priority: job.priority,
          queue: job.queue,
          schedule_in: 900,
          tags: job.tags,
          worker: job.worker
        )
        |> Oban.insert()
      end)
    end)
  end

  defp save_apps_secrets_as_env_vars do
    case get_master_key() do
      {:ok, master_key} ->
        secrets_file = Application.app_dir(:gaia, "priv/secrets/secrets.yml.enc")
        load_secrets(master_key, secrets_file)

      {:error, reason} ->
        Logger.warning("Failed to get master key: #{inspect(reason)}")
    end
  end

  defp get_master_key do
    case Application.get_env(:helper, :secrets_master_key) do
      nil ->
        case File.read(Application.app_dir(:gaia, "priv/secrets/master.key")) do
          {:ok, key} -> {:ok, key}
          {:error, reason} -> {:error, reason}
        end

      master_key ->
        {:ok, master_key}
    end
  end

  defp load_secrets(master_key, secrets_file) do
    case EncryptedSecrets.read(master_key, secrets_file) do
      {:ok, all_secrets} ->
        set_app_secrets(all_secrets)

      {:error, reason} ->
        Logger.warning("Failed to read encrypted secrets: #{inspect(reason)}")
    end
  end

  defp set_app_secrets(all_secrets) do
    Enum.each(all_secrets, fn {app, secrets} ->
      Enum.each(secrets, fn {key, secret} ->
        Application.put_env(app, key, secret)
      end)
    end)
  end

  # We provide an integration with Link Registry Group, who insists on sharing transaction data
  # with us via Managed File Transfer (MFT). On top of this, the files shared on their MFT server
  # are encrypted at rest with PGP encryption. So, in order to download the files via SFTP and then
  # decrypt those files, we need to configure our server with SSH and GPG public/private key pairs.
  defp initialize_ssh_and_gpg_config do
    Logger.info("Creating SSH + GPG config directories exist...")
    Link.ssh_dir() |> Path.expand() |> File.mkdir!()
    Link.gpg_dir() |> Path.expand() |> File.mkdir!()

    Logger.info("Placing SSH public/private key pair...")
    ssh_public_key_path = Link.ssh_public_key_path()
    ssh_private_key_path = Link.ssh_private_key_path()

    File.write!(ssh_public_key_path, Application.fetch_env!(:link, :ssh_public_key))
    File.chmod!(ssh_public_key_path, 400)
    File.write!(ssh_private_key_path, Application.fetch_env!(:link, :ssh_private_key))
    File.chmod!(ssh_private_key_path, 400)

    Logger.info("Placing GPG public/private key pair...")
    gpg_public_key_path = Link.gpg_public_key_path()
    gpg_private_key_path = Link.gpg_private_key_path()

    File.write!(gpg_public_key_path, Application.fetch_env!(:link, :gpg_public_key))
    File.chmod!(gpg_public_key_path, 400)
    File.write!(gpg_private_key_path, Application.fetch_env!(:link, :gpg_private_key))
    File.chmod!(gpg_private_key_path, 400)

    Logger.info("Importing GPG public/private key pair to gpg CLI...")

    System.shell(
      "gpg --pinentry-mode=loopback --passphrase=#{Link.gpg_private_key_passphrase()} --yes --import #{gpg_private_key_path}"
    )

    System.shell("gpg --import #{gpg_public_key_path}")
  end
end
