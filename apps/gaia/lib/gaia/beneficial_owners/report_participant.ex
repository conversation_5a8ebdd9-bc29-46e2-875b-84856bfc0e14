defmodule Gaia.BeneficialOwners.ReportParticipant do
  @moduledoc """
  Identical to `ReportCandidate`, except candidates are meant to be temporary
  Also not associated with an `Account` or `Holding`

  Used to be returned to <PERSON> in the actual ownership report

  Created when a report is "synced" aka published, from a copy of each candidate
  """
  use Ecto.Schema

  import Ecto.Changeset

  @fields [
    :report_id,
    :address_line_one,
    :address_line_two,
    :address_city,
    :address_state,
    :address_postcode,
    :address_country,
    :shareholding_id,
    :account_name,
    :shares,
    :layer,
    :candidate_id,
    :parent_id,
    :last_contact_at,
    :nominee_contact_id,
    :type,
    :status,
    :last_report_participant_id,
    :beneficial_owner_account_id,
    :asic_member_id,
    :asic_organisation_id
  ]

  @required [:report_id]

  @types [:nominee, :asic, :owner]
  @statuses [:pending, :done, :failed]
  def types, do: @types
  def statuses, do: @statuses

  schema "beneficial_owner_report_participants" do
    belongs_to :report, Gaia.BeneficialOwners.Report
    belongs_to :shareholding, Gaia.Registers.Shareholding
    belongs_to :parent, Gaia.BeneficialOwners.ReportParticipant
    has_many :children, Gaia.BeneficialOwners.ReportParticipant, foreign_key: :parent_id
    belongs_to :nominee_contact, Gaia.BeneficialOwners.NomineeContact
    belongs_to :asic_member, Gaia.BeneficialOwners.AsicMember
    belongs_to :last_report_participant, Gaia.BeneficialOwners.ReportParticipant
    belongs_to :asic_organisation, Gaia.BeneficialOwners.AsicOrganisation
    belongs_to :candidate, Gaia.BeneficialOwners.ReportCandidate
    belongs_to :beneficial_owner_account, Gaia.BeneficialOwners.Account
    has_one :contact, through: [:beneficial_owner_account, :contact]

    field :account_name, :string
    field :shares, :integer, default: 0
    field :address_line_one, :string
    field :address_line_two, :string
    field :address_city, :string
    field :address_state, :string
    field :address_postcode, :string
    field :address_country, :string
    field :layer, :integer
    field :last_contact_at, :naive_datetime
    field :type, Ecto.Enum, values: @types, default: :owner
    field :status, Ecto.Enum, values: @statuses, default: :pending

    timestamps()
  end

  @doc false
  def changeset(report_participant, attrs) do
    report_participant
    |> cast(attrs, @fields)
    |> validate_required(@required)
    |> unique_constraint([:report_id, :account_name, :address_line_one, :address_postcode, :parent_id, :layer])
  end
end
