defmodule Gaia.BeneficialOwners.NomineeContact do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query, warn: false

  alias Gaia.Repo

  @required [:account_name]
  @fields [
    :account_name,
    :email,
    :cc_emails,
    :contact_name,
    :alias_names,
    :invalidated,
    :address_line_one,
    :address_line_two,
    :address_city,
    :address_state,
    :address_postcode,
    :address_country,
    :notes,
    :similar_contacts,
    :encrypted_password,
    :custom_prompt,
    :invalidated
  ]

  schema "beneficial_owners_nominee_contacts" do
    field :account_name, :string
    field :email, :string
    field :cc_emails, {:array, :string}, default: []
    field :contact_name, :string
    field :address_line_one, :string
    field :address_line_two, :string
    field :address_city, :string
    field :address_state, :string
    field :address_postcode, :string
    field :address_country, :string
    field :alias_names, {:array, :string}
    field :notes, :string
    field :invalidated, :boolean, default: false
    field :similar_contacts, :any, virtual: true
    field :encrypted_password, :string
    field :custom_prompt, :string
    timestamps()
  end

  @doc false
  def changeset(nominee_contact, attrs) do
    nominee_contact
    |> cast(attrs, @fields)
    |> validate_required(@required)
    |> validate_account_name_alias_uniqueness()
    |> unique_constraint([:account_name],
      name: :uniq_beneficial_owners_nominee_contacts_constraint
    )
  end

  defp validate_account_name_alias_uniqueness(changeset) do
    account_name = get_change(changeset, :account_name)
    alias_names = get_change(changeset, :alias_names) || []

    account_name_conflict =
      __MODULE__
      |> where([nc], fragment("? = ANY(?)", ^account_name, nc.alias_names))
      |> select([nc], %{id: nc.id, account_name: nc.account_name})
      |> limit(1)
      |> Repo.one()

    changeset =
      if account_name_conflict do
        add_error(
          changeset,
          :account_name,
          "is already an alias on nominee id: #{account_name_conflict.id}"
        )
      else
        changeset
      end

    alias_names_conflicts =
      __MODULE__
      |> where([nc], fragment("? = ANY(?)", nc.account_name, ^alias_names))
      |> select([nc], %{id: nc.id, account_name: nc.account_name})
      |> Repo.all()
      |> Enum.reduce(%{}, fn %{id: id, account_name: name}, acc ->
        Map.put(acc, name, id)
      end)

    alias_names
    |> Enum.with_index()
    |> Enum.reduce(changeset, fn {alias, idx}, cs ->
      case Map.get(alias_names_conflicts, alias) do
        nil ->
          cs

        conflict_id ->
          add_error(
            cs,
            :alias_names,
            "Name #{alias} is already the account name for nominee id: #{conflict_id}",
            index: idx
          )
      end
    end)
  end
end
