defmodule Gaia.BeneficialOwners.AsicParser do
  @moduledoc """
  Parses ASIC Company Extract PDFs into structured data for insertion.
  """

  def parse_pdf(binary) do
    first_page = pdf_to_text_first_page(binary)

    {full_org_name, simple_org_name, acn, chunk_to_remove} = extra_initial_details(binary)

    text = binary |> pdf_to_text() |> clean_doc(chunk_to_remove, simple_org_name, acn)

    # Only for debugging purposes
    # Save raw output to tmp/debug_asic_output.txt for inspection
    # File.write!("/tmp/debug_asic_output_raw.txt", pdf_to_text(binary))
    # File.write!("/tmp/debug_asic_output.txt", text)

    share_structures = parse_share_structures(text)
    share_classes = Enum.map(share_structures, & &1.class)

    %{
      organisation: parse_organisation(text, first_page, full_org_name, acn),
      share_structures: share_structures,
      members: parse_members(text, share_classes)
    }
  end

  defp pdf_to_text(binary) do
    case System.cmd("pdftotext", [binary, "-", "-f", "2", "-nodiag"]) do
      {text, 0} -> text
      {_, _} -> :error
    end
  end

  defp pdf_to_text_first_page(binary) do
    case System.cmd("pdftotext", [binary, "-", "-l", "1", "-nodiag"]) do
      {text, 0} -> text
      {_, _} -> :error
    end
  end

  defp extra_initial_details(binary) do
    list =
      binary
      |> pdf_to_text()
      |> String.split("\n", trim: true)

    acn_index =
      Enum.find_index(list, fn item -> String.starts_with?(item, "ACN") end)

    acn = Enum.at(list, acn_index)

    name_parts =
      list |> Enum.slice(0, acn_index) |> List.delete("Current Company Extract")

    # Get all elements before that index
    full_org_name = name_parts |> Enum.join(" ") |> String.trim()
    simple_org_name = name_parts |> Enum.at(0) |> String.trim()

    chunk_to_remove = if Enum.count(name_parts) > 1, do: List.last(name_parts)

    {full_org_name, simple_org_name, acn, chunk_to_remove}
  end

  defp parse_organisation(text, first_page, full_name, acn) do
    expiration_date = get_expiration_date(first_page)

    acn =
      acn
      |> String.replace("ACN ", "")
      |> String.trim()

    block =
      case Regex.run(~r/Current Organisation Details\s*(.*?)\s*Address Details/s, text) do
        [_, body] -> String.trim(body)
        _ -> ""
      end

    lines =
      block
      |> String.split("\n", trim: true)
      |> Enum.reject(&(&1 == ""))

    has_abn = Enum.any?(lines, &String.contains?(&1, "ABN:"))

    has_previous_state_number =
      Enum.any?(lines, &String.contains?(&1, "Previous state number:"))

    values =
      lines
      |> Enum.reject(
        &(&1 in [
            "Name:",
            "ACN:",
            "ABN:",
            "Registered in:",
            "Registration date:",
            "Next review date:",
            "Name start date:",
            "Previous state number:",
            "Status:",
            "Company type:",
            "Class:",
            "Subclass:"
          ])
      )
      |> then(fn v ->
        List.insert_at(v, 1, acn)
      end)
      |> then(fn v ->
        if has_abn do
          v
        else
          List.insert_at(v, 2, nil)
        end
      end)
      |> then(fn v ->
        if has_previous_state_number do
          v
        else
          List.insert_at(v, 7, nil)
        end
      end)

    [
      :company_name,
      :acn,
      :abn,
      :registered_in,
      :registration_date,
      :next_review_date,
      :name_start_date,
      :previous_state_number,
      :status,
      :company_type,
      :class,
      :subclass
    ]
    |> Enum.zip(values)
    |> Map.new()
    |> Map.replace!(:company_name, full_name)
    |> Map.update!(:registration_date, &extract_date_from_string/1)
    |> Map.update!(:next_review_date, &extract_date_from_string/1)
    |> Map.update!(:name_start_date, &extract_date_from_string/1)
    |> Map.put(:extract_expiry_date, expiration_date)
  end

  defp parse_share_structures(text) do
    block =
      case Regex.run(~r/Share Structure\s*(.*?)\s*Members/s, text) do
        [_, body] -> String.trim(body)
        _ -> ""
      end

    block
    |> String.replace(~r/([A-Z]{5,})\n([A-Z]{5,})/, "\\1 \\2")
    |> String.replace("\nSHARES", "SHARES")
    |> String.split("\n", trim: true)
    |> Enum.filter(fn line ->
      line != "" and is_binary(line)
    end)
    |> Enum.drop(10)
    |> Enum.reduce([], fn
      "PREFERENCE SHARES", [prev | rest] -> [prev <> " PREFERENCE SHARES" | rest]
      line, acc -> [line | acc]
    end)
    |> Enum.reverse()
    |> Enum.chunk_every(6)
    |> Enum.filter(fn chunk ->
      length(chunk) == 6 and
        Enum.all?(chunk, fn val -> is_binary(val) end) and
        is_numeric(Enum.at(chunk, 2))
    end)
    |> Enum.map(fn
      [class, description, issued, _paid, _unpaid, doc] ->
        %{
          class: class,
          description: description,
          number_issued: parse_int(issued),
          document_number: doc
        }
    end)
  end

  defp get_member_text(text, attempt \\ 1) do
    patterns = [
      ~r/ceased to be a member of the company.\s*(.*?)\s*Financial Reports/s,
      ~r/ceased to be a member of the company.\s*(.*?)\s*Documents/s,
      ~r/ceased to be a member of the company.\s*(.*?)\s*Note: Where/s,
      ~r/ceased to be a member of the company.\s*(.*?)\s*\*\*\*End of Extract/s
    ]

    pattern = Enum.at(patterns, attempt - 1)

    if is_nil(pattern) and attempt > Enum.count(patterns) do
      :error
    else
      case Regex.run(pattern, text) do
        [_, body] -> String.trim(body)
        _ -> get_member_text(text, attempt + 1)
      end
    end
  end

  # Fallback for member block extraction when default patterns fail
  defp fallback_member_block(text) do
    case Regex.run(
           ~r/Members\s*(.*?)\s*(Financial Reports|Documents|Note:|\*\*\*End of Extract|$)/s,
           text
         ) do
      [_, body | _] -> String.trim(body)
      _ -> ""
    end
  end

  defp parse_members(text, share_classes) do
    # Extract member block, fallback if 'ceased to be a member' patterns not found
    block =
      case get_member_text(text) do
        :error -> fallback_member_block(text)
        block -> block
      end

    {lines, _} = block |> split_lines_and_clean() |> process_joint_members(share_classes)

    {lines, acn_map} = process_acn_map(lines)

    lines
    |> split_into_members(share_classes)
    |> group_and_merge_members(acn_map)
  end

  defp split_lines_and_clean(block) do
    block
    |> String.replace(",\n", ",")
    |> String.split("\n", trim: true)
    |> Enum.reject(fn line ->
      # drop empty and standard header lines
      # also drop any exact spill-over header line 'PTY. LTD.'
      line in [
        "",
        "ACN:",
        "Org No.:",
        "Name:",
        "Address:",
        "Class",
        "Number held",
        "Beneficially held",
        "Paid",
        "Document number"
      ] or
        String.match?(line, ~r/^PTY\. LTD\.$/)
    end)
  end

  # Safely parse strings to Decimal, defaulting to zero on error
  defp safe_parse_decimal(str) when is_binary(str) do
    case Decimal.parse(str) do
      {dec, ""} -> Decimal.round(dec, 4)
      {dec, _} -> Decimal.round(dec, 4)
      _ -> Decimal.new(0)
    end
  end

  # Preprocess lines to join multi-line addresses before chunking
  defp preprocess_multiline_addresses(lines, share_classes) do
    Enum.reduce(lines, [], fn line, acc ->
      cond do
        acc == [] ->
          [line]

        should_join_address_line_precise?(List.last(acc), line, share_classes) ->
          List.update_at(acc, -1, fn prev -> prev <> " " <> line end)

        true ->
          acc ++ [line]
      end
    end)
  end

  defp should_join_address_line_precise?(prev, line, share_classes) do
    is_address_line = String.contains?(prev, ",") or String.contains?(prev, "Address:")

    not Enum.member?(share_classes, line) and
      not is_share_field(line) and
      is_address_line
  end

  defp is_share_field(line) do
    # Looks like a number held, yes/no, paid, or document number
    String.match?(line, ~r/^\d+(\.\d+)?$/) or
      line in ["yes", "no", "FULLY"] or
      String.match?(line, ~r/^[A-Z0-9]{6,}$/)
  end

  defp split_into_members(lines, share_classes) do
    lines
    |> preprocess_multiline_addresses(share_classes)
    |> Enum.chunk_every(7)
    |> Enum.map(fn chunk ->
      [name, address, share_class, held, beneficially_held, _paid, doc] = chunk

      %{
        address_line_one: line_one,
        address_line_two: line_two,
        address_city: city,
        address_state: state,
        address_postcode: postcode,
        address_country: country
      } =
        parse_address(address)

      %{
        name: name,
        address_line_one: line_one,
        address_line_two: line_two,
        address_city: city,
        address_state: state,
        address_postcode: postcode,
        address_country: country,
        shares: [
          %{
            share_class: share_class,
            number_held: safe_parse_decimal(held),
            beneficially_held: beneficially_held == "yes",
            document_number: doc
          }
        ]
      }
    end)
  end

  defp group_and_merge_members(members, acn_map) do
    # Group by name, address_line_one, address_city (to combine repeated members)
    members
    |> Enum.group_by(&{&1.name, &1.address_line_one, &1.address_city})
    |> Enum.map(fn {_key, members} ->
      first = hd(members)
      # Merge shares from all grouped members
      member =
        first
        |> Map.drop([:shares])
        |> Map.put(
          :shares,
          members
          |> Enum.flat_map(& &1.shares)
          |> Enum.group_by(
            fn share ->
              {share.share_class, share.document_number, share.beneficially_held}
            end,
            & &1.number_held
          )
          |> Enum.map(fn {{share_class, document_number, beneficially_held}, number_helds} ->
            %{
              share_class: share_class,
              document_number: document_number,
              beneficially_held: beneficially_held,
              number_held: number_helds |> Enum.reduce(Decimal.new(0), &Decimal.add/2) |> Decimal.round(4)
            }
          end)
        )

      # Add ACN if present
      case Map.fetch(acn_map, member.name) do
        {:ok, acn} -> Map.put(member, :acn, acn)
        :error -> member
      end
    end)
  end

  defp process_acn_map(lines) do
    lines
    |> Enum.reduce({[], %{}}, fn line, {acc, map} ->
      if Regex.match?(~r/^\d{3} \d{3} \d{3}$/, line) and acc != [] do
        [prev | rest] = acc
        {[prev | rest], Map.put(map, prev, line)}
      else
        {[line | acc], map}
      end
    end)
    |> then(fn {lines, map} -> {Enum.reverse(lines), map} end)
  end

  defp process_joint_members(lines, share_classes) do
    case Enum.split_while(lines, &(&1 != "Joint members")) do
      {before, ["Joint members" | rest]} ->
        case parse_joint_member_section(rest, share_classes) do
          {members, remaining} ->
            {processed_remaining, _has_more} = process_joint_members(remaining, share_classes)
            {before ++ members ++ processed_remaining, true}
        end

      _ ->
        {lines, false}
    end
  end

  defp parse_joint_member_section(lines, share_classes) do
    _example_inputs = """
    ["RICHARD JOHN BRENNAN GERAHTY", "333 Bronte Road, BRONTE NSW 2024",
    "SUSAN MARGARET LOUGHNAN GERAHTY", "333 Bronte Road, BRONTE NSW 2024",
    "SARAH LOUISE LOUGHNAN JUDD", "7 Stan Street, WILLOUGHBY EAST NSW 2068", "ORD",
    "149000", "no", "FULLY", "9E0023141"]

    ["Name: ADAM VICTOR SMITH", "77 Kalakau Avenue, FORRESTERS BEACH NSW 2260",
    "MATTHEW HOWARD SMITH", "58 Jenner Parade, HAMILTON SOUTH NSW 2303", "ORD",
    "1", "no", "FULLY", "6EEXW8992", "Joint members", "ADAM VICTOR SMITH",
    "77 Kalakau Avenue, FORRESTERS BEACH NSW 2260", "MATTHEW HOWARD SMITH",
    "58 Jenner Parade, HAMILTON SOUTH NSW 2303", "ORD", "1", "yes", "FULLY",
    "6EEXW8992"]

    ["ADAM VICTOR SMITH", "77 Kalakau Avenue, FORRESTERS BEACH NSW 2260",
    "MATTHEW HOWARD SMITH", "58 Jenner Parade, HAMILTON SOUTH NSW 2303", "ORD",
    "1", "yes", "FULLY", "6EEXW8992"]
    """

    # Extract names and addresses until we hit something that looks like share info
    {member_info, share_info} =
      Enum.split_while(lines, fn line ->
        not Enum.member?(share_classes, line) and
          not String.starts_with?(line, "PREFERENCE")
      end)

    # Safely handle share_info; avoid crash if format unexpected
    case share_info do
      # Expected format: [share_class, held, beneficially_held, paid, doc | remaining]
      [share_class, held, beneficially_held, paid, doc | remaining] ->
        # Group names and addresses into pairs
        member_pairs =
          member_info
          |> Enum.map(fn line ->
            case line do
              "Name: " <> name -> name
              line -> line
            end
          end)
          |> Enum.chunk_every(2)

        # Calculate equal shares for each member
        member_count = length(member_pairs)
        held_count = held |> Integer.parse() |> elem(0)
        shared = held_count / member_count

        # Create individual member entries
        members =
          member_pairs
          |> Enum.map(fn [name, addr] ->
            [name, addr, share_class, "#{shared}", beneficially_held, paid, doc]
          end)
          |> List.flatten()

        {members, remaining}

      _ ->
        # If share_info is not in expected format, skip joint member processing
        {[], []}
    end
  end

  defp extract_date_from_string(nil), do: nil

  defp extract_date_from_string("UNKNOWN"), do: nil

  defp extract_date_from_string(str) do
    case String.split(str, "/") do
      [day, month, year] ->
        {day, month, year} =
          {String.to_integer(day), String.to_integer(month), String.to_integer(year)}

        case Date.from_erl({year, month, day}) do
          {:ok, date} -> date
          _ -> nil
        end

      _ ->
        nil
    end
  end

  defp extract_date_from_formatted_string(date_string) do
    # Remove AE{S/D}T and parse the naive datetime
    clean_string = String.replace(date_string, "AEST ", "")
    format = "%d %B %Y %I:%M:%S %p"

    naive_dt = Timex.parse!(clean_string, format, :strftime)
    naive_dt |> Timex.to_datetime("Australia/Sydney") |> Timex.to_date()
  rescue
    _ ->
      nil
  end

  defp is_numeric(str) do
    case Integer.parse(str) do
      {_, _} -> true
      _ -> false
    end
  end

  defp parse_int(str) do
    case Integer.parse(str) do
      {num, _} -> num
      _ -> 0
    end
  end

  defp get_expiration_date(first_page) do
    case Regex.run(~r/Date\/Time:\s*(.*?)\s*This extract contains/, first_page) do
      [_, date_str] ->
        date_str
        |> String.trim()
        |> extract_date_from_formatted_string()
        |> case do
          nil -> nil
          date -> Date.add(date, 365)
        end

      _ ->
        nil
    end
  end

  defp parse_address(raw_address) do
    case Regex.run(~r/^(.*),\s*(.+?)\s+([A-Z]{2,3})\s+(\d{4})$/, raw_address) do
      [_original, line_one, city, state, postcode] ->
        [address_line_one | rest] = String.split(line_one, ",")
        address_line_two = if List.first(rest), do: rest |> Enum.join(",") |> String.trim()

        %{
          address_line_one: address_line_one,
          address_line_two: address_line_two,
          address_city: city,
          address_state: state,
          address_postcode: postcode,
          address_country: "Australia"
        }

      _ ->
        %{
          address_line_one: raw_address,
          address_line_two: nil,
          address_city: nil,
          address_state: nil,
          address_postcode: nil,
          address_country: nil
        }
    end
  end

  defp clean_doc(text, name_extension, name, acn) do
    # Remove the page header rows
    fragments =
      text
      |> String.split("\n")
      |> Enum.with_index()
      |> Enum.reject(fn {line, _index} ->
        String.trim(line) == "" or
          String.trim(line) == name_extension or
          String.trim(line) == name or String.trim(line) == acn or String.trim(line) == "Current Company Extract"
      end)
      |> Enum.map(fn {line, _index} -> String.trim(line) end)

    # Get list of the indexes of the lines that are dates
    # Used to filter out pages footers
    date_indexes =
      fragments
      |> Enum.with_index()
      |> Enum.filter(fn {line, _index} ->
        String.match?(line, ~r/^\d{2} \w+ \d{4} AE[SD]T \d{2}:\d{2}:\d{2} (AM|PM)$/i)
      end)

    # Remove the footer rows (a date on the left, page number on the right)
    fragments
    |> Enum.with_index()
    |> Enum.reject(fn {line, index} ->
      Enum.any?(date_indexes, fn {_, date_index} ->
        date_index == index or (date_index + 1 == index and String.match?(line, ~r/^\d+$/))
      end)
    end)
    |> Enum.map_join("\n", fn {line, _index} -> String.trim(line) end)
  end
end
