defmodule Gaia.BeneficialOwners.Report do
  @moduledoc """
    Benefitial Owner Report Schema

    It holds the information about the beneficial owner reports for a given company
  """

  use Ecto.Schema

  import Ecto.Changeset

  alias Gaia.BeneficialOwners.ReportDetail
  alias Gaia.BeneficialOwners.ReportParticipant

  @required [
    :company_profile_id,
    :type,
    :report_date
  ]

  @optional [
    :is_user_uploaded,
    :previous_report_date,
    :report_date,
    :disclosed_interest_document_uploaded_at,
    :disclosed_interest_document_filename,
    :disclosed_interest_document_url,
    :sync_at,
    :stage,
    :is_past,
    :total_shares
  ]

  @stage_types [
    :processing_stage_1,
    :processing_stage_2,
    :done
  ]

  # importing -> state after a user requests a BO report
  # processing -> state after a user uploads a BO report
  # completed -> either a processing or importing BO report that has been manually cleaned and ingested by our system
  @report_types [:processing, :importing, :completed]

  schema "beneficial_owner_reports" do
    field :is_user_uploaded, :boolean, default: false
    field :is_past, :boolean, default: false
    field :previous_report_date, :date
    field :report_date, :date
    field :type, Ecto.Enum, values: @report_types, default: :importing
    field :stage, Ecto.Enum, values: @stage_types, default: :processing_stage_1
    field :total_shares, :integer

    # UK compliance fields
    field :disclosed_interest_document_uploaded_at, :naive_datetime
    field :disclosed_interest_document_filename, :string
    field :disclosed_interest_document_url, :string
    field :sync_at, :naive_datetime
    belongs_to :company_profile, Gaia.Companies.Profile

    has_many :report_details, ReportDetail, foreign_key: :beneficial_owner_report_id
    has_many :participants, ReportParticipant, foreign_key: :report_id

    has_many :shareholdings, through: [:participants, :shareholding]
    has_many :contacts, through: [:participants, :shareholding, :contact]

    has_many :layer_one_participants, ReportParticipant,
      foreign_key: :report_id,
      where: [parent_id: nil, layer: 1]

    field :report_details_count, :integer, virtual: true

    timestamps()
  end

  @doc false
  def changeset(report, attrs) do
    report
    |> cast(attrs, @required ++ @optional)
    |> put_default_report_date()
    |> validate_required(@required)
    |> foreign_key_constraint(:company_profile_id)
  end

  defp put_default_report_date(changeset) do
    case get_field(changeset, :report_date) do
      nil ->
        put_change(
          changeset,
          :report_date,
          changeset |> get_field(:inserted_at) |> maybe_get_report_date_from_timestamps()
        )

      _ ->
        changeset
    end
  end

  defp maybe_get_report_date_from_timestamps(%NaiveDateTime{} = inserted_at), do: NaiveDateTime.to_date(inserted_at)

  defp maybe_get_report_date_from_timestamps(nil), do: Helper.ExDay.now_date()

  def get_report_types do
    @report_types
  end

  def get_stage_types do
    @stage_types
  end

  @doc """
  Create a new report

  ## Examples

        iex> Gaia.BeneficialOwners.Report.get_report_date_from_csv(["Holding Individual", "33,480,799", "individual", "TRUE", "TRUE", "999", "516537", "Joao Silva", "555", "Rio", "RJ", "Brazil", "Investor Manager", "777", "São Paulo", "SP", "Brazil", "11,111,111", "22,222,222", "22/11/2024"])
        ~N[2024-11-22 00:00:00]

  """
  @spec get_report_date_from_csv([String.t()]) :: DateTime.t() | NaiveDateTime.t() | no_return
  def get_report_date_from_csv(line) do
    line
    |> Enum.at(-1)
    |> Timex.parse!("{0D}/{0M}/{YYYY}")
  end
end
