defmodule Gaia.BeneficialOwners.ReportCandidate do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset

  @fields [
    :report_id,
    :address_line_one,
    :address_line_two,
    :address_city,
    :address_state,
    :address_postcode,
    :address_country,
    :shareholding_id,
    :account_name,
    :shares,
    :layer,
    :parent_id,
    :last_contact_at,
    :nominee_contact_id,
    :type,
    :status,
    :last_report_candidate_id,
    :last_report_candidate_shares,
    :asic_member_id,
    :asic_organisation_id
  ]

  @required [:report_id]

  @types [:nominee, :asic, :owner]
  @statuses [:pending, :done, :failed, :email]
  def types, do: @types
  def statuses, do: @statuses

  schema "beneficial_owner_report_candidates" do
    belongs_to :report, Gaia.BeneficialOwners.Report
    belongs_to :shareholding, Gaia.Registers.Shareholding
    belongs_to :parent, Gaia.BeneficialOwners.ReportCandidate
    has_many :children, Gaia.BeneficialOwners.ReportCandidate, foreign_key: :parent_id
    belongs_to :nominee_contact, Gaia.BeneficialOwners.NomineeContact
    belongs_to :asic_member, Gaia.BeneficialOwners.AsicMember
    belongs_to :last_report_candidate, Gaia.BeneficialOwners.ReportCandidate
    belongs_to :asic_organisation, Gaia.BeneficialOwners.AsicOrganisation

    field :account_name, :string
    field :shares, :integer, default: 0
    field :last_report_candidate_shares, :integer, default: 0
    field :address_line_one, :string
    field :address_line_two, :string
    field :address_city, :string
    field :address_state, :string
    field :address_postcode, :string
    field :address_country, :string
    field :layer, :integer
    field :last_contact_at, :naive_datetime
    field :type, Ecto.Enum, values: @types, default: :owner
    field :status, Ecto.Enum, values: @statuses, default: :pending

    has_one :account, Gaia.BeneficialOwners.Account,
      foreign_key: :account_name,
      references: :account_name

    has_one :holding, Gaia.BeneficialOwners.Holding, foreign_key: :candidate_id

    timestamps()
  end

  @doc false
  def changeset(report_candidate, attrs) do
    report_candidate
    |> cast(attrs, @fields)
    |> validate_required(@required)
    |> unique_constraint([:report_id, :account_name, :address_line_one, :address_postcode, :parent_id, :layer],
      name: :beneficial_owner_report_candidates_unique_idx
    )
  end
end
