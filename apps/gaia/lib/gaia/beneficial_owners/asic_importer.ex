defmodule Gaia.BeneficialOwners.AsicImporter do
  @moduledoc """
  Imports ASIC company extract PDFs.
  """

  import Ecto.Query, warn: false

  alias <PERSON><PERSON>.BeneficialOwners
  alias Gaia.BeneficialOwners.AsicMember
  alias Gaia.BeneficialOwners.AsicMemberShareStructure
  alias Gaia.BeneficialOwners.AsicOrganisation
  alias Gaia.BeneficialOwners.AsicParser
  alias Gaia.BeneficialOwners.AsicShareStructure
  alias Gaia.BeneficialOwners.ReportCandidate
  alias Gaia.Repo

  def process_uploaded_file(path, entry) do
    parsed = path |> AsicParser.parse_pdf() |> add_file_to_parsed_result(path, entry)

    Ecto.Multi.new()
    # Upsert organisation by ACN
    |> Ecto.Multi.run(:organisation, fn repo, _changes ->
      upsert_organisation(repo, parsed)
    end)
    # Upsert share_structures
    |> Ecto.Multi.run(:share_structures, fn repo, %{organisation: org} ->
      upsert_share_structures(repo, org, parsed)
    end)
    # Upsert members by name, delete removed
    |> Ecto.Multi.run(:members, fn repo, %{organisation: org, share_structures: share_structures} ->
      upsert_members(repo, org, parsed, share_structures)
    end)
    |> Repo.transaction()
    |> case do
      {:ok, result} -> {:ok, result}
      {:error, _step, reason, _} -> {:postpone, reason}
    end
  end

  def import_and_create_candidates(path, entry, candidate) do
    parsed = path |> AsicParser.parse_pdf() |> add_file_to_parsed_result(path, entry)

    Ecto.Multi.new()
    # Upsert organisation by ACN
    |> Ecto.Multi.run(:organisation, fn repo, _changes ->
      upsert_organisation(repo, parsed)
    end)
    # Upsert share_structures
    |> Ecto.Multi.run(:share_structures, fn repo, %{organisation: org} ->
      upsert_share_structures(repo, org, parsed)
    end)
    # Upsert members by name, delete removed
    |> Ecto.Multi.run(:members, fn repo, %{organisation: org, share_structures: share_structures} ->
      upsert_members(repo, org, parsed, share_structures)
    end)
    |> Ecto.Multi.run(:update_parent, fn repo, %{organisation: org} ->
      candidate
      |> ReportCandidate.changeset(%{status: :done, asic_organisation_id: org.id})
      |> repo.update()
    end)
    |> Ecto.Multi.run(:child_candidates, fn repo,
                                            %{
                                              share_structures: share_structures,
                                              members: members
                                            } ->
      create_child_candidates(repo, candidate, share_structures, members)
    end)
    |> Repo.transaction()
    |> case do
      {:ok, result} -> {:ok, result}
      {:error, _step, reason, _} -> {:postpone, reason}
    end
  end

  def create_candidates_from_existing_asic_members(asic_organisation_id, candidate) do
    org = BeneficialOwners.get_asic_organisation_with_preloads!(asic_organisation_id)

    Ecto.Multi.new()
    |> Ecto.Multi.run(:child_candidates, fn repo, _changes ->
      create_child_candidates(
        repo,
        candidate,
        org.asic_share_structures,
        org.asic_members
      )
    end)
    |> Ecto.Multi.update(
      :update_parent,
      ReportCandidate.changeset(candidate, %{
        status: :done,
        asic_organisation_id: asic_organisation_id
      })
    )
    |> Repo.transaction()
  end

  defp add_file_to_parsed_result(parsed, path, entry) do
    # Add the file to the parsed data
    organisation =
      Map.put(parsed.organisation, :extract_file, %Plug.Upload{
        path: path,
        filename: entry.client_name,
        content_type: entry.client_type
      })

    Map.replace(parsed, :organisation, organisation)
  end

  defp upsert_organisation(repo, parsed) do
    case repo.get_by(AsicOrganisation, acn: parsed.organisation.acn) do
      nil ->
        changeset = AsicOrganisation.changeset(%AsicOrganisation{}, parsed.organisation)
        repo.insert(changeset)

      existing ->
        changeset = AsicOrganisation.changeset(existing, parsed.organisation)
        repo.update(changeset)
    end
  end

  defp upsert_share_structures(repo, org, parsed) do
    existing = repo.all(Ecto.assoc(org, :asic_share_structures))
    incoming = parsed.share_structures

    {updates, inserts} =
      Enum.split_with(incoming, fn inc ->
        Enum.any?(existing, fn e -> e.class == inc.class end)
      end)

    delete_ids =
      Enum.map(Enum.map(existing, & &1.class) -- Enum.map(incoming, & &1.class), fn class ->
        Enum.find(existing, fn s -> s.class == class end).id
      end)

    # delete removed classes
    Enum.each(delete_ids, fn id ->
      repo.delete!(repo.get!(AsicShareStructure, id))
    end)

    # update or insert
    structs =
      Enum.map(updates ++ inserts, fn attrs ->
        existing_struct = Enum.find(existing, fn s -> s.class == attrs.class end)
        base = existing_struct || Ecto.build_assoc(org, :asic_share_structures)
        changeset = AsicShareStructure.changeset(base, attrs)
        {:ok, struct} = repo.insert_or_update(changeset)
        struct
      end)

    {:ok, structs}
  end

  defp upsert_members(repo, org, parsed, share_structures) do
    # Map share class to structure id
    class_to_id =
      Enum.reduce(share_structures, %{}, fn struct, acc ->
        Map.put(acc, struct.class, struct.id)
      end)

    existing = repo.all(Ecto.assoc(org, :asic_members))

    existing_member_share_structures =
      AsicMemberShareStructure
      |> where([amss], amss.asic_member_id in ^Enum.map(existing, & &1.id))
      |> repo.all()

    incoming = parsed.members

    {updates, inserts} =
      Enum.split_with(incoming, fn inc ->
        Enum.any?(existing, fn e ->
          e.name == inc.name and
            e.address_line_one == inc.address_line_one and
            e.address_city == inc.address_city
        end)
      end)

    delete_keys =
      Enum.map(existing, fn e -> {e.name, e.address_line_one, e.address_city} end) --
        Enum.map(incoming, fn inc -> {inc.name, inc.address_line_one, inc.address_city} end)

    delete_ids =
      Enum.map(delete_keys, fn {name, line1, city} ->
        Enum.find(existing, fn m ->
          m.name == name and m.address_line_one == line1 and m.address_city == city
        end).id
      end)

    Enum.each(delete_ids, fn id ->
      repo.delete!(repo.get!(AsicMember, id))
    end)

    # update or insert members and their share structures
    structs =
      Enum.map(updates ++ inserts, fn attrs ->
        # Remove :shares from the member struct for the member table
        member_attrs = Map.drop(attrs, [:shares])

        existing_struct =
          Enum.find(existing, fn m ->
            m.name == attrs.name and
              m.address_line_one == attrs.address_line_one and
              m.address_city == attrs.address_city
          end)

        base = existing_struct || Ecto.build_assoc(org, :asic_members)
        member_changeset = AsicMember.changeset(base, member_attrs)
        {:ok, member} = repo.insert_or_update(member_changeset)

        upsert_asic_member_structures(
          repo,
          attrs.shares,
          member,
          existing_member_share_structures,
          class_to_id
        )

        member
      end)

    {:ok, structs}
  end

  defp upsert_asic_member_structures(repo, shares, member, existing_member_share_structures, class_to_id) do
    # Upsert AsicMemberShareStructure join records for each share holding
    Enum.each(shares, fn share ->
      share_structure_id = Map.get(class_to_id, share.share_class)

      existing_join =
        Enum.find(existing_member_share_structures, fn amss ->
          amss.asic_member_id == member.id and
            amss.asic_share_structure_id == share_structure_id and amss.beneficially_held === share.beneficially_held and
            amss.document_number == share.document_number
        end)

      join_attrs = %{
        asic_member_id: member.id,
        asic_share_structure_id: share_structure_id,
        number_held: share.number_held,
        beneficially_held: share.beneficially_held,
        document_number: share.document_number
      }

      join_changeset =
        if existing_join do
          AsicMemberShareStructure.changeset(existing_join, join_attrs)
        else
          AsicMemberShareStructure.changeset(%AsicMemberShareStructure{}, join_attrs)
        end

      repo.insert_or_update(join_changeset)
    end)
  end

  def create_child_candidates(repo, candidate, share_structures, members) do
    total_all_shares =
      case share_structures do
        [] ->
          Decimal.new("0")

        _ ->
          share_structures
          |> Enum.map(& &1.number_issued)
          |> Enum.map(&Decimal.new/1)
          |> Enum.reduce(&Decimal.add/2)
      end

    members
    |> Enum.map(fn member ->
      candidate =
        member
        |> Gaia.Repo.preload([
          :asic_member_share_structures,
          :share_structures,
          :asic_organisation
        ])
        |> calculate_candidate_attrs(candidate, total_all_shares)

      %ReportCandidate{}
      |> ReportCandidate.changeset(candidate)
      |> repo.insert(
        on_conflict: {:replace, [:shares]},
        conflict_target:
          {:unsafe_fragment,
           "(report_id, account_name, COALESCE(address_line_one, ''), COALESCE(address_postcode, ''), parent_id, layer)"}
      )
    end)
    |> Enum.reduce_while({:ok, []}, fn
      {:ok, candidate}, {:ok, acc} -> {:cont, {:ok, [candidate | acc]}}
      {:error, reason}, _ -> {:halt, {:error, reason}}
    end)
  end

  defp calculate_candidate_attrs(member, candidate, total_all_shares) do
    percent_of_total =
      Enum.reduce(member.share_structures, Decimal.new(0), fn structure, acc ->
        number_issued = Decimal.new(structure.number_issued)

        number_helds =
          member.asic_member_share_structures
          |> Enum.filter(fn join ->
            join.asic_share_structure_id == structure.id
          end)
          |> Enum.map(& &1.number_held)

        held = number_helds |> Enum.reduce(Decimal.new(0), &Decimal.add/2) |> Decimal.round(4)

        part = Decimal.div(held, number_issued)

        structure_ratio =
          if Decimal.equal?(total_all_shares, Decimal.new(0)) do
            Decimal.new(0)
          else
            Decimal.div(number_issued, total_all_shares)
          end

        structure_contrib = Decimal.mult(part, structure_ratio)

        Decimal.add(acc, structure_contrib)
      end)

    parent_shares = Decimal.new(candidate.shares || 0)

    member_shares =
      Decimal.to_integer(Decimal.round(Decimal.mult(parent_shares, percent_of_total), 0))

    layer = candidate.layer + 1

    %{
      account_name: member.name,
      address_line_one: member.address_line_one,
      address_line_two: member.address_line_two,
      address_city: member.address_city,
      address_state: member.address_state,
      address_postcode: member.address_postcode,
      address_country: member.address_country,
      parent_id: candidate.id,
      report_id: candidate.report_id,
      layer: layer,
      type:
        if(Enum.any?(member.asic_member_share_structures, & &1.beneficially_held) || layer == 3,
          do: :owner,
          else: :asic
        ),
      status:
        if(Enum.any?(member.asic_member_share_structures, & &1.beneficially_held) || layer == 3,
          do: :done,
          else: :pending
        ),
      asic_member_id: member.id,
      shares: member_shares
    }
  end
end
