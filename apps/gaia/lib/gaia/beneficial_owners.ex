defmodule Gaia.BeneficialOwners do
  @moduledoc """
    Information about beneficial owners and it's report details
  """

  import Ecto.Query, warn: false
  import Helper.Guard, only: [is_id?: 1]

  alias Gaia.BeneficialOwners
  alias Gaia.BeneficialOwners.Account
  alias <PERSON>aia.BeneficialOwners.AsicOrganisation
  alias Gaia.BeneficialOwners.AuthorisationLetter
  alias Gaia.BeneficialOwners.Holding
  alias Gaia.BeneficialOwners.NomineeContact
  alias Gaia.BeneficialOwners.Report
  alias Gaia.BeneficialOwners.ReportCandidate
  alias Gaia.BeneficialOwners.ReportDetail
  alias Gaia.BeneficialOwners.ReportParticipant
  alias Gaia.Contacts.Contact
  alias Gaia.Repo

  @common_uk_nominees [
    "LAWSHARE NOMINEES LIMITED",
    "BARCLAYS DIRECT INVESTING NOMINEES LIMITED",
    "CHARLES STANLEY & CO. LIMITED",
    "CREDO CAPITAL",
    "EQUINITI GROUP PLC",
    "GHC NOMINEES LIMITED",
    "HSDL NOMINEES LIMITED",
    "HARGREAVES LANSDOWN NOMINEES LIMITED",
    "INTERACTIVE INVESTOR SERVICES NOMINEES LIMITED",
    "INTERACTIVE BROKERS (U.K.) NOMINEE LIMITED",
    "JIM NOMINEES LIMITED",
    "FERLIM NOMINEES LIMITED",
    "REDMAYNE (NOMINEES) LIMITED",
    "PEEL HUNT NOMINEES LIMITED",
    "TRADING 212 UK LIMITED",
    "WINTERFLOOD SECURITIES LIMITED",
    "ETORO",
    "SHARE CAPITAL PARTNERS"
  ]

  def sync_candidates_to_participants(report_id) do
    now = NaiveDateTime.utc_now(:second)

    Ecto.Multi.new()
    |> Ecto.Multi.one(:report, fn _ ->
      where(Report, [r], r.id == ^report_id)
    end)
    |> Ecto.Multi.put(:candidates_with_account, [])
    |> create_participants_with_accounts(report_id, now)
    |> Ecto.Multi.update(
      :update_report_sync_at,
      fn %{
           report: report
         } ->
        Ecto.Changeset.change(report,
          sync_at: NaiveDateTime.utc_now(:second)
        )
      end
    )
    |> Repo.transaction()
  end

  def sync_candidates_to_accounts_and_holdings_and_participants(report_id) do
    now = NaiveDateTime.utc_now(:second)

    Ecto.Multi.new()
    |> Ecto.Multi.one(:report, fn _ ->
      where(Report, [r], r.id == ^report_id)
    end)
    |> Ecto.Multi.all(
      :candidates,
      ReportCandidate
      |> where([rc], rc.report_id == ^report_id)
      |> where([rc], rc.status in [:done, :failed])
      |> where([rc], rc.layer > 1)
      |> preload([:parent, holding: :beneficial_owner_account])
    )
    # |> create_participants(report_id, now)
    |> Ecto.Multi.run(
      :new_account_candidates,
      fn repo,
         %{
           report: report
         } ->
        {:ok,
         ReportCandidate
         |> join(:left, [c], h in Holding, on: c.id == h.candidate_id and h.report_id == ^report.id)
         |> join(:left, [c, h], a in Account, on: h.beneficial_owner_account_id == a.id)
         |> where([c, h, a], is_nil(a.id))
         |> where([c], c.report_id == ^report.id)
         |> where([c], c.status in [:done, :failed])
         |> where([c], c.layer > 1)
         |> repo.all()}
      end
    )
    |> Ecto.Multi.run(
      :new_contacts,
      fn repo,
         %{
           report: report,
           new_account_candidates: new_account_candidates
         } ->
        contact =
          new_account_candidates
          |> Enum.map(fn candidate ->
            %{
              first_name: candidate.account_name,
              inserted_at: now,
              updated_at: now,
              company_profile_id: report.company_profile_id,
              address_line_one: candidate.address_line_one,
              address_line_two: candidate.address_line_two,
              address_city: candidate.address_city,
              address_state: candidate.address_state,
              address_postcode: candidate.address_postcode,
              address_country: candidate.address_country,
              contact_source: :beneficial_owners_import
            }
          end)
          |> Enum.uniq_by(fn contact ->
            {contact.first_name, contact.address_line_one, contact.address_country, contact.address_postcode}
          end)

        {_, result} =
          repo.insert_all(Contact, contact,
            on_conflict: {:replace, [:updated_at]},
            conflict_target:
              {:unsafe_fragment,
               ~s<("company_profile_id", "first_name", COALESCE("address_line_one", ''), COALESCE("address_country", ''), COALESCE("address_postcode", ''), "contact_source") WHERE contact_source = 'beneficial_owners_import'>},
            returning: true
          )

        {:ok, result}
      end
    )
    |> Ecto.Multi.run(
      :new_accounts,
      fn repo,
         %{
           report: report,
           new_contacts: new_contacts,
           new_account_candidates: new_account_candidates
         } ->
        contact_lookup =
          new_contacts
          |> Enum.group_by(fn contact ->
            {contact.first_name, contact.address_line_one, contact.address_country, contact.address_postcode}
          end)
          |> Map.new(fn {key, contacts} -> {key, List.first(contacts)} end)

        new_account_candidates_with_contact_id =
          Enum.map(new_account_candidates, fn candidate ->
            key =
              {candidate.account_name, candidate.address_line_one, candidate.address_country, candidate.address_postcode}

            contact = Map.get(contact_lookup, key)
            Map.put(candidate, :contact_id, contact && contact.id)
          end)

        accounts =
          Enum.map(new_account_candidates_with_contact_id, fn candidate ->
            %{
              account_name: candidate.account_name,
              company_profile_id: report.company_profile_id,
              contact_id: candidate.contact_id,
              address_line_one: candidate.address_line_one,
              address_line_two: candidate.address_line_two,
              address_city: candidate.address_city,
              address_state: candidate.address_state,
              address_postcode: candidate.address_postcode,
              address_country: candidate.address_country,
              inserted_at: now,
              updated_at: now
            }
          end)

        {_, result} = repo.insert_all(Account, accounts, on_conflict: :nothing, returning: true)
        {:ok, result}
      end
    )
    |> Ecto.Multi.run(
      :candidates_with_account,
      fn _repo,
         %{
           candidates: candidates,
           new_accounts: new_accounts
         } ->
        # Assign accounts to report candidates

        candidates_with_account = link_accounts_to_report_candidates(candidates, new_accounts)

        {:ok, candidates_with_account}
      end
    )
    |> create_participants_with_accounts(report_id, now)
    |> Ecto.Multi.run(
      :create_layer_2_holdings,
      fn repo,
         %{
           candidates_with_account: candidates_with_account,
           report: report
         } ->
        layer_2_holding =
          candidates_with_account
          |> Enum.filter(fn candidate -> candidate.layer == 2 end)
          |> Enum.map(fn candidate ->
            %{
              company_profile_id: report.company_profile_id,
              report_id: report.id,
              shareholding_id: candidate.parent.shareholding_id,
              beneficial_owner_account_id: candidate.account.id,
              shares: candidate.shares,
              candidate_id: candidate.id,
              inserted_at: now,
              updated_at: now
            }
          end)

        {_, result} =
          repo.insert_all(Holding, layer_2_holding, on_conflict: :nothing, returning: true)

        {:ok, result}
      end
    )
    |> Ecto.Multi.run(
      :candidates_with_account_and_holding,
      fn repo,
         %{
           candidates_with_account: candidates_with_account
         } ->
        {:ok, repo.preload(candidates_with_account, [:holding, parent: :holding])}
      end
    )
    |> Ecto.Multi.run(
      :create_layer_3_holdings,
      fn repo,
         %{
           candidates_with_account_and_holding: candidates_with_account_and_holding,
           report: report
         } ->
        layer_3_holding =
          candidates_with_account_and_holding
          |> Enum.filter(fn candidate -> candidate.layer == 3 end)
          |> Enum.map(fn candidate ->
            %{
              company_profile_id: report.company_profile_id,
              report_id: report.id,
              parent_id: candidate.parent.holding.id,
              beneficial_owner_account_id: candidate.account.id,
              shares: candidate.shares,
              candidate_id: candidate.id,
              inserted_at: now,
              updated_at: now
            }
          end)

        {_, result} =
          repo.insert_all(Holding, layer_3_holding, on_conflict: :nothing, returning: true)

        {:ok, result}
      end
    )
    |> Ecto.Multi.update(
      :update_report_sync_at,
      fn %{
           report: report
         } ->
        Ecto.Changeset.change(report,
          sync_at: NaiveDateTime.utc_now(:second)
        )
      end
    )
    |> Repo.transaction()
  end

  defp create_participants_with_accounts(multi, report_id, now) do
    multi =
      multi
      |> Ecto.Multi.run(:candidates_for_participants, fn repo,
                                                         %{
                                                           candidates_with_account: candidates_with_account
                                                         } ->
        # Fetch ALL candidates for the report
        all_candidates =
          ReportCandidate
          |> where(
            [rc],
            rc.report_id == ^report_id and not is_nil(rc.status) and not is_nil(rc.type)
          )
          |> repo.all()
          |> repo.preload([:parent, :holding, holding: :beneficial_owner_account])

        # Assign accounts to ALL candidates (account will be nil if not found)
        report_candidates_with_account = link_accounts_to_candidates(all_candidates, candidates_with_account)

        {:ok, report_candidates_with_account}
      end)
      |> Ecto.Multi.run(:last_report_participants, fn _repo, %{report: _report} ->
        {:ok, get_last_report_participants_by_report_id(report_id)}
      end)
      |> Ecto.Multi.delete_all(
        :current_report_participants,
        where(ReportParticipant, [rp], rp.report_id == ^report_id)
      )

    # Create participants for layers 1 through 3
    Enum.reduce(1..3, multi, fn layer, acc_multi ->
      participant_key = String.to_atom("layer_#{layer}_participants")
      previous_layer_key = if layer > 1, do: String.to_atom("layer_#{layer - 1}_participants")

      Ecto.Multi.run(acc_multi, participant_key, fn repo, changes ->
        previous_layer_participants =
          if previous_layer_key, do: Map.get(changes, previous_layer_key)

        create_participants_by_layer_with_accounts(
          layer,
          repo,
          changes.candidates_for_participants,
          changes.last_report_participants,
          previous_layer_participants,
          changes.report,
          now
        )
      end)
    end)
  end

  defp create_participants_by_layer_with_accounts(
         layer,
         repo,
         candidates_with_accounts,
         last_report_participants,
         previous_layer_participants,
         report,
         now
       ) do
    # Create a mapping of candidate IDs to their corresponding participant IDs
    candidate_to_participant =
      map_candidates_to_participants(layer, candidates_with_accounts, previous_layer_participants)

    # Get all candidates for this layer
    layer_candidates = Enum.filter(candidates_with_accounts, &(&1.layer == layer))

    # Create participants for this layer
    participants_to_insert =
      Enum.map(layer_candidates, fn candidate ->
        # Look up the parent's participant ID from our mapping
        parent_participant_id = Map.get(candidate_to_participant, candidate.parent_id)

        last_report_participant =
          Enum.find(
            last_report_participants,
            &((&1.account_name == candidate.account_name and
                 &1.shareholding_id == candidate.shareholding_id) or
                (&1.account_name == candidate.account_name and &1.layer == candidate.layer))
          ) ||
            %{}

        %{
          candidate_id: candidate.id,
          report_id: report.id,
          shareholding_id: candidate.shareholding_id,
          parent_id: parent_participant_id,
          nominee_contact_id: candidate.nominee_contact_id,
          asic_member_id: candidate.asic_member_id,
          asic_organisation_id: candidate.asic_organisation_id,
          last_report_participant_id: Map.get(last_report_participant, :id),
          account_name: candidate.account_name,
          beneficial_owner_account_id: candidate.account && candidate.account.id,
          layer: candidate.layer,
          type: candidate.type,
          status: candidate.status,
          shares: candidate.shares,
          address_line_one: candidate.address_line_one,
          address_line_two: candidate.address_line_two,
          address_city: candidate.address_city,
          address_state: candidate.address_state,
          address_postcode: candidate.address_postcode,
          address_country: candidate.address_country,
          last_contact_at: candidate.last_contact_at,
          inserted_at: now,
          updated_at: now
        }
      end)

    {_, created_participants} =
      repo.insert_all(ReportParticipant, participants_to_insert, returning: true)

    {:ok, created_participants}
  end

  defp link_accounts_to_candidates(all_candidates, candidates_with_account) do
    Enum.map(all_candidates, fn candidate ->
      if candidate.holding && candidate.holding.beneficial_owner_account do
        Map.put(candidate, :account, candidate.holding.beneficial_owner_account)
      else
        account =
          case Enum.find(candidates_with_account, fn candidate_with_account ->
                 candidate_with_account.id == candidate.id
               end) do
            nil -> nil
            found_candidate -> Map.get(found_candidate, :account, nil)
          end

        Map.put(candidate, :account, account)
      end
    end)
  end

  defp link_accounts_to_report_candidates(candidates, new_accounts) do
    {candidates_with_account, _} =
      Enum.map_reduce(candidates, [], fn candidate, accounts_taken ->
        if candidate.holding && candidate.holding.beneficial_owner_account do
          {Map.put(candidate, :account, candidate.holding.beneficial_owner_account), accounts_taken}
        else
          account =
            Enum.find(new_accounts, fn account ->
              account.account_name == candidate.account_name and
                account.address_line_one == candidate.address_line_one and
                account.address_country == candidate.address_country and
                account.address_postcode == candidate.address_postcode and
                account.id not in accounts_taken
            end)

          if account do
            {Map.put(candidate, :account, account), [account.id | accounts_taken]}
          else
            {Map.put(candidate, :account, nil), accounts_taken}
          end
        end
      end)

    candidates_with_account
  end

  defp map_candidates_to_participants(_, _, nil), do: %{}

  defp map_candidates_to_participants(layer, candidates, participants) do
    Enum.reduce(participants, %{}, fn participant, acc ->
      case Enum.find(
             candidates,
             &(&1.account_name == participant.account_name && &1.layer == layer - 1)
           ) do
        nil -> acc
        candidate -> Map.put(acc, candidate.id, participant.id)
      end
    end)
  end

  @doc """
  Deletes a beneficial owners report.

  ## Examples

      iex> delete_beneficial_owners_report(report)
      {:ok, %Report{}}

      iex> delete_beneficial_owners_report(report)
      {:error, %Ecto.Changeset{}}

  """
  def delete_beneficial_owners_report(%Report{} = report) do
    Repo.delete(report)
  end

  def delete_beneficial_owners_report(id) when is_id?(id) do
    id
    |> get_report_by_id!()
    |> Repo.delete()
  end

  @doc """
  Gets a single beneficial owners report by attributes.

  Returns nil if the report does not exist.

  ## Examples

      iex> get_beneficial_owners_report_by(%{id: 1})
      %Report{}

      iex> get_beneficial_owners_report_by(%{id: 1})
      nil

  """
  def get_beneficial_owners_report_by(attrs), do: Repo.get_by(Report, attrs)

  @doc """
  Lists all reports for a given company profile
  """
  def reports_for_company_with_details(company_profile_id) do
    Report
    |> where([r], r.company_profile_id == ^company_profile_id)
    |> order_by([r], desc: r.report_date)
    |> preload(:report_details)
    |> Repo.all()
  end

  @doc """
  Paginates reports for a given company profile (for hades)
  """
  def reports_for_company_paginated(%{company_profile_id: company_profile_id} = params) do
    paginate_opts = Map.get(params, :paginate_opts, [])

    Report
    |> where([r], r.company_profile_id == ^company_profile_id)
    |> join(:left, [r], rp in assoc(r, :report_details))
    |> group_by([r], r.id)
    |> select_merge([r, rp], %{report_details_count: count(rp.id)})
    |> distinct(true)
    |> order_by([r],
      desc_nulls_last: r.disclosed_interest_document_uploaded_at,
      desc_nulls_last: r.report_date,
      desc: r.id
    )
    |> Repo.paginate(paginate_opts)
  end

  def list_pending_report_for_company(company_profile_id) do
    Report
    |> where([r], r.company_profile_id == ^company_profile_id)
    |> where([r], r.type != :importing)
    |> order_by([r], desc: r.inserted_at)
    |> Repo.all()
  end

  @doc """
  Lists all report ids for a given company profile
  """
  def report_ids_for_company(company_profile_id, opts \\ []) do
    where_opts = Keyword.get(opts, :where, [])

    Report
    |> where([r], r.company_profile_id == ^company_profile_id)
    |> where(^where_opts)
    |> order_by([r], desc: r.id)
    |> select([r], r.id)
    |> Repo.all()
  end

  @doc """
    Fetch report by id
  """
  def get_report_by_id!(id) do
    Repo.get!(Report, id)
  end

  @doc """
  Fetch report by attributes
  """
  def get_report_by!(attrs) do
    Repo.get_by!(Report, attrs)
  end

  def get_previous_completed_report!(report) do
    Report
    |> where([r], r.report_date < ^report.report_date)
    |> where([r], r.company_profile_id == ^report.company_profile_id)
    |> where([r], r.type == :completed)
    |> order_by([r], desc: r.report_date)
    |> limit(1)
    |> Repo.one()
  end

  def get_next_completed_report!(report) do
    Report
    |> where([r], r.report_date > ^report.report_date)
    |> where([r], r.company_profile_id == ^report.company_profile_id)
    |> where([r], r.type == :completed)
    |> order_by([r], asc: r.report_date)
    |> limit(1)
    |> Repo.one()
  end

  def get_previous_report!(report) do
    date_to_compare = report.report_date || report.inserted_at

    Report
    |> where([r], r.report_date < ^date_to_compare)
    |> where([r], r.company_profile_id == ^report.company_profile_id)
    |> order_by([r], desc: r.report_date, desc: r.inserted_at, desc: r.id)
    |> limit(1)
    |> Repo.one()
  end

  def get_next_report!(report) do
    date_to_compare = report.report_date || report.inserted_at

    Report
    |> where([r], r.report_date > ^date_to_compare)
    |> where([r], r.company_profile_id == ^report.company_profile_id)
    |> order_by([r], asc: r.report_date, asc: r.inserted_at, asc: r.id)
    |> limit(1)
    |> Repo.one()
  end

  @doc """
    Get a report by company profile id and report id
  """
  def from_report_for_company_with_details(company_profile_id, report_id) do
    Report
    |> where([r], r.company_profile_id == ^company_profile_id)
    |> where([r], r.id == ^report_id)
    |> preload(:report_details)
    |> Repo.one()
  end

  def report_details_for_report(report_id, search_term) do
    ReportDetail
    |> where([rd], rd.beneficial_owner_report_id == ^report_id)
    |> with_query(search_term)
    |> Repo.all()
  end

  def upsert_report(nil, attrs) do
    create_report(attrs)
  end

  def upsert_report(report, attrs) do
    report
    |> Report.changeset(attrs)
    |> Repo.update()
  end

  def insert_all_report_details(attrs) do
    now =
      NaiveDateTime.truncate(NaiveDateTime.utc_now(), :second)

    data =
      Enum.map(
        attrs,
        &Map.merge(&1, %{
          inserted_at: {:placeholder, :now},
          updated_at: {:placeholder, :now}
        })
      )

    placeholders = %{now: now}

    Repo.insert_all(ReportDetail, data, placeholders: placeholders)
  end

  @doc """
  Creates a beneficial owner report
  """
  def create_report(attrs \\ %{}) do
    %Report{}
    |> Report.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates a report detail
  """
  def create_report_detail(attrs \\ %{}) do
    %ReportDetail{}
    |> ReportDetail.changeset(attrs)
    |> Repo.insert()
  end

  def beneficial_owners_reports_query(options, company_profile_id) do
    query = where(Report, [r], r.company_profile_id == ^company_profile_id)

    Enum.reduce(options, query, fn
      {:filters, filters}, query -> beneficial_owners_reports_filter_with(query, filters)
      {:orders, orders}, query -> beneficial_owners_reports_order_with(query, orders)
      _, query -> query
    end)
  end

  defp beneficial_owners_reports_filter_with(query, filters) do
    Enum.reduce(filters, query, fn
      %{key: "synced_only", value: "true"}, query ->
        where(query, [q], not is_nil(q.sync_at))

      %{key: _, value: ""}, query ->
        query
    end)
  end

  defp beneficial_owners_reports_order_with(query, orders) do
    Enum.reduce(orders, query, fn
      %{key: _, value: ""}, query ->
        query

      %{key: "report_date", value: "asc"}, query ->
        order_by(query, [q], asc_nulls_first: q.report_date)

      %{key: "report_date", value: "desc"}, query ->
        order_by(query, [q], desc_nulls_first: q.report_date)

      %{key: "sync_at", value: "asc"}, query ->
        order_by(query, [q], asc_nulls_first: q.sync_at)

      %{key: "sync_at", value: "desc"}, query ->
        order_by(query, [q], desc_nulls_first: q.sync_at)
    end)
  end

  def get_latest_completed_beneficial_owners_report_details(company_profile_id) do
    company_profile_id
    |> get_latest_completed_report()
    |> beneficial_owners_report_details_by_report()
  end

  def beneficial_owners_report_details_by_report(nil), do: nil

  def beneficial_owners_report_details_by_report(%Report{} = report) do
    report =
      Repo.preload(report,
        report_details: [
          :beneficial_owner_contact,
          :investment_manager_contact,
          :shareholder,
          :beneficial_owner_report
        ]
      )

    rows = report.report_details

    unique_registered_holder_names =
      rows
      |> Enum.filter(fn row -> row.type != :rest_of_registry end)
      |> Enum.uniq_by(fn row -> row.registered_holder_name end)
      |> Enum.count()

    nominees_unmasked_without_shareholder_id =
      Enum.filter(rows, fn row -> is_nil(row.shareholder_id) and row.type == :nominee end)

    nominees_unmasked =
      rows
      |> Enum.filter(fn row -> row.type == :nominee and not is_nil(row.shareholder_id) end)
      |> Enum.uniq_by(fn row -> row.shareholder_id end)
      |> Enum.concat(nominees_unmasked_without_shareholder_id)
      |> Enum.count()

    total_holdings =
      Enum.reduce(rows, 0, fn
        %{beneficial_owner_holdings: nil}, sum -> sum
        %{beneficial_owner_holdings: holdings}, sum -> sum + holdings
      end)

    unmasked_holdings =
      rows
      |> Enum.filter(fn row -> row.type != :rest_of_registry end)
      |> Enum.reduce(0, fn
        %{beneficial_owner_holdings: nil}, sum -> sum
        %{beneficial_owner_holdings: holdings}, sum -> sum + holdings
      end)

    %{
      report_id: report.id,
      previous_report_date: report.previous_report_date,
      report_date: report.report_date,
      nominees_unmasked: nominees_unmasked,
      total_holdings: total_holdings,
      type: report.type,
      unmasked_holdings: unmasked_holdings,
      disclosed_interest_document_uploaded_at: report.disclosed_interest_document_uploaded_at,
      disclosed_interest_document_filename: report.disclosed_interest_document_filename,
      disclosed_interest_document_url: report.disclosed_interest_document_url,
      unique_registered_holder_names: unique_registered_holder_names
    }
  end

  defp get_latest_completed_report(company_profile_id) do
    Report
    |> where([r], r.company_profile_id == ^company_profile_id)
    |> where([r], r.type == :completed)
    |> order_by([r], desc: r.report_date)
    |> limit(1)
    |> Repo.one()
  end

  def get_current_beneficial_owners_report_request_importing(company_profile_id) do
    Report
    |> where([r], r.company_profile_id == ^company_profile_id)
    |> where([r], r.type == :importing)
    |> order_by([r], desc: r.inserted_at)
    |> limit(1)
    |> Repo.one()
  end

  def get_current_beneficial_owners_report_upload_processing(company_profile_id) do
    Report
    |> where([r], r.company_profile_id == ^company_profile_id)
    |> where([r], r.type == :processing)
    |> order_by([r], desc: r.inserted_at)
    |> limit(1)
    |> Repo.one()
  end

  defp with_query(query, search_term) do
    case search_term do
      nil ->
        query

      _ ->
        where(
          query,
          [rd],
          ilike(rd.registered_holder_name, ^"%#{search_term}%") or
            ilike(rd.beneficial_owner_name, ^"%#{search_term}%") or
            ilike(rd.investment_manager, ^"%#{search_term}%")
        )
    end
  end

  @doc """
  Returns the list of beneficial_owners_nominee_contacts.

  ## Examples

      iex> list_beneficial_owners_nominee_contacts()
      [%NomineeContact{}, ...]

  """
  def list_beneficial_owners_nominee_contacts do
    Repo.all(NomineeContact)
  end

  def list_beneficial_owners_nominee_contacts_with_similar_names do
    NomineeContact
    |> order_by([nc], desc: nc.updated_at)
    |> Repo.all()
    |> Enum.map(fn nc ->
      similar = find_similar_nominee_contacts(nc.account_name, nc.id)
      %{nc | similar_contacts: similar}
    end)
  end

  @doc """
  Gets a single nominee_contact.

  Raises `Ecto.NoResultsError` if the Nominee contact does not exist.

  ## Examples

      iex> get_nominee_contact!(123)
      %NomineeContact{}

      iex> get_nominee_contact!(456)
      ** (Ecto.NoResultsError)

  """
  def get_nominee_contact!(id), do: Repo.get!(NomineeContact, id)

  @doc """
  Creates a nominee_contact.

  ## Examples

      iex> create_nominee_contact(%{field: value})
      {:ok, %NomineeContact{}}

      iex> create_nominee_contact(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_nominee_contact(attrs \\ %{}) do
    %NomineeContact{}
    |> NomineeContact.changeset(attrs)
    |> Repo.insert()
  end

  def get_nominee_contact_by_account_name(account_name) do
    NomineeContact
    |> where([nc], nc.account_name == ^account_name)
    |> or_where([nc], fragment("? = ANY(?)", ^account_name, nc.alias_names))
    |> Repo.one()
  end

  def get_asic_organisation_by_company_name(account_name) do
    AsicOrganisation
    |> where([ao], ao.company_name == ^account_name)
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Updates a nominee_contact.

  ## Examples

      iex> update_nominee_contact(nominee_contact, %{field: new_value})
      {:ok, %NomineeContact{}}

      iex> update_nominee_contact(nominee_contact, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_nominee_contact(%NomineeContact{} = nominee_contact, attrs) do
    nominee_contact
    |> NomineeContact.changeset(attrs)
    |> Repo.update()
  rescue
    error in [Postgrex.Error] ->
      {:error, error}
  end

  @doc """
  Deletes a nominee_contact.

  ## Examples

      iex> delete_nominee_contact(nominee_contact)
      {:ok, %NomineeContact{}}

      iex> delete_nominee_contact(nominee_contact)
      {:error, %Ecto.Changeset{}}

  """
  def delete_nominee_contact(%NomineeContact{} = nominee_contact) do
    Repo.delete(nominee_contact)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking nominee_contact changes.

  ## Examples

      iex> change_nominee_contact(nominee_contact)
      %Ecto.Changeset{data: %NomineeContact{}}

  """
  def change_nominee_contact(%NomineeContact{} = nominee_contact, attrs \\ %{}) do
    NomineeContact.changeset(nominee_contact, attrs)
  end

  @doc """
  Returns the list of beneficial_owners_authorisation_letters.

  ## Examples

      iex> list_beneficial_owners_authorisation_letters()
      [%AuthorisationLetter{}, ...]

  """
  def list_beneficial_owners_authorisation_letters do
    Repo.all(AuthorisationLetter)
  end

  @doc """
  Gets a single authorisation_letter.

  Raises `Ecto.NoResultsError` if the Authorisation letter does not exist.

  ## Examples

      iex> get_authorisation_letter!(123)
      %AuthorisationLetter{}

      iex> get_authorisation_letter!(456)
      ** (Ecto.NoResultsError)

  """
  def get_authorisation_letter!(id), do: Repo.get!(AuthorisationLetter, id)
  def get_authorisation_letter_by!(attrs), do: Repo.get_by!(AuthorisationLetter, attrs)

  def get_authorisation_letter_by_company_profile_id(company_profile_id) do
    AuthorisationLetter
    |> where([al], al.company_profile_id == ^company_profile_id)
    |> order_by([al], desc: al.expired_date)
    |> Repo.all()
    |> Enum.map(
      &Map.put(
        &1,
        :file_url,
        Gaia.Uploaders.BeneficialOwners.AuthorisationLetter.url({&1.file, &1}, signed: true)
      )
    )
  end

  def get_authorisation_letter_with_company_profile_by_company_profile_id(company_profile_id) do
    AuthorisationLetter
    |> where([al], al.company_profile_id == ^company_profile_id)
    |> order_by([al], desc: al.expired_date)
    |> Repo.all()
    |> Repo.preload(company_profile: :ticker)
  end

  @doc """
  Creates a authorisation_letter.

  ## Examples

      iex> create_authorisation_letter(%{field: value})
      {:ok, %AuthorisationLetter{}}

      iex> create_authorisation_letter(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_authorisation_letter(attrs \\ %{}) do
    %AuthorisationLetter{}
    |> AuthorisationLetter.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a authorisation_letter.

  ## Examples

      iex> update_authorisation_letter(authorisation_letter, %{field: new_value})
      {:ok, %AuthorisationLetter{}}

      iex> update_authorisation_letter(authorisation_letter, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_authorisation_letter(%AuthorisationLetter{} = authorisation_letter, attrs) do
    authorisation_letter
    |> AuthorisationLetter.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a authorisation_letter.

  ## Examples

      iex> delete_authorisation_letter(authorisation_letter)
      {:ok, %AuthorisationLetter{}}

      iex> delete_authorisation_letter(authorisation_letter)
      {:error, %Ecto.Changeset{}}

  """
  def delete_authorisation_letter(%AuthorisationLetter{} = authorisation_letter) do
    Repo.delete(authorisation_letter)
  end

  def delete_authorisation_letter(id) when is_id?(id) do
    id
    |> get_authorisation_letter!()
    |> Repo.delete()
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking authorisation_letter changes.

  ## Examples

      iex> change_authorisation_letter(authorisation_letter)
      %Ecto.Changeset{data: %AuthorisationLetter{}}

  """
  def change_authorisation_letter(%AuthorisationLetter{} = authorisation_letter, attrs \\ %{}) do
    AuthorisationLetter.changeset(authorisation_letter, attrs)
  end

  @doc """
  Returns the list of beneficial_owner_report_candidates.

  ## Examples

      iex> list_beneficial_owner_report_candidates()
      [%ReportCandidate{}, ...]

  """
  def list_beneficial_owner_report_candidates do
    Repo.all(ReportCandidate)
  end

  @doc """
  Lists cascaded beneficial owner report candidates by report
  This will bring all children to be under their parents

  ## Options
  - `%{}` - Returns all candidates (empty map)
  - `%{status: status}` - Returns candidates filtered by status (e.g., :pending, :done, :failed)
  - `%{type: type}` - Returns candidates filtered by type (e.g., :nominee, :asic, :owner)
  - `%{sent: "true"}` - Returns candidates that have been contacted (last_contact_at is not nil)
  - `%{status: status, type: type, sent: sent}` - Returns candidates filtered by multiple criteria
  """
  def list_cascaded_beneficial_owner_report_candidates_by_report(report_id, filter) do
    # Start with the base query
    query =
      where(ReportCandidate, [rc], rc.report_id == ^report_id)

    query =
      case filter do
        %{status: status} when not is_nil(status) and status !== "" ->
          where(query, [rc], rc.status == ^status)

        _ ->
          query
      end

    query =
      case filter do
        %{type: type} when not is_nil(type) and type !== "" ->
          where(query, [rc], rc.type == ^type)

        _ ->
          query
      end

    query =
      case filter do
        %{sent: sent} when sent == "true" ->
          where(query, [rc], not is_nil(rc.last_contact_at))

        _ ->
          query
      end

    # Complete the query and process the results
    query
    |> order_by([rc], desc: rc.shares, desc: rc.account_name)
    |> preload([:parent, :last_report_candidate])
    |> Repo.all(with_invalidated: true)
    |> Repo.preload(:nominee_contact, with_invalidated: true)
    |> cascade_candidates()
  end

  defp cascade_candidates(candidates) do
    # Group top-level candidates (those with no parent)
    top_level = Enum.filter(candidates, fn c -> is_nil(c.parent_id) end)

    # Create a map of parent_id -> [children] for quick lookup
    children_map =
      candidates
      |> Enum.filter(fn c -> not is_nil(c.parent_id) end)
      |> Enum.group_by(fn c -> c.parent_id end)

    # Recursively build the tree structure
    Enum.map(top_level, fn candidate ->
      add_children(candidate, children_map)
    end)
  end

  # Helper function to recursively add children to a candidate
  defp add_children(candidate, children_map) do
    children = Map.get(children_map, candidate.id, [])

    children_with_their_children =
      Enum.map(children, fn child -> add_children(child, children_map) end)

    Map.put(candidate, :children, children_with_their_children)
  end

  @doc """
  Gets a single report_candidate.

  Raises `Ecto.NoResultsError` if the Report candidate does not exist.

  ## Examples

      iex> get_report_candidate!(123)
      %ReportCandidate{}

      iex> get_report_candidate!(456)
      ** (Ecto.NoResultsError)

  """
  def get_report_candidate!(id), do: Repo.get!(ReportCandidate, id)

  @doc """
  Lists all report candidates for a specific report.

  ## Examples

      iex> list_report_candidates(report_id)
      [%ReportCandidate{}, ...]
  """
  def list_report_candidates(report_id) do
    ReportCandidate
    |> where([rc], rc.report_id == ^report_id)
    |> Repo.all()
  end

  def get_report_candidate(id), do: Repo.get(ReportCandidate, id)

  @doc """
  Creates a report_candidate.

  ## Examples

      iex> create_report_candidate(%{field: value})
      {:ok, %ReportCandidate{}}

      iex> create_report_candidate(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_report_candidate(attrs \\ %{}) do
    %ReportCandidate{}
    |> ReportCandidate.changeset(attrs)
    |> Repo.insert()
  end

  def create_report_candidate_with_nominee_or_asic(%{account_name: account_name} = attrs) do
    nominee_account = get_nominee_contact_by_account_name(account_name)

    type =
      cond do
        Map.has_key?(attrs, :type) -> Map.get(attrs, :type)
        nominee_account -> :nominee
        true -> :owner
      end

    attrs
    |> Map.put(:type, type)
    |> Map.put(:nominee_contact_id, nominee_account && nominee_account.id)
    |> create_report_candidate()
  end

  def create_report_candidate_with_nominee_or_asic(attrs) do
    create_report_candidate(attrs)
  end

  def uk_common_nominees, do: @common_uk_nominees

  def create_report_candidates_for_uk(_company_profile_id, report_id, account_names \\ @common_uk_nominees) do
    now = NaiveDateTime.truncate(NaiveDateTime.utc_now(), :second)

    previous_candidates = generate_candidates_from_previous_report(report_id)

    candidates =
      Enum.map(account_names, &uk_candidate_from_nominee_account(&1, previous_candidates, report_id, now))

    {count, _} = Repo.insert_all(ReportCandidate, candidates)
    {:ok, count}
  end

  def uk_candidate_from_nominee_account(account_name, previous_candidates, report_id, now) do
    nominee_account = get_nominee_contact_by_account_name(account_name)
    previous_candidate = find_previous_candidate(account_name, previous_candidates)

    build_candidate_map(account_name, previous_candidate, nominee_account, report_id, now)
  end

  # Helper function to find the previous candidate
  defp find_previous_candidate(account_name, previous_candidates) do
    Enum.find(previous_candidates, fn c ->
      c.account_name == account_name and c.layer == 1
    end)
  end

  # Helper function to build the candidate map
  defp build_candidate_map(account_name, previous_candidate, %NomineeContact{} = nominee_account, report_id, now) do
    %{
      report_id: report_id,
      account_name: account_name,
      last_report_candidate_shares: previous_candidate && previous_candidate.last_report_candidate_shares,
      last_report_candidate_id: previous_candidate && previous_candidate.last_report_candidate_id,
      shares: 0,
      nominee_contact_id: nominee_account.id,
      address_line_one: nominee_account.address_line_one,
      address_line_two: nominee_account.address_line_two,
      address_city: nominee_account.address_city,
      address_state: nominee_account.address_state,
      address_postcode: nominee_account.address_postcode,
      address_country: nominee_account.address_country,
      layer: 1,
      type: :nominee,
      status: :pending,
      inserted_at: now,
      updated_at: now
    }
  end

  # Helper function to build the candidate map
  defp build_candidate_map(account_name, previous_candidate, _nominee_account, report_id, now) do
    %{
      report_id: report_id,
      account_name: account_name,
      last_report_candidate_shares: previous_candidate && previous_candidate.last_report_candidate_shares,
      last_report_candidate_id: previous_candidate && previous_candidate.last_report_candidate_id,
      shares: 0,
      layer: 1,
      type: :nominee,
      status: :pending,
      inserted_at: now,
      updated_at: now
    }
  end

  def create_report_candidates_from_top_50_shareholders(company_profile_id, report_id, report_time) do
    case company_profile_id
         |> Gaia.Registers.top_50_query(report_time)
         |> join(:inner, [q], sh in assoc(q, :shareholding))
         |> select([q, sh], %{
           id: sh.id,
           account_name: sh.account_name,
           address_line_one: sh.address_line_one,
           address_line_two: sh.address_line_two,
           address_city: sh.address_city,
           address_state: sh.address_state,
           address_postcode: sh.address_postcode,
           address_country: sh.address_country,
           daily_balance: q.balance,
           daily_date: q.date,
           current_share_count: sh.share_count
         })
         |> Repo.all() do
      [%{} | _] = top_50_shareholders ->
        now = NaiveDateTime.utc_now(:second)

        previous_candidates = generate_candidates_from_previous_report(report_id)

        pending_insert_top_50_shareholders =
          top_50_shareholders
          |> Enum.filter(fn shareholding ->
            not Enum.any?(previous_candidates, fn prev_candidate ->
              prev_candidate.shareholding_id == shareholding.id && prev_candidate.layer == 1
            end)
          end)
          |> Enum.map(fn shareholding ->
            nominee_account = get_nominee_contact_by_account_name(shareholding.account_name)
            type = if nominee_account, do: :nominee, else: :owner
            status = if type == :nominee, do: :pending, else: :done

            %{
              report_id: report_id,
              shareholding_id: shareholding.id,
              account_name: shareholding.account_name,
              shares: shareholding.daily_balance,
              nominee_contact_id: nominee_account && nominee_account.id,
              address_line_one: shareholding.address_line_one,
              address_line_two: shareholding.address_line_two,
              address_city: shareholding.address_city,
              address_state: shareholding.address_state,
              address_postcode: shareholding.address_postcode,
              address_country: shareholding.address_country,
              layer: 1,
              type: type,
              status: status,
              inserted_at: now,
              updated_at: now
            }
          end)

        all_candidates = previous_candidates ++ pending_insert_top_50_shareholders

        layer_1_candidates =
          Enum.filter(all_candidates, fn candidate -> candidate.layer == 1 end)

        layer_2_candidates =
          Enum.filter(all_candidates, fn candidate -> candidate.layer == 2 end)

        layer_3_candidates = Enum.filter(all_candidates, fn candidate -> candidate.layer == 3 end)

        Ecto.Multi.new()
        # Create All first layer candidates
        |> Ecto.Multi.run(:layer_1_candidates, fn repo, _ ->
          candidates =
            Enum.map(layer_1_candidates, fn candidate ->
              Map.delete(candidate, :parent_account_name)
            end)

          {_, result} =
            repo.insert_all(ReportCandidate, candidates, returning: true)

          {:ok, result}
        end)
        # Create all second layer candidates
        |> Ecto.Multi.run(:layer_2_candidates, fn repo, %{layer_1_candidates: layer_1_candidates} ->
          candidates =
            Enum.map(layer_2_candidates, fn candidate ->
              parent_id =
                layer_1_candidates
                |> Enum.find(&(&1.account_name == candidate.parent_account_name))
                |> Helper.safe_get(:id)

              candidate
              |> Map.put(:parent_id, parent_id)
              |> Map.put(:shares, 0)
              |> Map.delete(:parent_account_name)
            end)

          {_, result} =
            repo.insert_all(ReportCandidate, candidates, returning: true, on_conflict: :nothing)

          {:ok, result}
        end)
        # Update layer 2 asic shares based on their parent layer 1 candidates' asic org id
        |> Ecto.Multi.run(:update_layer_2_asic_shares, fn repo, %{layer_1_candidates: layer_1_candidates} ->
          adding_asic_when_fetch(repo, layer_1_candidates)
        end)
        # As in here, update_layer_2_asic_shares missing parent_id, and layer_2_candidates missing latest shares updated by aisc
        # So we need to refresh layer_2_candidates by getting the values in database
        |> Ecto.Multi.run(:layer_2_candidates_refreshed, fn repo,
                                                            %{
                                                              layer_2_candidates: layer_2_candidates
                                                            } ->
          ids = Enum.map(layer_2_candidates, & &1.id)
          result = repo.all(from c in ReportCandidate, where: c.id in ^ids)
          {:ok, result}
        end)
        # Create all third layer candidates
        |> Ecto.Multi.run(:layer_3_candidates, fn repo,
                                                  %{
                                                    layer_2_candidates_refreshed: layer_2_candidates
                                                  } ->
          candidates =
            Enum.map(layer_3_candidates, fn candidate ->
              parent_id =
                layer_2_candidates
                |> Enum.find(&(&1.account_name == candidate.parent_account_name))
                |> Helper.safe_get(:id)

              candidate
              |> Map.put(:parent_id, parent_id)
              |> Map.put(:shares, 0)
              |> Map.delete(:parent_account_name)
            end)

          {_, result} =
            repo.insert_all(ReportCandidate, candidates, returning: true, on_conflict: :nothing)

          {:ok, result}
        end)
        # Update layer 3 asic shares based on their parent layer 2 candidates' asic org id
        |> Ecto.Multi.run(:update_layer_3_asic_shares, fn repo,
                                                          %{
                                                            layer_2_candidates_refreshed: layer_2_candidates
                                                          } ->
          adding_asic_when_fetch(repo, layer_2_candidates)
        end)
        |> Repo.transaction()

      nil ->
        {:error, "No shareholding data found for company profile #{company_profile_id}"}

      [] ->
        {:error, "No shareholders found for company profile #{company_profile_id}"}
    end
  end

  defp adding_asic_when_fetch(repo, layer_parent_candidates) do
    result =
      layer_parent_candidates
      |> Enum.filter(& &1.asic_organisation_id)
      |> Enum.map(fn candidate ->
        org =
          BeneficialOwners.get_asic_organisation_with_preloads!(candidate.asic_organisation_id)

        {:ok, result} =
          BeneficialOwners.AsicImporter.create_child_candidates(
            repo,
            candidate,
            org.asic_share_structures,
            org.asic_members
          )

        result
      end)
      |> List.flatten()

    {:ok, result}
  end

  def generate_candidates_from_previous_report(current_report_id) when is_binary(current_report_id) do
    generate_candidates_from_previous_report(String.to_integer(current_report_id))
  end

  def generate_candidates_from_previous_report(current_report_id) when is_integer(current_report_id) do
    # Get the current report with company_profile_id
    current_report = get_report_by_id!(current_report_id)

    # Find the latest report before the current one for the same company
    Report
    |> where([r], r.company_profile_id == ^current_report.company_profile_id)
    |> where([r], r.id != ^current_report_id and r.report_date < ^current_report.report_date)
    |> order_by([r], desc: r.report_date, desc: r.id)
    |> limit(1)
    |> Repo.one()
    |> case do
      nil ->
        # No previous report found
        []

      previous_report ->
        ReportCandidate
        |> join(:left, [rc], parent in assoc(rc, :parent))
        |> join(:left, [rc, parent], shareholding in assoc(rc, :shareholding))
        |> where([rc, parent, shareholding], rc.report_id == ^previous_report.id)
        |> select([rc, parent, shareholding], %{
          account_name: rc.account_name,
          nominee_contact_id: rc.nominee_contact_id,
          address_line_one: rc.address_line_one,
          address_line_two: rc.address_line_two,
          address_city: rc.address_city,
          address_state: rc.address_state,
          address_postcode: rc.address_postcode,
          address_country: rc.address_country,
          shareholding_id: rc.shareholding_id,
          shares: fragment("COALESCE(?, ?)", shareholding.share_count, rc.shares),
          last_report_candidate_shares: rc.shares,
          layer: rc.layer,
          type: rc.type,
          last_report_candidate_id: rc.id,
          parent_account_name: parent.account_name,
          asic_organisation_id: rc.asic_organisation_id
        })
        |> Repo.all()
        |> Enum.map(fn candidate ->
          now = NaiveDateTime.utc_now(:second)

          status =
            if candidate.type == :nominee, do: :pending, else: :done

          candidate
          |> Map.put(:status, status)
          |> Map.put(:inserted_at, now)
          |> Map.put(:updated_at, now)
          |> Map.put(:report_id, current_report_id)
        end)
    end
  end

  def get_last_report_participants_by_report_id(current_report_id) when is_binary(current_report_id) do
    get_last_report_participants_by_report_id(String.to_integer(current_report_id))
  end

  def get_last_report_participants_by_report_id(current_report_id) when is_integer(current_report_id) do
    # Get the current report with company_profile_id
    current_report = get_report_by_id!(current_report_id)

    # Find the latest report before the current one for the same company
    Report
    |> where([r], r.company_profile_id == ^current_report.company_profile_id)
    |> where([r], r.id < ^current_report_id)
    |> order_by([r], desc: r.report_date, desc: r.id)
    |> limit(1)
    |> Repo.one()
    |> case do
      nil ->
        # No previous report found
        []

      previous_report ->
        ReportParticipant
        |> join(:left, [rp], parent in assoc(rp, :parent))
        |> join(:left, [rp, parent], shareholding in assoc(rp, :shareholding))
        |> where([rp, parent, shareholding], rp.report_id == ^previous_report.id)
        |> Repo.all()
    end
  end

  @doc """
  Updates a report_candidate.

  ## Examples

      iex> update_report_candidate(report_candidate, %{field: new_value})
      {:ok, %ReportCandidate{}}

      iex> update_report_candidate(report_candidate, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_report_candidate(%ReportCandidate{} = report_candidate, attrs) do
    report_candidate
    |> ReportCandidate.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Updates a report_candidate's status and all its children recursively.
  Only children with status :pending will be updated.

  ## Examples

      iex> update_report_candidate_status(candidate_id, :done)
      {:ok, %{candidate: %ReportCandidate{}, update_children: %{count: 5}}}

      iex> update_report_candidate_status(candidate_id, :invalid_status)
      {:error, :candidate, %Ecto.Changeset{}, %{}}

  """
  def update_report_candidate_status(candidate_id, status) when is_atom(status) do
    # Get the candidate with its report_id
    candidate = get_report_candidate!(candidate_id)

    # Start a transaction
    Ecto.Multi.new()
    |> Ecto.Multi.update(:candidate, ReportCandidate.changeset(candidate, %{status: status}))
    |> Ecto.Multi.run(:children, fn repo, _changes ->
      # Find all children recursively
      find_all_children_recursively(repo, candidate_id, candidate.report_id)
    end)
    |> Ecto.Multi.run(:update_children, fn repo, %{children: children} ->
      # Update only children with status :pending
      if Enum.empty?(children) do
        {:ok, %{count: 0}}
      else
        {updated_count, _} =
          repo.update_all(
            from(rc in ReportCandidate,
              where: rc.id in ^Enum.map(children, & &1.id),
              where: rc.status == :pending
            ),
            set: [status: status, updated_at: NaiveDateTime.utc_now(:second)]
          )

        {:ok, %{count: updated_count}}
      end
    end)
    |> Repo.transaction()
  end

  def update_report_candidate_status(candidate_id, status) when is_binary(status) do
    update_report_candidate_status(candidate_id, String.to_existing_atom(status))
  end

  # Helper function to find all children recursively
  defp find_all_children_recursively(repo, parent_id, report_id) do
    # First, find direct children
    direct_children =
      ReportCandidate
      |> where([rc], rc.parent_id == ^parent_id)
      |> where([rc], rc.report_id == ^report_id)
      |> repo.all()

    # Then recursively find their children
    all_children =
      Enum.reduce(direct_children, direct_children, fn child, acc ->
        {:ok, child_children} = find_all_children_recursively(repo, child.id, report_id)
        acc ++ child_children
      end)

    {:ok, all_children}
  end

  @doc """
  Deletes a report_candidate.

  ## Examples

      iex> delete_report_candidate(report_candidate)
      {:ok, %ReportCandidate{}}

      iex> delete_report_candidate(report_candidate)
      {:error, %Ecto.Changeset{}}

  """
  def delete_report_candidate(%ReportCandidate{} = report_candidate) do
    # Delete the candidate, which will cascade to delete associated holdings
    # due to the on_delete: :delete_all constraint in the Holding schema
    Repo.delete(report_candidate)
  end

  def delete_all_candidate_from_report(report_id) do
    # Delete all candidates for the report, which will cascade to delete associated holdings
    # due to the on_delete: :delete_all constraint in the Holding schema
    ReportCandidate
    |> where([rc], rc.report_id == ^report_id)
    |> Repo.delete_all()
  end

  @doc """
  Deletes all children of a report candidate.

  ## Examples

      iex> delete_all_children(parent_candidate_id)
      {count, nil}

  Where count is the number of deleted records.
  """
  def delete_all_children(parent_id) do
    # Delete all candidates where parent_id matches the given ID
    # This will cascade to delete associated holdings due to the on_delete: :delete_all constraint
    ReportCandidate
    |> where([rc], rc.parent_id == ^parent_id)
    |> Repo.delete_all()
  end

  @doc """
  Deletes children of a report candidate where last_report_candidate_id is nil,
  and sets shares to 0 for children that have a last_report_candidate_id.

  ## Examples

      iex> delete_children_without_last_report(parent_candidate_id)
      {:ok, %{deleted_count: deleted_count, updated_count: updated_count}}

  """
  def delete_children_without_last_report(parent_id) do
    # Delete only children where last_report_candidate_id is nil
    {deleted_count, _} =
      ReportCandidate
      |> where([rc], rc.parent_id == ^parent_id)
      |> where([rc], is_nil(rc.last_report_candidate_id))
      |> Repo.delete_all()

    # Set shares to 0 for children that have a last_report_candidate_id
    {updated_count, _} =
      ReportCandidate
      |> where([rc], rc.parent_id == ^parent_id)
      |> where([rc], not is_nil(rc.last_report_candidate_id))
      |> Repo.update_all(set: [shares: 0, updated_at: NaiveDateTime.utc_now(:second)])

    {:ok, %{deleted_count: deleted_count, updated_count: updated_count}}
  end

  @doc """
  Moves a report to the next stage by:
  1. Getting the latest shareholding date
  2. Getting the top 50 shareholders
  3. For layer 1 candidates: updating current shares into last_report_candidate_shares and setting shares to latest values
  4. For layer > 1 candidates: updating current shares into last_report_candidate_shares and resetting shares to 0
  5. Adding any missing shareholders from the top 50

  Returns {:ok, result} on success or {:error, failed_operation, reason, changes} on failure.

  ## Examples

      iex> go_to_next_stage(company_profile_id, report_id)
      {:ok, %{update_candidates: [...], update_children: {count, nil}, add_missing_shareholders: [...]}}

      iex> go_to_next_stage(company_profile_id, report_id)
      {:error, :update_candidates, reason, changes}
  """
  def go_to_next_stage(company_profile_id, report_id) when is_binary(report_id) do
    go_to_next_stage(company_profile_id, String.to_integer(report_id))
  end

  def go_to_next_stage(company_profile_id, report_id) when is_integer(report_id) do
    date = Gaia.Registers.get_latest_daily_holding_date_by_company_profile(company_profile_id)

    if is_nil(date) do
      {:error, :no_shareholding_data, "No shareholding data available", %{}}
    else
      # Step 1: Get all layer 1 candidates
      layer_1_candidates = get_layer_1_candidates(report_id)

      # Step 2: Get the top 50 shareholders
      top_50_shareholders = get_top_50_shareholders(company_profile_id, date)

      # Step 3: Create and execute the multi transaction
      report_id
      |> build_next_stage_transaction(layer_1_candidates, top_50_shareholders)
      |> Repo.transaction()
    end
  end

  # Helper to get layer 1 candidates
  defp get_layer_1_candidates(report_id) do
    ReportCandidate
    |> where([rc], rc.report_id == ^report_id)
    |> where([rc], rc.layer == 1)
    |> Repo.all()
  end

  # Helper to get top 50 shareholders
  defp get_top_50_shareholders(company_profile_id, date) do
    company_profile_id
    |> Gaia.Registers.top_50_query(date)
    |> join(:inner, [q], sh in assoc(q, :shareholding))
    |> select([q, sh], sh)
    |> Repo.all()
  end

  # Helper to build the transaction for next stage
  defp build_next_stage_transaction(report_id, layer_1_candidates, top_50_shareholders) do
    Ecto.Multi.new()
    |> update_layer_1_candidates(layer_1_candidates, top_50_shareholders)
    |> update_higher_layer_candidates(report_id)
    |> add_missing_shareholders(report_id, layer_1_candidates, top_50_shareholders)
  end

  # Helper to update layer 1 candidates
  defp update_layer_1_candidates(multi, layer_1_candidates, top_50_shareholders) do
    Enum.reduce(layer_1_candidates, multi, fn candidate, acc_multi ->
      # Find matching shareholding from top 50
      shareholding = Enum.find(top_50_shareholders, &(&1.account_name == candidate.account_name))

      # Prepare update attributes
      update_attrs = %{
        last_report_candidate_shares: candidate.shares,
        shares: if(shareholding, do: shareholding.share_count, else: 0),
        shareholding_id: if(shareholding, do: shareholding.id, else: candidate.shareholding_id)
      }

      # Add update operation to multi
      Ecto.Multi.update(
        acc_multi,
        {:update_candidate, candidate.id},
        ReportCandidate.changeset(candidate, update_attrs)
      )
    end)
  end

  # Helper to update layer > 1 candidates
  defp update_higher_layer_candidates(multi, report_id) do
    Ecto.Multi.update_all(
      multi,
      :update_children,
      ReportCandidate
      |> where([rc], rc.report_id == ^report_id)
      |> where([rc], rc.layer > 1),
      set: [
        last_report_candidate_shares: dynamic([rc], rc.shares),
        shares: 0,
        updated_at: NaiveDateTime.utc_now(:second)
      ]
    )
  end

  # Helper to add missing shareholders
  defp add_missing_shareholders(multi, report_id, layer_1_candidates, top_50_shareholders) do
    Ecto.Multi.run(multi, :add_missing_shareholders, fn repo, _ ->
      # Find shareholders that don't exist in current candidates
      existing_account_names = Enum.map(layer_1_candidates, & &1.account_name)

      # Filter to get only missing shareholders
      missing_shareholders =
        Enum.filter(top_50_shareholders, fn sh ->
          sh.account_name not in existing_account_names
        end)

      # Create and insert new candidates
      insert_missing_shareholders(repo, report_id, missing_shareholders)
    end)
  end

  # Helper to create and insert missing shareholders
  defp insert_missing_shareholders(repo, report_id, missing_shareholders) do
    if Enum.empty?(missing_shareholders) do
      {:ok, []}
    else
      # Create new candidates for missing shareholders
      now = NaiveDateTime.utc_now(:second)

      candidates_to_insert =
        Enum.map(missing_shareholders, fn shareholding ->
          nominee_account = get_nominee_contact_by_account_name(shareholding.account_name)
          type = if nominee_account, do: :nominee, else: :owner
          status = if type == :nominee, do: :pending, else: :done

          %{
            report_id: report_id,
            shareholding_id: shareholding.id,
            account_name: shareholding.account_name,
            shares: shareholding.share_count,
            nominee_contact_id: nominee_account && nominee_account.id,
            address_line_one: shareholding.address_line_one,
            address_line_two: shareholding.address_line_two,
            address_city: shareholding.address_city,
            address_state: shareholding.address_state,
            address_postcode: shareholding.address_postcode,
            address_country: shareholding.address_country,
            layer: 1,
            type: type,
            status: status,
            inserted_at: now,
            updated_at: now
          }
        end)

      {_, result} =
        repo.insert_all(
          ReportCandidate,
          candidates_to_insert,
          returning: true
        )

      {:ok, result}
    end
  end

  @doc """
  Reassigns a candidate to a new parent.

  This function updates the parent_id of a candidate and ensures the layer is updated
  appropriately based on the new parent's layer.

  ## Examples

      iex> reassign_candidate_parent(candidate_id, new_parent_id)
      {:ok, %ReportCandidate{}}

      iex> reassign_candidate_parent(candidate_id, new_parent_id)
      {:error, reason}
  """
  def reassign_candidate_parent(candidate_id, new_parent_id) do
    # Normalize IDs to integers
    candidate_id = normalize_id(candidate_id)
    new_parent_id = normalize_id(new_parent_id)

    # Start a transaction to ensure all updates are atomic
    Ecto.Multi.new()
    |> fetch_candidate(candidate_id)
    |> fetch_new_parent(new_parent_id)
    |> update_candidate_parent()
    |> update_children_layers()
    |> Repo.transaction()
    |> handle_transaction_result()
  end

  # Helper to normalize IDs to integers
  defp normalize_id(id) when is_binary(id), do: String.to_integer(id)
  defp normalize_id(id) when is_integer(id), do: id

  # Helper to fetch the candidate
  defp fetch_candidate(multi, candidate_id) do
    Ecto.Multi.run(multi, :candidate, fn repo, _ ->
      case repo.get(ReportCandidate, candidate_id) do
        nil -> {:error, "Candidate not found"}
        candidate -> {:ok, candidate}
      end
    end)
  end

  # Helper to fetch the new parent
  defp fetch_new_parent(multi, new_parent_id) do
    Ecto.Multi.run(multi, :new_parent, fn repo, _ ->
      case repo.get(ReportCandidate, new_parent_id) do
        nil -> {:error, "New parent not found"}
        parent -> {:ok, parent}
      end
    end)
  end

  # Helper to update the candidate with new parent and layer
  defp update_candidate_parent(multi) do
    Ecto.Multi.run(multi, :update_candidate, fn repo, %{candidate: candidate, new_parent: new_parent} ->
      # Calculate the new layer based on the parent's layer
      new_layer = new_parent.layer + 1

      # Update the candidate with the new parent_id and layer
      candidate
      |> ReportCandidate.changeset(%{parent_id: new_parent.id, layer: new_layer})
      |> repo.update()
    end)
  end

  # Helper to update all children's layers
  defp update_children_layers(multi) do
    Ecto.Multi.run(multi, :update_children, fn repo,
                                               %{
                                                 candidate: candidate,
                                                 update_candidate: updated_candidate
                                               } ->
      # Find all direct children of the candidate
      children = find_direct_children(repo, candidate.id)

      # Update each child's layer
      update_children_results(repo, children, updated_candidate.layer + 1)
    end)
  end

  # Helper to find direct children of a candidate
  defp find_direct_children(repo, parent_id) do
    ReportCandidate
    |> where([rc], rc.parent_id == ^parent_id)
    |> repo.all()
  end

  # Helper to update children and check results
  defp update_children_results(repo, children, new_layer) do
    results =
      Enum.map(children, fn child ->
        child
        |> ReportCandidate.changeset(%{layer: new_layer})
        |> repo.update()
      end)

    # Check if any updates failed
    case Enum.find(results, fn {status, _} -> status == :error end) do
      nil -> {:ok, length(results)}
      {_, changeset} -> {:error, changeset}
    end
  end

  # Helper to handle transaction result
  defp handle_transaction_result({:ok, %{update_candidate: updated_candidate}}), do: {:ok, updated_candidate}

  defp handle_transaction_result({:error, _step, reason, _changes}), do: {:error, reason}

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking report_candidate changes.

  ## Examples

      iex> change_report_candidate(report_candidate)
      %Ecto.Changeset{data: %ReportCandidate{}}

  """
  def change_report_candidate(%ReportCandidate{} = report_candidate, attrs \\ %{}) do
    ReportCandidate.changeset(report_candidate, attrs)
  end

  @doc """
  Constructs a disclosure letter html
  """

  def construct_template_html(template_type, values) do
    with {:ok, values} <- maybe_add_signature(template_type, values),
         {:ok, template} <- read_template(template_type) do
      {:ok, replace_placeholders(template, values)}
    end
  end

  defp read_template(template_type) do
    template_path =
      case template_type do
        :disclosure_letter -> "priv/data/disclosure_letter_template.html"
        :disclosure_request_email -> "priv/data/disclosure_request_email_template.html"
        :disclosure_letter_uk -> "priv/data/disclosure_letter_template_uk.html"
        :disclosure_request_email_uk -> "priv/data/disclosure_request_email_template_uk.html"
      end

    case File.read(Application.app_dir(:gaia, template_path)) do
      {:ok, template} -> {:ok, template}
      {:error, _} -> {:error, "Failed to read #{template_type} template file"}
    end
  end

  defp maybe_add_signature(:disclosure_letter_uk, values) do
    case GoogleAPI.Storage.download_file("uploads/beneficial_owners/s793_notice/signature.png") do
      {:ok, image_binary} ->
        values =
          Map.put(
            values,
            :signature_image_url,
            "data:image/png;base64,#{Base.encode64(image_binary)}"
          )

        {:ok, values}

      {:error, _} ->
        {:error, "Failed to download signature image"}
    end
  end

  defp maybe_add_signature(_, values), do: {:ok, values}

  defp replace_placeholders(template, values) do
    Enum.reduce(values, template, fn {key, value}, acc ->
      placeholder = "{{#{key}}}"
      String.replace(acc, placeholder, value)
    end)
  end

  @doc """
  Finds nominee contacts with similar account names.
  """
  def find_similar_nominee_contacts(account_name, id, already_selected_ids \\ []) do
    normalized_account_name =
      account_name
      |> String.downcase()
      # remove all non-word characters (including whitespace)
      |> String.replace(~r/[^\w]/, "")
      |> String.trim()

    prefix = String.slice(normalized_account_name, 0..4)

    NomineeContact
    |> where([nc], nc.id != ^id)
    |> where(
      [nc],
      ilike(
        fragment(
          "LOWER(REGEXP_REPLACE(REGEXP_REPLACE(TRIM(?), '[^\\w]', '', 'g'), '\\s+', '', 'g'))",
          nc.account_name
        ),
        ^"%#{prefix}%"
      )
    )
    |> or_where(
      [nc],
      nc.id in ^already_selected_ids
    )
    |> Repo.all()
  end

  @doc """
  Merges nominee contacts by invalidating the selected ones and adding their account names as aliases to the target.
  """
  def merge_nominee_contacts(target_id, merge_ids) do
    Repo.transaction(fn ->
      target = get_nominee_contact!(target_id)
      merge_contacts = Enum.map(merge_ids, &get_nominee_contact!/1)

      # Invalidate duplicates
      Enum.each(merge_contacts, &update_nominee_contact(&1, %{invalidated: true}))

      # Build consolidated attributes
      consolidated_aliases =
        (target.alias_names || [])
        |> Enum.concat(Enum.flat_map(merge_contacts, fn c -> [c.account_name | c.alias_names || []] end))
        |> Enum.uniq()

      # Sort by updated_at to get most recently updated value first
      sorted_contacts = Enum.sort_by(merge_contacts, & &1.updated_at, {:desc, NaiveDateTime})

      consolidated_email = target.email || Enum.find_value(sorted_contacts, & &1.email)

      consolidated_name =
        target.contact_name || Enum.find_value(sorted_contacts, & &1.contact_name)

      params = %{
        alias_names: consolidated_aliases,
        email: consolidated_email,
        contact_name: consolidated_name
      }

      update_nominee_contact(target, params)
    end)
  end

  @doc """
  Searches nominee contacts by a search term, excluding the given nominee contact ID.
  """
  def search_nominee_contacts_excluding_current(search_term, exclude_id, already_selected_ids \\ []) do
    NomineeContact
    |> where([nc], nc.id != ^exclude_id)
    |> where(
      [nc],
      ilike(nc.account_name, ^"%#{search_term}%") or
        ilike(nc.contact_name, ^"%#{search_term}%") or
        ilike(nc.email, ^"%#{search_term}%")
    )
    |> or_where(
      [nc],
      nc.id in ^already_selected_ids
    )
    |> Repo.all()
  end

  @doc """
  Searches nominee contacts by contact name or alias names.
  Returns a list of nominee contacts with similar_contacts populated.
  """
  def search_nominee_contacts_by_contact_name(search_term) do
    NomineeContact
    |> where(
      [nc],
      ilike(nc.contact_name, ^"%#{search_term}%") or
        ilike(nc.account_name, ^"%#{search_term}%") or
        fragment("? @> ARRAY[?]::varchar[]", nc.alias_names, ^search_term) or
        fragment(
          "EXISTS (SELECT 1 FROM unnest(?) AS alias WHERE alias ILIKE ?)",
          nc.alias_names,
          ^"%#{search_term}%"
        )
    )
    |> order_by([nc], desc: nc.updated_at)
    |> Repo.all()
    |> Enum.map(fn nc ->
      similar = find_similar_nominee_contacts(nc.account_name, nc.id)
      %{nc | similar_contacts: similar}
    end)
  end

  @doc """
  Returns the list of beneficial_owners_asic_organisations.

  ## Examples

      iex> list_beneficial_owners_asic_organisations()
      [%AsicOrganisation{}, ...]

  """
  def list_beneficial_owners_asic_organisations do
    Repo.all(AsicOrganisation)
  end

  @doc """
  Returns a list of AsicOrganisations whose normalized company name contains a prefix from the given account_name.

  The prefix is the first 5 characters of the normalized (lowercase, non-word chars removed, trimmed) account_name.

  Also returns those for the given search term
  """
  def list_asic_organisations_with_similar_names(account_name, search) do
    normalized_account_name =
      account_name
      |> Kernel.||("")
      |> String.downcase()
      |> String.replace(~r/[^\w]/, "")
      |> String.trim()

    prefix = String.slice(normalized_account_name, 0..4)

    query =
      where(
        AsicOrganisation,
        [ao],
        ilike(
          fragment(
            "LOWER(REGEXP_REPLACE(REGEXP_REPLACE(TRIM(?), '[^\\w]', '', 'g'), '\\s+', '', 'g'))",
            ao.company_name
          ),
          ^"%#{prefix}%"
        )
      )

    query =
      if String.match?(search, ~r/^\d{3,}$/) do
        or_where(
          query,
          [ao],
          fragment("REGEXP_REPLACE(?, '\\s+', '', 'g') ILIKE ?", ao.acn, ^"%#{search}%")
        )
      else
        query
      end

    query
    |> or_where(
      [ao],
      ilike(ao.company_name, ^"%#{search}%")
    )
    |> Repo.all()
  end

  @doc """
  Returns a list of AsicOrganisations whose normalized company name contains a prefix from the given account_name.

  The prefix is the first 5 characters of the normalized (lowercase, non-word chars removed, trimmed) account_name.
  """
  def list_asic_organisations_with_similar_names(account_name) do
    normalized_account_name =
      account_name
      |> Kernel.||("")
      |> String.downcase()
      |> String.replace(~r/[^\w]/, "")
      |> String.trim()

    prefix = String.slice(normalized_account_name, 0..4)

    AsicOrganisation
    |> where(
      [ao],
      ilike(
        fragment(
          "LOWER(REGEXP_REPLACE(REGEXP_REPLACE(TRIM(?), '[^\\w]', '', 'g'), '\\s+', '', 'g'))",
          ao.company_name
        ),
        ^"%#{prefix}%"
      )
    )
    |> Repo.all()
  end

  @doc """
  Gets a single asic_organisation.

  Raises `Ecto.NoResultsError` if the ASIC organisation does not exist.

  ## Examples

      iex> get_asic_organisation!(123)
      %AsicOrganisation{}

      iex> get_asic_organisation!(456)
      ** (Ecto.NoResultsError)

  """
  def get_asic_organisation!(id), do: Repo.get!(AsicOrganisation, id)

  def get_asic_organisation_with_preloads!(id) do
    AsicOrganisation
    |> Repo.get!(id)
    |> Repo.preload([
      :asic_share_structures,
      asic_members: [:asic_member_share_structures, :share_structures]
    ])
  end

  @doc """
  Creates a asic_organisation.

  ## Examples

      iex> create_asic_organisation(%{field: value})
      {:ok, %AsicOrganisation{}}

      iex> create_asic_organisation(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_asic_organisation(attrs \\ %{}) do
    %AsicOrganisation{}
    |> AsicOrganisation.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a asic_organisation.

  ## Examples

      iex> update_asic_organisation(asic_organisation, %{field: new_value})
      {:ok, %AsicOrganisation{}}

      iex> update_asic_organisation(asic_organisation, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_asic_organisation(%AsicOrganisation{} = asic_organisation, attrs) do
    asic_organisation
    |> AsicOrganisation.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a asic_organisation.

  ## Examples

      iex> delete_asic_organisation(asic_organisation)
      {:ok, %AsicOrganisation{}}

      iex> delete_asic_organisation(asic_organisation)
      {:error, %Ecto.Changeset{}}

  """
  def delete_asic_organisation(%AsicOrganisation{} = asic_organisation) do
    Repo.delete(asic_organisation)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking asic_organisation changes.

  ## Examples

      iex> change_asic_organisation(asic_organisation)
      %Ecto.Changeset{data: %AsicOrganisation{}}

  """
  def change_asic_organisation(%AsicOrganisation{} = asic_organisation, attrs \\ %{}) do
    AsicOrganisation.changeset(asic_organisation, attrs)
  end

  @doc """
  Creates an ASIC share structure.
  """
  def create_asic_share_structure(attrs \\ %{}) do
    %BeneficialOwners.AsicShareStructure{}
    |> BeneficialOwners.AsicShareStructure.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates an ASIC member.
  """
  def create_asic_member(attrs \\ %{}) do
    %BeneficialOwners.AsicMember{}
    |> BeneficialOwners.AsicMember.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Returns a list of nominee contacts whose normalized account name contains a prefix from the given account_name.

  The prefix is the first 6 characters of the normalized (lowercase, non-word chars removed, trimmed) account_name.

  Also returns those for the given search term
  """
  def list_nominee_contacts_with_similar_names(account_name, search) do
    normalized_account_name =
      account_name
      |> Kernel.||("")
      |> String.downcase()
      |> String.replace(~r/[^\w]/, "")
      |> String.trim()

    prefix = String.slice(normalized_account_name, 0..5)

    query =
      where(
        NomineeContact,
        [nc],
        ilike(
          fragment(
            "LOWER(REGEXP_REPLACE(REGEXP_REPLACE(TRIM(?), '[^\\w]', '', 'g'), '\\s+', '', 'g'))",
            nc.account_name
          ),
          ^"%#{prefix}%"
        )
      )

    query
    |> or_where(
      [nc],
      ilike(
        fragment(
          "LOWER(REGEXP_REPLACE(REGEXP_REPLACE(TRIM(?), '[^\\w]', '', 'g'), '\\s+', '', 'g'))",
          nc.account_name
        ),
        ^"%#{search}%"
      )
    )
    |> or_where(
      [nc],
      ilike(nc.email, ^"%#{search}%")
    )
    |> Repo.all()
  end

  @doc """
  Returns a list of nominee contacts whose normalized account name contains a prefix from the given account_name.

  The prefix is the first 6 characters of the normalized (lowercase, non-word chars removed, trimmed) account_name.
  """
  def list_nominee_contacts_with_similar_names(account_name) do
    normalized_account_name =
      account_name
      |> Kernel.||("")
      |> String.downcase()
      |> String.replace(~r/[^\w]/, "")
      |> String.trim()

    prefix = String.slice(normalized_account_name, 0..5)

    NomineeContact
    |> where(
      [nc],
      ilike(
        fragment(
          "LOWER(REGEXP_REPLACE(REGEXP_REPLACE(TRIM(?), '[^\\w]', '', 'g'), '\\s+', '', 'g'))",
          nc.account_name
        ),
        ^"%#{prefix}%"
      )
    )
    |> Repo.all()
  end

  @doc """
  Inserts a past report candidate, and the children of the registered holder.
  """
  def insert_past_report_candidate(registered_holder, report_id) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:registered_holder, fn repo, _changes ->
      parent_attrs = %{
        "account_name" => empty_to_nil(registered_holder["account_name"]),
        "shares" => shares_to_integer(registered_holder["shares"]),
        "address_line_one" => empty_to_nil(registered_holder["address_line_one"]),
        "address_line_two" => empty_to_nil(registered_holder["address_line_two"]),
        "address_city" => empty_to_nil(registered_holder["address_city"]),
        "address_state" => empty_to_nil(registered_holder["address_state"]),
        "address_postcode" => empty_to_nil(registered_holder["address_postcode"]),
        "address_country" => empty_to_nil(registered_holder["address_country"]),
        "report_id" => report_id,
        "layer" => 1,
        "parent_id" => nil,
        "type" => "nominee"
      }

      %ReportCandidate{}
      |> ReportCandidate.changeset(parent_attrs)
      |> repo.insert()
    end)
    |> Ecto.Multi.run(:beneficial_owners, fn repo, %{registered_holder: parent} ->
      # Create the children (layer 2) - beneficial owners
      beneficial_owners = registered_holder["beneficial_owners"] || []

      results =
        Enum.map(beneficial_owners, fn beneficial_owner ->
          child_attrs = %{
            "account_name" => empty_to_nil(beneficial_owner["account_name"]),
            "shares" => shares_to_integer(beneficial_owner["shares"]),
            "address_line_one" => empty_to_nil(beneficial_owner["address_line_one"]),
            "address_line_two" => empty_to_nil(beneficial_owner["address_line_two"]),
            "address_city" => empty_to_nil(beneficial_owner["address_city"]),
            "address_state" => empty_to_nil(beneficial_owner["address_state"]),
            "address_postcode" => empty_to_nil(beneficial_owner["address_postcode"]),
            "address_country" => empty_to_nil(beneficial_owner["address_country"]),
            "report_id" => report_id,
            "layer" => 2,
            "parent_id" => parent.id,
            "type" => "owner"
          }

          %ReportCandidate{}
          |> ReportCandidate.changeset(child_attrs)
          |> repo.insert()
        end)

      # Check if all insertions were successful
      case Enum.find(results, fn result -> match?({:error, _}, result) end) do
        nil ->
          successful_results = Enum.map(results, fn {:ok, candidate} -> candidate end)
          {:ok, successful_results}

        {:error, changeset} ->
          {:error, changeset}
      end
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{registered_holder: parent, beneficial_owners: children}} ->
        {:ok, %{candidates: {1 + length(children), [parent | children]}}}

      {:error, _step, changeset, _changes} ->
        {:error, changeset}
    end
  end

  @doc """
  Creates a report candidate in bulk.
  """
  def upsert_candidates_multi(candidates, parent, report_id) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:candidates, fn repo, _changes ->
      # Process each candidate one by one
      results =
        Enum.map(candidates, fn candidate ->
          attrs =
            maybe_nominee_or_asic(%{
              account_name: empty_to_nil(candidate["account_name"]),
              shares: shares_to_integer(candidate["shares"]),
              address_line_one: empty_to_nil(candidate["address_line_one"]),
              address_line_two: empty_to_nil(candidate["address_line_two"]),
              address_city: empty_to_nil(candidate["address_city"]),
              address_state: empty_to_nil(candidate["address_state"]),
              address_postcode: empty_to_nil(candidate["address_postcode"]),
              address_country: empty_to_nil(candidate["address_country"]),
              report_id: report_id,
              parent_id: parent.id,
              layer: parent.layer + 1,
              inserted_at: NaiveDateTime.utc_now(:second),
              updated_at: NaiveDateTime.utc_now(:second)
            })

          # Check if a candidate with the same identifying attributes already exists
          base_query =
            ReportCandidate
            |> where([rc], rc.account_name == ^attrs.account_name)
            |> where([rc], rc.report_id == ^report_id)
            |> where([rc], rc.parent_id == ^parent.id)
            |> where([rc], rc.layer == ^(parent.layer + 1))

          # Add address_line_one condition
          query_with_address_line =
            if is_nil(attrs.address_line_one) do
              where(base_query, [rc], is_nil(rc.address_line_one))
            else
              where(base_query, [rc], rc.address_line_one == ^attrs.address_line_one)
            end

          # Add address_postcode condition
          query_with_address_postcode =
            if is_nil(attrs.address_postcode) do
              where(query_with_address_line, [rc], is_nil(rc.address_postcode))
            else
              where(query_with_address_line, [rc], rc.address_postcode == ^attrs.address_postcode)
            end

          existing_candidate =
            repo.one(query_with_address_postcode)

          if existing_candidate do
            # Update the existing candidate, summing the shares
            updated_shares = existing_candidate.shares + attrs.shares

            existing_candidate
            |> ReportCandidate.changeset(%{
              shares: updated_shares,
              address_line_one: attrs.address_line_one,
              address_line_two: attrs.address_line_two,
              address_city: attrs.address_city,
              address_state: attrs.address_state,
              address_postcode: attrs.address_postcode,
              address_country: attrs.address_country,
              updated_at: NaiveDateTime.utc_now(:second)
            })
            |> repo.update()
          else
            # Insert a new candidate
            %ReportCandidate{}
            |> ReportCandidate.changeset(attrs)
            |> repo.insert()
          end
        end)

      # Count successful operations and collect the candidates
      successful_candidates =
        results
        |> Enum.filter(fn result -> elem(result, 0) == :ok end)
        |> Enum.map(fn {:ok, candidate} -> candidate end)

      {:ok, {length(successful_candidates), successful_candidates}}
    end)
    |> maybe_create_child_candidates_from_asic()
    |> Gaia.Repo.transaction()
  end

  defp maybe_nominee_or_asic(candidate) do
    nominee_account = get_nominee_contact_by_account_name(candidate.account_name)
    asic_organisation = get_asic_organisation_by_company_name(candidate.account_name)

    cond do
      nominee_account ->
        candidate
        |> Map.put(:nominee_contact_id, nominee_account.id)
        |> Map.put(:type, :nominee)

      asic_organisation ->
        candidate
        |> Map.put(:asic_organisation_id, asic_organisation.id)
        |> Map.put(:type, :asic)

      true ->
        candidate
    end
  end

  defp maybe_create_child_candidates_from_asic(multi) do
    Ecto.Multi.run(multi, :create_child_candidates, fn _repo, %{candidates: {_, candidates}} ->
      candidates
      |> Enum.filter(& &1.asic_organisation_id)
      |> Enum.map(fn candidate ->
        create_child_candidates_for_asic(candidate)
      end)
      |> List.flatten()
      |> case do
        [] -> {:ok, []}
        result -> {:ok, result}
      end
    end)
  end

  defp create_child_candidates_for_asic(candidate) do
    asic_organisation = get_asic_organisation_with_preloads!(candidate.asic_organisation_id)

    Ecto.Multi.new()
    |> Ecto.Multi.run(:child_candidates, fn repo, _changes ->
      BeneficialOwners.AsicImporter.create_child_candidates(
        repo,
        candidate,
        asic_organisation.asic_share_structures,
        asic_organisation.asic_members
      )
    end)
    |> Repo.transaction()
    |> case do
      {:ok, _result} ->
        candidate

      {:error, _step, reason, _changes} ->
        raise "Failed to create child candidates: #{inspect(reason)}"
    end
  end

  defp shares_to_integer(""), do: 0

  defp shares_to_integer(shares) when is_binary(shares) do
    shares
    |> String.trim()
    |> String.replace(",", "")
    |> String.replace(~r/\.0+$/, "")
    |> Integer.parse()
    |> case do
      {int, _} -> int
      :error -> 0
    end
  end

  defp shares_to_integer(shares) when is_integer(shares), do: shares

  defp empty_to_nil(""), do: nil
  defp empty_to_nil(value), do: value

  @doc """
  Returns a tuple of {paginated_organisations, total_count} for ASIC organisations.
  """
  def paginate_beneficial_owners_asic_organisations(offset, limit, search \\ "") do
    base_query = from(ao in AsicOrganisation)

    query =
      if search && search != "" do
        where(
          base_query,
          [ao],
          ilike(ao.company_name, ^"%#{search}%") or ilike(ao.acn, ^"%#{search}%") or
            ilike(ao.abn, ^"%#{search}%")
        )
      else
        base_query
      end

    query = order_by(query, [ao], desc: ao.inserted_at)

    total_count = Repo.aggregate(query, :count)

    organisations =
      query
      |> limit(^limit)
      |> offset(^offset)
      |> Repo.all()

    {organisations, total_count}
  end

  def get_account("", _), do: nil

  def get_account(id, company_profile_id) do
    Account
    |> where([ho], ho.id == ^id and ho.company_profile_id == ^company_profile_id)
    |> limit(1)
    |> Repo.one()
  end

  def get_accounts_and_latest_holding_by_holding_ids(holding_ids, company_profile_id) do
    Account
    |> join(:inner, [acc], ho in assoc(acc, :beneficial_owner_holdings))
    |> where([_, ho], ho.id in ^holding_ids)
    |> where([_, ho], ho.company_profile_id == ^company_profile_id)
    |> select([acc, _], acc)
    |> Repo.all()
  end

  def batch_get_account_latest_holding(company_profile_id, account_ids) do
    BeneficialOwners.Holding
    |> join(:inner, [ho], c in assoc(ho, :candidate))
    |> join(:inner, [ho, c], r in assoc(c, :report))
    |> where([ho], ho.company_profile_id == ^company_profile_id)
    |> where([ho], ho.beneficial_owner_account_id in ^account_ids)
    |> order_by([_, _, r], desc: r.report_date)
    |> distinct([ho], ho.beneficial_owner_account_id)
    |> Repo.all()
    |> Map.new(fn holding -> {holding.beneficial_owner_account_id, holding} end)
  end

  def beneficial_owner_latest_holdings_per_account_by_shareholding_id(shareholding_id, company_profile_id) do
    Holding
    |> join(:inner, [h], r in assoc(h, :report))
    |> where([h], h.company_profile_id == ^company_profile_id)
    |> where([h], h.shareholding_id == ^shareholding_id)
    |> order_by([_, r], desc: r.report_date)
    |> distinct([h], h.beneficial_owner_account_id)
    |> Repo.all()
  end

  def beneficial_owner_latest_holdings_by_contact(
        %Contact{id: contact_id, company_profile_id: company_profile_id},
        %Date{} = start_date,
        %Date{} = end_date
      ) do
    contact_id_string = to_string(contact_id)

    BeneficialOwners.Holding
    |> join(:inner, [h], boa in assoc(h, :beneficial_owner_account))
    |> join(:inner, [h, boa], r in assoc(h, :report))
    |> where([h, boa, _r], h.company_profile_id == ^company_profile_id)
    |> where([h, boa, _r], boa.contact_id == ^contact_id)
    |> where([_h, _boa, r], r.report_date >= ^start_date)
    |> where([_h, _boa, r], r.report_date <= ^end_date)
    |> group_by([h, boa, r], r.report_date)
    |> group_by([h, boa, r], boa.account_name)
    |> select([h, boa, r], %{
      date: r.report_date,
      balance: type(sum(h.shares), :integer),
      id: fragment("? || '-' || ?", ^contact_id_string, r.report_date),
      account_name: boa.account_name
    })
    |> order_by([_h, _boa, r], desc: r.report_date)
    |> Repo.all()
  end

  def merge_beneficial_owner_holdings_by_account_name(holdings) do
    holdings
    |> Enum.group_by(fn holding -> {holding.date, holding.id} end)
    |> Enum.map(fn {{date, id}, holdings} ->
      total_balance = holdings |> Enum.map(& &1.balance) |> Enum.sum()

      %{
        date: date,
        id: id,
        balance: total_balance
      }
    end)
    |> Enum.sort_by(& &1.date, {:desc, Date})
  end

  @doc """
  Gets available parents for a report candidate.

  Returns a list of candidates that can be assigned as parents to the given candidate.
  Simply returns all candidates with layer < 3 (layer 1 or 2) from the same report.

  ## Examples

      iex> get_available_parents(candidate)
      [%ReportCandidate{}, ...]

  """
  def get_available_parents(candidate) do
    # Query for valid parents (layer 1 or 2)
    ReportCandidate
    |> where([rc], rc.report_id == ^candidate.report_id)
    |> where([rc], rc.layer < 3)
    |> order_by([rc], [rc.layer, fragment("LOWER(?)", rc.account_name)])
    |> Repo.all()
  end

  @doc """
  Updates a report.

  ## Examples

      iex> update_report(report, %{field: new_value})
      {:ok, %Report{}}

      iex> update_report(report, %{field: bad_value})
      {:error, %Ecto.Changeset{}}
  """
  def update_report(%Report{} = report, attrs) do
    report
    |> Report.changeset(attrs)
    |> Repo.update()
  end

  def set_past_report(report_id, past_report_date) do
    report = get_report_by_id!(report_id)

    report
    |> Report.changeset(%{report_date: past_report_date})
    |> Repo.update()
  end

  def get_most_recent_report_to_current_report(current_report) do
    Report
    |> where([r], r.company_profile_id == ^current_report.company_profile_id)
    |> where([r], r.report_date > ^current_report.report_date)
    |> order_by([r], asc: r.report_date)
    |> limit(1)
    |> Repo.one()
  end

  def sync_future_report(report_id) do
    current_report = get_report_by_id!(report_id)

    case get_next_report_for_sync(current_report) do
      nil ->
        {:ok, {0, nil}}

      future_report ->
        result = update_future_candidates(current_report.id, future_report.id)
        {:ok, result}
    end
  end

  defp get_next_report_for_sync(current_report) do
    Report
    |> where([r], r.company_profile_id == ^current_report.company_profile_id)
    |> where([r], r.report_date > ^current_report.report_date)
    |> order_by([r], asc: r.report_date)
    |> limit(1)
    |> Repo.one()
  end

  defp update_future_candidates(current_report_id, future_report_id) do
    query =
      from fc in ReportCandidate,
        join: cc in ReportCandidate,
        on:
          fragment("TRIM(?)", fc.account_name) == fragment("TRIM(?)", cc.account_name) and
            fc.layer == cc.layer,
        where:
          fc.report_id == ^future_report_id and
            cc.report_id == ^current_report_id

    updates = [
      set: [
        last_report_candidate_id: dynamic([_, cc], cc.id),
        last_report_candidate_shares: dynamic([_, cc], coalesce(cc.shares, 0)),
        updated_at: NaiveDateTime.utc_now(:second)
      ]
    ]

    Repo.update_all(query, updates)
  end
end
