defmodule Gaia.Contacts.Contact do
  @moduledoc """
  Contact Schema

  Contact is the main data entrypoint for our IRM system (Investor Relationship Management)

  1 contact has 0 to 1 investor hub
  1 contact has 0 to many shareholdings
  1 shareholding can only be in 1 contact
  1 investor hub can only be in 1 contact
  """

  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query, warn: false

  @fields [
    :email,
    :first_name,
    :last_name,
    :phone_number,
    :address_line_one,
    :address_line_two,
    :address_city,
    :address_state,
    :address_postcode,
    :address_country,
    :hnw_identified_at,
    :hnw_status,
    :invalidated,
    :company_profile_id,
    :creator_name,
    :creator_user_id,
    :imported_at,
    :lead_identified_at,
    :lead_converted_at,
    :is_nominated_shareholder,
    :nominated_shareholder_identified_at,
    :email_validity_response,
    :email_validity_checked_at,
    :email_validity_result,
    :bulk_import_id,
    :contact_source,
    :sunrice_grower_number,
    :registry_holder_ids,
    :occupation,
    :company,
    :age_range,
    :email_engagement_score
  ]

  @contact_source_type [
    :bulk_import,
    :hub_signup,
    :manual_creation,
    :registry_import,
    :subscribe_form,
    :beneficial_owners_import
  ]

  def contact_source_type, do: @contact_source_type

  @hnw_statuses [
    :nominated_without_cert,
    :nominated_cert_pending,
    :nominated_cert_verified,
    :identified_via_behaviour
  ]

  def hnw_statuses, do: @hnw_statuses

  @email_validity_results [
    :unknown,
    :valid,
    :invalid,
    :do_not_mail,
    :spamtrap,
    :catch_all,
    :abuse
  ]

  def email_validity_results, do: @email_validity_results

  schema "contacts_contacts" do
    field(:address_city, :string)
    field(:address_country, :string)
    field(:address_line_one, :string)
    field(:address_line_two, :string)
    field(:address_postcode, :string)
    field(:address_state, :string)
    field(:email, :string)
    field(:first_name, :string)
    field(:hnw_identified_at, :naive_datetime)
    field(:hnw_status, Ecto.Enum, values: @hnw_statuses)
    field(:invalidated, :boolean, default: false)
    field(:last_name, :string)
    field(:phone_number, :string)
    field(:lead_identified_at, :naive_datetime)
    field(:lead_converted_at, :naive_datetime)
    field(:is_nominated_shareholder, :boolean, default: false)
    field(:nominated_shareholder_identified_at, :naive_datetime)
    field(:email_validity_response, :map)
    field(:email_validity_checked_at, :naive_datetime)
    field(:email_validity_result, Ecto.Enum, values: @email_validity_results, default: :unknown)
    field(:sunrice_grower_number, :string)
    field(:registry_holder_ids, {:array, :string})
    field(:occupation, :string)
    field(:company, :string)
    field(:age_range, :string)
    field(:email_engagement_score, :float)

    # Defines how the contact was created
    # This field becomes immutable once populated (NOT NULL) and will throw an error if updated
    # Check postgres trigger `contacts_contacts_prevent_contact_source_update_trigger` for more info
    field(:contact_source, Ecto.Enum, values: @contact_source_type)

    belongs_to(:company_profile, Gaia.Companies.Profile, where: [invalidated: false])

    has_many(:comms_unsubscribes, Gaia.Comms.ContactUnsubscribe,
      foreign_key: :contact_id,
      on_replace: :delete
    )

    has_one(:global_unsubscribe, Gaia.Comms.ContactGlobalUnsubscribe,
      foreign_key: :contact_id,
      on_replace: :delete
    )

    has_one(:suppression, Gaia.Comms.ContactSuppression, foreign_key: :contact_id)

    has_many(:notes, Gaia.Contacts.Note, foreign_key: :contact_id, where: [invalidated: false])
    has_many(:shareholdings, Gaia.Registers.Shareholding)
    # Quick fix for preloading shareholdings timing out with a 502 response
    has_many(:shareholdings_without_preloads, Gaia.Registers.Shareholding)

    has_many(:tags, Gaia.Contacts.Tag, foreign_key: :contact_id, where: [invalidated: false])
    has_one(:investor, Gaia.Investors.User)

    has_many(:static_list_memberships, Gaia.Contacts.StaticListMember, foreign_key: :contact_id)
    has_many(:static_lists, through: [:static_list_memberships, :static_list])

    has_many(:beneficial_owner_accounts, Gaia.BeneficialOwners.Account, foreign_key: :contact_id)

    # To identify how the contact is created
    # Nil means the contact is auto-generated
    # The creator_name field is for when creator_user is deleted or invalidated
    field(:creator_name, :string)
    belongs_to(:creator_user, Gaia.Companies.User)

    # To identify if contact is imported
    field(:imported_at, :naive_datetime)
    belongs_to(:bulk_import, Gaia.Contacts.BulkImportContact)

    timestamps()
  end

  @doc false
  def changeset(contact, attrs) do
    contact
    |> cast(attrs, @fields)
    |> cast_assoc(:comms_unsubscribes, with: &Gaia.Comms.ContactUnsubscribe.changeset/2)
    |> cast_assoc(:global_unsubscribe, with: &Gaia.Comms.ContactGlobalUnsubscribe.changeset/2)
    |> validate_required([:company_profile_id])
    |> update_change(:email, &maybe_downcase/1)
    |> maybe_remove_suppression(contact)
    |> validate_format(:email, ~r/^[^\s]+@[^\s]+$/, message: "must have the @ sign and no spaces")
    |> validate_length(:email, max: 160)
    |> unique_constraint([:company_profile_id, :email])
    |> unique_constraint(
      [:company_profile_id, :first_name, :address_line_one, :address_country, :address_postcode, :contact_source],
      name: :contacts_beneficial_owners_unique_idx,
      where: "contact_source = 'beneficial_owners_import'"
    )
  end

  defp maybe_downcase(email) when is_binary(email), do: String.downcase(String.trim(email))

  defp maybe_downcase(nil), do: nil

  # Remove suppression if the email has been changed
  defp maybe_remove_suppression(changeset, contact) do
    email = get_change(changeset, :email)

    if email do
      contact = Gaia.Repo.preload(contact, :suppression)

      case contact.suppression do
        nil -> :ok
        suppression -> Gaia.Repo.delete(suppression)
      end
    end

    changeset
  end
end
