defmodule Gaia.Flows.Distribution do
  @moduledoc """
  Module related to distribution features
  """

  alias Gaia.Comms.CustomEmail
  alias Gaia.Comms.Email
  alias Gaia.Comms.EmailRecipient
  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.SocialConnection
  alias Gaia.Flows
  alias Gaia.Flows.DistributedSocial
  alias Gaia.Flows.DistributionSettings
  alias Gaia.Flows.DistributionSettingsEmail
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Interactions.MediaUpdate
  alias Gaia.Markets.Ticker
  alias Gaia.Repo
  alias Gaia.Socials
  alias Socials.LinkedIn
  alias Socials.Twitter

  #########################################################################
  # Distribute to socials                                                 #
  #########################################################################

  @doc """
  Distribute media to social a specific social platform
  """
  def distribute_to_social(
        %Media{
          company_profile:
            %Profile{
              social_connection: social_connection,
              ticker: %Ticker{listing_key: listing_key, market_key: market_key}
            } = company_profile,
          distributed_social: distributed_social,
          media_announcement: announcement,
          media_update: update
        } = media,
        channel,
        social_post_template,
        linkedin_thumbail_url
      )
      when channel in [:linkedin, :twitter] and
             (is_struct(announcement, MediaAnnouncement) or is_struct(update, MediaUpdate)) do
    # When linkedin_thumbail_url is nil, Linkedin preview will scrape the url attached(Hermes announcement/update url) in post content and use company's logo as preview image.
    # Hard code for ASX:WZR, because they create their own version of meta image for LinkedIn.
    linkedin_thumbail_url =
      if (listing_key == "wzr" and market_key == :asx) && is_nil(linkedin_thumbail_url) &&
           channel == :linkedin,
         do: "https://storage.googleapis.com/leaf-prod/uploads/public/WZR%20Investor%20Hub%20Meta%20image.png",
         else: linkedin_thumbail_url

    with {:check_published, false} <-
           {:check_published, published_to_social?(distributed_social, channel)},
         {:check_social, true} <-
           {:check_social, social_connected?(social_connection, channel)},
         {:ok, base_url} <-
           Companies.get_company_url(company_profile),
         {:ok, content_to_post, content_url, content_title} <-
           get_content_to_post(
             media,
             listing_key,
             base_url,
             social_post_template,
             channel
           ),
         {:ok, formatted_content_to_post} <-
           format_content_to_post(content_to_post, channel),
         {:ok, %DistributedSocial{} = distributed_social} <-
           publish_to_social(
             channel,
             social_connection,
             media,
             formatted_content_to_post,
             content_url,
             content_title,
             linkedin_thumbail_url
           ) do
      {:ok, distributed_social}
    else
      error ->
        {:error, error}
    end
  end

  defp published_to_social?(nil, _channel), do: false
  defp published_to_social?(%DistributedSocial{linkedin_post_id: nil}, :linkedin), do: false
  defp published_to_social?(%DistributedSocial{twitter_post_id: nil}, :twitter), do: false
  defp published_to_social?(_distributed_social, _channel), do: true

  defp social_connected?(social_connection, :linkedin) do
    Companies.get_is_linkedin_setup_completed(social_connection)
  end

  defp social_connected?(social_connection, :twitter) do
    Companies.get_is_twitter_setup_completed(social_connection)
  end

  # Replace the merge tags with the real value from media
  defp get_content_to_post(
         %Media{id: media_id, media_announcement: %MediaAnnouncement{id: id, header: header}},
         listing_key,
         base_url,
         text,
         channel
       ) do
    announcement_url =
      base_url
      |> URI.parse()
      |> URI.merge(
        "/announcements/#{id}?utm_medium=social&utm_source=#{Atom.to_string(channel)}&utm_campaign=mid-#{media_id}"
      )
      |> URI.to_string()

    post_content =
      text
      |> String.replace("{{ announcement_title }}", header)
      |> String.replace(
        "{{ announcement_url }}",
        announcement_url
      )
      |> String.replace("{{ ticker }}", String.upcase(listing_key))

    {:ok, post_content, announcement_url, header}
  end

  defp get_content_to_post(
         %Media{id: media_id, media_update: %MediaUpdate{slug: slug, title: title}},
         listing_key,
         base_url,
         text,
         channel
       ) do
    update_url =
      base_url
      |> URI.parse()
      |> URI.merge(
        "/activity-updates/#{slug}?utm_medium=social&utm_source=#{Atom.to_string(channel)}&utm_campaign=mid-#{media_id}"
      )
      |> URI.to_string()

    post_content =
      text
      |> String.replace("{{ update_title }}", title)
      |> String.replace(
        "{{ update_url }}",
        update_url
      )
      |> String.replace("{{ ticker }}", String.upcase(listing_key))

    {:ok, post_content, update_url, title}
  end

  # With content_formatted field, we no longer need complex formatting logic
  # Simple pass-through for content formatting
  defp format_content_to_post(post_content, _), do: {:ok, post_content}

  defp publish_to_social(
         :linkedin,
         %SocialConnection{
           company_profile_id: company_profile_id,
           linkedin_access_token: access_token,
           linkedin_organisation_id: organisation_id
         },
         %Media{id: media_id},
         formatted_post_content,
         content_url,
         title,
         thumbnail_url
       ) do
    with {:ok, post_id} <-
           LinkedIn.share_post_to_feed(
             access_token,
             formatted_post_content,
             organisation_id,
             content_url,
             title,
             thumbnail_url
           ),
         {:ok, %DistributedSocial{} = distributed_social} <-
           Flows.upsert_distributed_social(media_id, %{
             company_profile_id: company_profile_id,
             linkedin_post_id: post_id,
             linkedin_posted_at: NaiveDateTime.utc_now(:second)
           }) do
      {:ok, distributed_social}
    end
  end

  defp publish_to_social(
         :twitter,
         %SocialConnection{
           company_profile_id: company_profile_id,
           twitter_oauth_token: token,
           twitter_oauth_token_secret: token_secret
         },
         %Media{id: media_id},
         formatted_post_content,
         _content_url,
         _title,
         thumbnail_url
       ) do
    tweet_function =
      if thumbnail_url,
        do: Twitter.new_tweet(formatted_post_content, token, token_secret, thumbnail_url),
        else: Twitter.new_tweet(formatted_post_content, token, token_secret)

    with {:ok, %{"data" => %{"id" => post_id}}} <-
           tweet_function,
         {:ok, %DistributedSocial{} = distributed_social} <-
           Flows.upsert_distributed_social(media_id, %{
             company_profile_id: company_profile_id,
             twitter_post_id: post_id,
             twitter_posted_at: NaiveDateTime.utc_now(:second)
           }) do
      {:ok, distributed_social}
    end
  end

  #########################################################################
  # Decides to distribute announcement and where                          #
  #########################################################################

  @doc """
  Decide where to distribute the announcement and enqueued them as Oban jobs
  """
  def enqueue_announcement_distributions(%MediaAnnouncement{media: %Ecto.Association.NotLoaded{}} = announcement_input) do
    announcement_input
    |> Repo.preload(:media)
    |> enqueue_announcement_distributions()
  end

  def enqueue_announcement_distributions(%MediaAnnouncement{media: %Media{}} = announcement_input) do
    %MediaAnnouncement{media: %Media{company_profile_id: company_profile_id}, subtypes: subtypes} =
      media_announcement =
      Repo.preload(announcement_input,
        media: [:email, :distributed_social, company_profile: [:social_connection]]
      )

    :announcement
    |> Flows.get_active_settings_by_flow_for_company(company_profile_id)
    |> Enum.each(fn
      %DistributionSettings{included_announcement_types: included_announcement_types} = setting ->
        if not is_nil(included_announcement_types) and
             Enum.any?(subtypes, &(&1 in included_announcement_types)) do
          enqueue_announcement_distributions_exec(media_announcement, setting)
        else
          :skip
        end
    end)
  end

  # Announcements for company that are not on InvestorHub won't have media_id
  # Do not distribute them
  def enqueue_announcement_distributions(_), do: :ok

  defp enqueue_announcement_distributions_exec(
         %MediaAnnouncement{id: announcement_id, media: %Media{email: nil}},
         %DistributionSettings{channel: :email, is_active: true}
       ) do
    Gaia.Jobs.DistributeAnnouncement.enqueue(%{
      media_announcement_id: announcement_id,
      channel: :email
    })
  end

  defp enqueue_announcement_distributions_exec(
         %MediaAnnouncement{
           id: announcement_id,
           media: %Media{
             company_profile: %Profile{social_connection: social_connection},
             distributed_social: distributed_social
           }
         },
         %DistributionSettings{channel: :linkedin, is_active: true}
       ) do
    if Companies.get_is_linkedin_setup_completed(social_connection) and
         (is_nil(distributed_social) or is_nil(distributed_social.linkedin_post_id)) do
      Gaia.Jobs.DistributeAnnouncement.enqueue(%{
        media_announcement_id: announcement_id,
        channel: :linkedin
      })
    else
      :skip
    end
  end

  defp enqueue_announcement_distributions_exec(
         %MediaAnnouncement{
           id: announcement_id,
           media: %Media{
             company_profile: %Profile{social_connection: social_connection},
             distributed_social: distributed_social
           }
         },
         %DistributionSettings{channel: :twitter, is_active: true}
       ) do
    if Companies.get_is_twitter_setup_completed(social_connection) and
         (is_nil(distributed_social) or is_nil(distributed_social.twitter_post_id)) do
      Gaia.Jobs.DistributeAnnouncement.enqueue(%{
        media_announcement_id: announcement_id,
        channel: :twitter
      })
    else
      :skip
    end
  end

  defp enqueue_announcement_distributions_exec(_, _), do: :skip

  #########################################################################
  # Decides to distribute activity update and where                       #
  #########################################################################
  def enqueue_update_distributions(%MediaUpdate{} = update_input) do
    %MediaUpdate{
      media: %Media{company_profile_id: company_profile_id},
      included_types: included_types
    } =
      media_update =
      Repo.preload(update_input,
        media: [:email, :distributed_social, company_profile: [:social_connection]]
      )

    :update
    |> Flows.get_active_settings_by_flow_for_company(company_profile_id)
    |> Enum.each(fn
      %DistributionSettings{included_update_types: included_update_types} = setting ->
        if not is_nil(included_update_types) and not is_nil(included_types) and
             not MapSet.disjoint?(MapSet.new(included_update_types), MapSet.new(included_types)) do
          enqueue_update_distributions_exec(media_update, setting)
        else
          :skip
        end
    end)
  end

  defp enqueue_update_distributions_exec(%MediaUpdate{id: update_id, media: %Media{email: nil}}, %DistributionSettings{
         channel: :email,
         is_active: true
       }) do
    Gaia.Jobs.DistributeUpdate.enqueue(%{media_update_id: update_id, channel: :email})
  end

  defp enqueue_update_distributions_exec(
         %MediaUpdate{
           id: update_id,
           media: %Media{
             company_profile: %Profile{social_connection: social_connection},
             distributed_social: distributed_social
           }
         },
         %DistributionSettings{channel: :linkedin, is_active: true}
       ) do
    if Companies.get_is_linkedin_setup_completed(social_connection) and
         (is_nil(distributed_social) or is_nil(distributed_social.linkedin_post_id)) do
      Gaia.Jobs.DistributeUpdate.enqueue(%{media_update_id: update_id, channel: :linkedin})
    else
      :skip
    end
  end

  defp enqueue_update_distributions_exec(
         %MediaUpdate{
           id: update_id,
           media: %Media{
             company_profile: %Profile{social_connection: social_connection},
             distributed_social: distributed_social
           }
         },
         %DistributionSettings{channel: :twitter, is_active: true}
       ) do
    if Companies.get_is_twitter_setup_completed(social_connection) and
         (is_nil(distributed_social) or is_nil(distributed_social.twitter_post_id)) do
      Gaia.Jobs.DistributeUpdate.enqueue(%{media_update_id: update_id, channel: :twitter})
    else
      :skip
    end
  end

  defp enqueue_update_distributions_exec(_, _), do: :skip

  def get_automated_distribution_email_settings_input(
        %DistributionSettings{
          email_settings: %DistributionSettingsEmail{
            from_name: from_name,
            subject: subject,
            email_html: email_html,
            email_json: email_json
          },
          included_announcement_types: included_announcement_types,
          included_update_types: included_update_types,
          recipient_list_type: recipient_list_types
        } = distribution_setting,
        media_type
      ) do
    configured_included_types =
      if media_type == :announcement, do: included_announcement_types, else: included_update_types

    {
      :ok,
      distribution_setting
      |> Map.take([
        :company_profile_id,
        :do_not_send_to_contact_ids,
        :do_not_send_to_dynamic_list_ids,
        :excluded_contacts,
        :send_to_all_contacts,
        :send_to_contact_ids,
        :send_to_dynamic_list_ids
      ])
      |> Map.merge(%{
        configured_included_types: configured_included_types,
        email_html: email_html,
        email_json: email_json,
        from_name: from_name,
        recipient_list_types: recipient_list_types,
        subject: subject
      })
    }
  end

  def get_automated_distribution_email_settings_input(_), do: {:error, "Cannot get distribution settings"}

  def automated_distribute_to_email(%Media{} = media, distribution_settings, media_type) do
    with {:email_settings_input, {:ok, email_settings_input}} <-
           {:email_settings_input, get_automated_distribution_email_settings_input(distribution_settings, media_type)},
         {:distribute_to_email, :ok} <-
           {:distribute_to_email, distribute_to_email(media, email_settings_input, :automated)} do
      :ok
    end
  end

  def distribute_to_email(
        %Media{
          id: media_id,
          media_announcement: media_announcement,
          media_update: media_update,
          company_profile: %Profile{
            id: company_profile_id,
            custom_emails: custom_emails,
            ticker: %Ticker{listing_key: listing_key, market_key: market_key}
          }
        } = media,
        %{
          subject: _subject,
          from_name: from_name,
          email_html: email_html,
          email_json: email_json,
          recipient_list_types: _recipient_list_types,
          excluded_contacts: _excluded_contacts,
          configured_included_types: configured_included_types,
          do_not_send_to_contact_ids: do_not_send_to_contact_ids,
          do_not_send_to_dynamic_list_ids: do_not_send_to_dynamic_list_ids,
          send_to_all_contacts: send_to_all_contacts,
          send_to_contact_ids: send_to_contact_ids,
          send_to_dynamic_list_ids: send_to_dynamic_list_ids
        } = email_settings,
        email_distribution_method
      ) do
    with {:get_distribution_included_types, {:ok, included_types_input}} <-
           {:get_distribution_included_types, get_distribution_included_types(media_announcement, media_update)},
         {:get_campaign_name, {:ok, campaign_name}} <-
           {:get_campaign_name, get_campaign_name(media_announcement, media_update)},
         {:is_distribution_configured, true} <-
           {:is_distribution_configured,
            is_distribution_configured(
              included_types_input,
              configured_included_types,
              email_distribution_method
            )},
         {:custom_marketing_email, %CustomEmail{send_from_email: send_from_email}} <-
           {:custom_marketing_email, Gaia.Comms.get_custom_email_by_type(custom_emails, :marketing)},
         {:get_unsubscribe_scope, {:ok, unsubscribe_scope}} <-
           {:get_unsubscribe_scope, get_unsubscribe_scope(media_announcement, media_update)},
         {:get_contacts, contacts} <-
           {:get_contacts, get_email_recipients(email_settings, unsubscribe_scope)},
         {:update_media_and_create_distribution_email_and_email_recipients,
          {:ok, %{create_distribution_email: %Email{id: created_distribution_email_id}}}} <-
           {:update_media_and_create_distribution_email_and_email_recipients,
            Ecto.Multi.new()
            |> Ecto.Multi.update(
              :update_media,
              Ecto.Changeset.change(media, %{email_distribution_method: email_distribution_method})
            )
            |> create_distribution_email_and_email_recipients(
              %{
                from_name: from_name,
                from_email: send_from_email,
                email_html: email_html,
                email_json: email_json,
                media_id: media_id,
                campaign_name:
                  "#{Helper.ExDay.now_date() |> Timex.format("{0D}/{0M}/{YYYY}") |> elem(1)} #{campaign_name}",
                subject:
                  "#{market_key |> Atom.to_string() |> String.upcase()}:#{String.upcase(listing_key)} - #{campaign_name}",
                company_profile_id: company_profile_id,
                is_draft: false,
                sent_at: NaiveDateTime.utc_now(:second),
                do_not_send_to_contact_ids: do_not_send_to_contact_ids,
                do_not_send_to_dynamic_list_ids: do_not_send_to_dynamic_list_ids,
                send_to_all_contacts: send_to_all_contacts,
                send_to_contact_ids: send_to_contact_ids,
                send_to_dynamic_list_ids: send_to_dynamic_list_ids,
                inserted_at: NaiveDateTime.utc_now(:second),
                updated_at: NaiveDateTime.utc_now(:second)
              },
              contacts
            )
            |> Repo.transaction()},
         {:queue_email, {:ok, %Oban.Job{}}} <-
           {:queue_email, Gaia.Jobs.SendEmail.enqueue(%{"email_id" => created_distribution_email_id})} do
      :ok
    end
  end

  def get_campaign_name(%MediaAnnouncement{header: header} = _ma, media_update) when is_nil(media_update) do
    {:ok, header}
  end

  def get_campaign_name(media_announcement, %MediaUpdate{title: title} = _mu) when is_nil(media_announcement) do
    {:ok, title}
  end

  def get_distribution_included_types(%MediaAnnouncement{subtypes: subtypes} = _media_announcement, media_update)
      when is_nil(media_update) do
    {:ok, subtypes}
  end

  def get_distribution_included_types(media_announcement, %MediaUpdate{included_types: included_types} = _media_update)
      when is_nil(media_announcement) do
    {:ok, included_types}
  end

  def get_distribution_included_types(_media_announcement, _media_update),
    do: {:error, "Cannot get distribution included types"}

  def is_distribution_configured(_included_types_input, _configured_included_types, :manual), do: true

  def is_distribution_configured(included_types_input, configured_included_types, _email_distribution_method)
      when not is_nil(included_types_input) and not is_nil(configured_included_types) do
    Enum.any?(included_types_input, &(&1 in configured_included_types))
  end

  def is_distribution_configured(_included_types_input, _configured_included_types, _email_distribution_method), do: false

  # Use old system if dynamic list has not been configured as recipients
  def get_email_recipients(
        %{
          company_profile_id: company_profile_id,
          excluded_contacts: excluded_contacts,
          recipient_list_types: recipient_list_types,
          send_to_all_contacts: false,
          send_to_contact_ids: [],
          send_to_dynamic_list_ids: []
        },
        unsubscribe_scope
      ) do
    Interactions.get_distribution_contacts(
      company_profile_id,
      recipient_list_types,
      excluded_contacts,
      unsubscribe_scope
    )
  end

  # Otherwise, use dynamic list configuration
  def get_email_recipients(
        %{
          company_profile_id: _company_profile_id,
          do_not_send_to_contact_ids: _do_not_send_to_contact_ids,
          do_not_send_to_dynamic_list_ids: _do_not_send_to_dynamic_list_ids,
          send_to_all_contacts: _send_to_all_contacts,
          send_to_contact_ids: _send_to_contact_ids,
          send_to_dynamic_list_ids: _send_to_dynamic_list_ids
        } = configuration,
        unsubscribe_scope
      ) do
    Gaia.Comms.generate_email_recipients(configuration, unsubscribe_scope)
  end

  def get_unsubscribe_scope(%MediaAnnouncement{} = _media_announcement, media_update) when is_nil(media_update) do
    {:ok, "announcement"}
  end

  def get_unsubscribe_scope(media_announcement, %MediaUpdate{} = _media_update) when is_nil(media_announcement) do
    {:ok, "activity_update"}
  end

  def get_unsubscribe_scope(_media_announcement, _media_update), do: {:error, "Cannot get unsubscribe scope"}

  defp create_distribution_email_and_email_recipients(multi, email_input, contacts) do
    multi
    |> Ecto.Multi.insert(:create_distribution_email, Email.changeset(%Email{}, email_input),
      on_conflict: {:replace_all_except, [:id, :media_id, :inserted_at]},
      conflict_target: {:unsafe_fragment, ~s<("media_id") WHERE invalidated IS FALSE>}
    )
    |> Ecto.Multi.run(
      :create_distribution_email_recipients,
      fn _, %{create_distribution_email: %Email{id: new_email_id}} ->
        results =
          contacts
          |> Enum.filter(&(not is_nil(&1.email)))
          |> Enum.map(
            &%{
              email_id: new_email_id,
              contact_id: &1.id,
              inserted_at: NaiveDateTime.utc_now(:second),
              updated_at: NaiveDateTime.utc_now(:second)
            }
          )
          |> Enum.chunk_every(5_000)
          |> Enum.map(&Repo.insert_all(EmailRecipient, &1))

        {:ok, results}
      end
    )
  end
end
