defmodule Gaia.Interactions.MediaStatsCache do
  @moduledoc """
  Handles caching and refreshing of media stats across all distribution channels.

  This module manages the complex logic of determining which media items need
  stats refresh based on their published distribution dates and cache staleness.
  """

  import Ecto.Query, warn: false

  alias <PERSON><PERSON>.Companies
  alias Gaia.Interactions.Media
  alias Gaia.Repo
  alias Gaia.Socials.LinkedIn

  require Logger

  @doc """
  Get recent media that need hourly stats refresh.
  Targets media with distributions published in the last 2 weeks.
  """
  def get_recent_media_for_refresh(batch_size \\ 500) do
    hour_ago = hours_ago(1)
    two_weeks_ago = 2 |> weeks_ago() |> DateTime.to_naive()

    potentially_recent_media = fetch_potentially_recent_media(hour_ago, two_weeks_ago, batch_size)
    filter_truly_recent_media(potentially_recent_media)
  end

  @doc """
  Get older media that need daily stats refresh.
  Targets media with stale cache that hasn't been updated recently.
  """
  def get_older_media_for_refresh(batch_size \\ 200) do
    day_ago = days_ago(1)

    # Find media with stale daily cache
    # This will include both truly old media and unpublished media
    try do
      Media
      |> where([m], m.invalidated == false)
      |> where([m], is_nil(m.cached_stats_last_updated) or m.cached_stats_last_updated < ^day_ago)
      |> preload(^media_associations())
      # Prioritize never-cached, then oldest cache
      |> order_by([m], asc: m.cached_stats_last_updated, desc: m.inserted_at)
      |> limit(^batch_size)
      |> Repo.all()
      |> Enum.filter(fn media ->
        # Only process media that actually has published distributions
        # This excludes draft-only media
        Media.get_latest_published_date(media) != nil
      end)
    rescue
      error ->
        Logger.error("Database error fetching older media: #{inspect(error)}")
        []
    end
  end

  @doc """
  Get very stale media for cleanup jobs.
  """
  def get_stale_media_for_refresh(stale_days, batch_size \\ 100) do
    stale_cutoff = days_ago(stale_days)

    try do
      Media
      |> where([m], m.invalidated == false)
      |> where([m], is_nil(m.cached_stats_last_updated) or m.cached_stats_last_updated < ^stale_cutoff)
      |> preload(^media_associations())
      |> order_by([m], asc: m.cached_stats_last_updated, desc: m.inserted_at)
      |> limit(^batch_size)
      |> Repo.all()
      |> Enum.filter(fn media ->
        # Only process media that actually has published distributions
        Media.get_latest_published_date(media) != nil
      end)
    rescue
      error ->
        Logger.error("Database error fetching stale media: #{inspect(error)}")
        []
    end
  end

  @doc """
  Calculate and cache stats for a single media item.
  Updates cached_stats_total_impressions, cached_stats, and cached_stats_last_updated.
  """
  def calculate_and_cache_stats(%Media{} = media) do
    company_profile_id = media.company_profile_id

    # Ensure necessary associations are preloaded
    media =
      case media do
        %{media_announcement: %Ecto.Association.NotLoaded{}} ->
          associations = media_associations()
          Repo.preload(media, associations)

        _ ->
          media
      end

    # Calculate stats from each distribution channel
    stats = %{
      email_views: calculate_email_views(media),
      announcement_views: calculate_announcement_views(media, company_profile_id),
      update_views: calculate_update_views(media, company_profile_id),
      linkedin_views: calculate_linkedin_views(media),
      twitter_views: calculate_twitter_views(media)
    }

    total_impressions =
      stats.email_views + stats.announcement_views + stats.update_views +
        stats.linkedin_views + stats.twitter_views

    # Update media with cached stats
    media
    |> Media.changeset(%{
      cached_stats_total_impressions: total_impressions,
      cached_stats: stats,
      cached_stats_last_updated: DateTime.utc_now()
    })
    |> Repo.update()
  end

  @doc """
  Orchestrate refresh of recent media stats by enqueuing individual jobs.
  """
  @spec refresh_recent_media_stats() ::
          {:ok, %{jobs_enqueued: non_neg_integer(), success: non_neg_integer(), errors: non_neg_integer()}}
  def refresh_recent_media_stats do
    recent_media = get_recent_media_for_refresh()

    Logger.info("Enqueuing #{length(recent_media)} recent media items for stats refresh")

    # Enqueue individual jobs for each media item
    job_results =
      Enum.map(recent_media, fn media ->
        Gaia.Jobs.CalculateMediaStatsJob.enqueue(%{"media_id" => media.id}, queue: :media_stats)
      end)

    success_count = Enum.count(job_results, fn {status, _} -> status == :ok end)
    error_count = length(job_results) - success_count

    Logger.info("Recent media stats jobs enqueued: #{success_count} success, #{error_count} errors")

    {:ok, %{jobs_enqueued: length(job_results), success: success_count, errors: error_count}}
  end

  @doc """
  Orchestrate refresh of older media stats with batch processing.
  """
  @spec refresh_older_media_stats(non_neg_integer()) ::
          {:ok,
           %{
             batches: non_neg_integer(),
             jobs_enqueued: non_neg_integer(),
             success: non_neg_integer(),
             errors: non_neg_integer()
           }}
  def refresh_older_media_stats(max_batches \\ 10) do
    total_enqueued = 0
    total_success = 0
    total_errors = 0
    batch_count = 0

    Logger.info("Starting older media stats refresh with up to #{max_batches} batches")

    # Process multiple batches until we've processed enough or no more media found
    result =
      Enum.reduce_while(1..max_batches, {total_enqueued, total_success, total_errors, batch_count}, fn batch_num,
                                                                                                       {enqueued, success,
                                                                                                        errors, count} ->
        older_media = get_older_media_for_refresh()

        if Enum.empty?(older_media) do
          Logger.info("No more older media found after #{batch_num - 1} batches")
          {:halt, {enqueued, success, errors, count}}
        else
          Logger.info("Processing batch #{batch_num}: enqueuing #{length(older_media)} older media items")

          batch_job_results =
            Enum.map(older_media, fn media ->
              Gaia.Jobs.CalculateMediaStatsJob.enqueue(%{"media_id" => media.id}, queue: :media_stats)
            end)

          batch_success = Enum.count(batch_job_results, fn {status, _} -> status == :ok end)
          batch_errors = length(batch_job_results) - batch_success

          new_enqueued = enqueued + length(batch_job_results)
          new_success = success + batch_success
          new_errors = errors + batch_errors
          new_count = count + 1

          Logger.info("Batch #{batch_num} jobs enqueued: #{batch_success} success, #{batch_errors} errors")

          {:cont, {new_enqueued, new_success, new_errors, new_count}}
        end
      end)

    # Enqueue individual jobs for each media item
    {total_enqueued, total_success, total_errors, batch_count} = result

    Logger.info(
      "Older media stats refresh completed: #{batch_count} batches, #{total_success} jobs enqueued successfully, #{total_errors} errors"
    )

    {:ok, %{batches: batch_count, jobs_enqueued: total_enqueued, success: total_success, errors: total_errors}}
  end

  @doc """
  Orchestrate refresh of very stale media stats.
  """
  @spec refresh_stale_media_stats(pos_integer(), non_neg_integer()) ::
          {:ok,
           %{
             batches: non_neg_integer(),
             jobs_enqueued: non_neg_integer(),
             success: non_neg_integer(),
             errors: non_neg_integer()
           }}
  def refresh_stale_media_stats(stale_days \\ 7, max_batches \\ 20) do
    total_enqueued = 0
    total_success = 0
    total_errors = 0
    batch_count = 0

    Logger.info("Starting stale media stats refresh (older than #{stale_days} days) with up to #{max_batches} batches")

    result =
      Enum.reduce_while(1..max_batches, {total_enqueued, total_success, total_errors, batch_count}, fn batch_num,
                                                                                                       {enqueued, success,
                                                                                                        errors, count} ->
        stale_media = get_stale_media_for_refresh(stale_days)

        if Enum.empty?(stale_media) do
          Logger.info("No more stale media found after #{batch_num - 1} batches")
          {:halt, {enqueued, success, errors, count}}
        else
          Logger.info("Processing stale batch #{batch_num}: enqueuing #{length(stale_media)} media items")

          batch_job_results =
            Enum.map(stale_media, fn media ->
              Gaia.Jobs.CalculateMediaStatsJob.enqueue(%{"media_id" => media.id}, queue: :media_stats)
            end)

          batch_success = Enum.count(batch_job_results, fn {status, _} -> status == :ok end)
          batch_errors = length(batch_job_results) - batch_success

          new_enqueued = enqueued + length(batch_job_results)
          new_success = success + batch_success
          new_errors = errors + batch_errors
          new_count = count + 1

          Logger.info("Stale batch #{batch_num} jobs enqueued: #{batch_success} success, #{batch_errors} errors")

          {:cont, {new_enqueued, new_success, new_errors, new_count}}
        end
      end)

    # Enqueue individual jobs for each media item
    {total_enqueued, total_success, total_errors, batch_count} = result

    Logger.info(
      "Stale media stats refresh completed: #{batch_count} batches, #{total_success} jobs enqueued successfully, #{total_errors} errors"
    )

    {:ok, %{batches: batch_count, jobs_enqueued: total_enqueued, success: total_success, errors: total_errors}}
  end

  # Private helper functions for get_recent_media_for_refresh complexity reduction
  defp fetch_potentially_recent_media(hour_ago, two_weeks_ago, batch_size) do
    Media
    |> apply_base_filters(hour_ago)
    |> add_media_joins()
    |> apply_recent_content_filter(two_weeks_ago)
    |> preload(^media_associations())
    |> order_by([m], desc: m.inserted_at)
    |> limit(^batch_size)
    |> Repo.all()
  rescue
    error ->
      Logger.error("Database error fetching recent media: #{inspect(error)}")
      []
  end

  defp apply_base_filters(query, hour_ago) do
    query
    |> where([m], m.invalidated == false)
    |> where([m], is_nil(m.cached_stats_last_updated) or m.cached_stats_last_updated < ^hour_ago)
  end

  defp add_media_joins(query) do
    query
    |> join(:left, [m], mu in assoc(m, :media_update))
    |> join(:left, [m], ma in assoc(m, :media_announcement))
    |> join(:left, [m], e in assoc(m, :email))
    |> join(:left, [m], lsp in assoc(m, :linkedin_social_post))
    |> join(:left, [m], tsp in assoc(m, :twitter_social_post))
  end

  # credo:disable-for-next-line
  defp apply_recent_content_filter(query, two_weeks_ago) do
    where(
      query,
      [m, mu, ma, e, lsp, tsp],
      ^dynamic(
        [mu, ma, e, lsp, tsp],
        (not is_nil(mu.posted_at) and mu.posted_at >= ^two_weeks_ago) or
          (not is_nil(ma.posted_at) and ma.posted_at >= ^two_weeks_ago) or
          (not is_nil(e.sent_at) and e.sent_at >= ^two_weeks_ago) or
          (not is_nil(lsp.published_at) and lsp.published_at >= ^two_weeks_ago) or
          (not is_nil(tsp.published_at) and tsp.published_at >= ^two_weeks_ago)
      )
    )
  end

  defp filter_truly_recent_media(potentially_recent_media) do
    recent_cutoff_date = weeks_ago(2)

    Enum.filter(potentially_recent_media, fn media ->
      media
      |> Media.get_latest_published_date()
      |> is_published_within_cutoff?(recent_cutoff_date)
    end)
  end

  defp is_published_within_cutoff?(nil, _cutoff_date), do: false

  defp is_published_within_cutoff?(published_date, cutoff_date) do
    case DateTime.from_naive(published_date, "Etc/UTC") do
      {:ok, published_datetime} -> DateTime.after?(published_datetime, cutoff_date)
      {:error, _} -> false
    end
  end

  # Private helper functions for date/time calculations
  defp time_ago(amount, unit) do
    DateTime.add(DateTime.utc_now(), -amount, unit)
  end

  defp hours_ago(hours), do: time_ago(hours * 60 * 60, :second)
  defp days_ago(days), do: time_ago(days * 24 * 60 * 60, :second)
  defp weeks_ago(weeks), do: time_ago(weeks * 7 * 24 * 60 * 60, :second)
  defp minutes_ago(minutes), do: time_ago(minutes * 60, :second)

  defp media_associations do
    [
      :media_announcement,
      :media_update,
      :email,
      :linkedin_social_post,
      :twitter_social_post
    ]
  end

  # Private helper functions for calculating stats from different sources
  defp calculate_email_views(%{email: %{id: email_id}}) do
    Gaia.Comms.count_total_recipient_events_by_email(email_id, :Open) || 0
  rescue
    _ -> 0
  end

  defp calculate_email_views(_), do: 0

  defp calculate_announcement_views(%{media_announcement: %{id: announcement_id}}, company_profile_id) do
    Gaia.Tracking.count_interactive_announcement_views(announcement_id, company_profile_id)
  rescue
    _ -> 0
  end

  defp calculate_announcement_views(_, _), do: 0

  defp calculate_update_views(%{media_update: %{id: update_id, slug: slug}}, company_profile_id) do
    Gaia.Tracking.count_activity_update_views_with_post_id_and_slug(
      update_id,
      URI.encode(slug),
      company_profile_id
    )
  rescue
    _ -> 0
  end

  defp calculate_update_views(_, _), do: 0

  defp calculate_linkedin_views(%{
         linkedin_social_post: %{social_post_id: post_id, media_id: media_id} = social_post,
         company_profile_id: company_profile_id
       })
       when not is_nil(post_id) do
    cached_count = get_cached_linkedin_views(social_post)

    if should_refresh_linkedin_data?(social_post) do
      fetch_fresh_linkedin_data(post_id, company_profile_id, media_id, cached_count)
    else
      Logger.debug("Using cached LinkedIn data for post #{post_id}: #{cached_count} impressions")
      cached_count
    end
  end

  defp calculate_linkedin_views(_), do: 0

  defp fetch_fresh_linkedin_data(post_id, company_profile_id, media_id, cached_count) do
    Logger.debug("LinkedIn data stale for post #{post_id}, fetching fresh data (cached: #{cached_count})")

    case fetch_live_linkedin_statistics(post_id, company_profile_id, media_id) do
      {:ok, impression_count} ->
        Logger.debug("LinkedIn refresh successful for post #{post_id}: #{cached_count} → #{impression_count}")
        impression_count

      {:error, reason} ->
        Logger.warning(
          "Failed to fetch LinkedIn stats for post #{post_id}: #{inspect(reason)}, using cached: #{cached_count}"
        )

        cached_count
    end
  end

  # Get impression count from cached analytics_data
  defp get_cached_linkedin_views(%{analytics_data: %{"impression_count" => count}}) when is_integer(count), do: count
  defp get_cached_linkedin_views(%{analytics_data: %{impression_count: count}}) when is_integer(count), do: count
  defp get_cached_linkedin_views(_), do: 0

  # Determine if LinkedIn data should be refreshed
  # Strategy: Refresh if no analytics_data OR if analytics_data doesn't have last_updated timestamp OR timestamp is stale
  # No cached data
  defp should_refresh_linkedin_data?(%{analytics_data: nil}), do: true

  defp should_refresh_linkedin_data?(%{analytics_data: %{"last_updated" => last_updated_str}}) do
    case DateTime.from_iso8601(last_updated_str) do
      {:ok, last_updated_dt, _} ->
        thirty_minutes_ago = minutes_ago(30)
        DateTime.before?(last_updated_dt, thirty_minutes_ago)

      {:error, _} ->
        # Invalid timestamp format, refresh
        true
    end
  end

  # No last_updated field, refresh
  defp should_refresh_linkedin_data?(%{analytics_data: %{}}), do: true
  # Default to refresh
  defp should_refresh_linkedin_data?(_), do: true

  # Fetch live LinkedIn statistics via API
  defp fetch_live_linkedin_statistics(post_id, company_profile_id, media_id) do
    Logger.debug(
      "Fetching live LinkedIn statistics for post #{post_id}, media #{media_id}, company #{company_profile_id}"
    )

    with {:ok, social_connection} <- get_linkedin_connection(company_profile_id),
         true <- Companies.get_is_linkedin_connected(social_connection),
         %{linkedin_access_token: access_token, linkedin_organisation_id: organisation_id}
         when not is_nil(access_token) and not is_nil(organisation_id) <- social_connection do
      Logger.debug("LinkedIn connection validated for company #{company_profile_id}, calling API for post #{post_id}")

      case LinkedIn.post_statistics(access_token, organisation_id, post_id) do
        {:ok, %{impression_count: count}} when is_integer(count) ->
          Logger.info("LinkedIn API success for post #{post_id}: #{count} impressions")
          # Update the social post with fresh data for future cache hits
          analytics_with_timestamp = %{impression_count: count, last_updated: DateTime.to_iso8601(DateTime.utc_now())}
          update_social_post_analytics(media_id, analytics_with_timestamp)
          {:ok, count}

        {:ok, stats} ->
          count = extract_impression_count(stats)
          Logger.info("LinkedIn API success for post #{post_id}: #{count} impressions (extracted from #{inspect(stats)})")
          # Handle case where impression_count might be in different format
          analytics_with_timestamp = Map.put(stats, :last_updated, DateTime.to_iso8601(DateTime.utc_now()))
          update_social_post_analytics(media_id, analytics_with_timestamp)
          {:ok, count}

        {:error, reason} ->
          Logger.error("LinkedIn API error for post #{post_id}: #{inspect(reason)}")
          {:error, reason}
      end
    else
      {:error, reason} ->
        Logger.warning("LinkedIn connection error for company #{company_profile_id}: #{inspect(reason)}")
        {:error, reason}

      false ->
        Logger.warning("LinkedIn not connected for company #{company_profile_id}")
        {:error, :linkedin_not_connected}

      _ ->
        Logger.warning("Invalid LinkedIn connection data for company #{company_profile_id}")
        {:error, :invalid_connection_data}
    end
  end

  defp get_linkedin_connection(company_profile_id) do
    case Companies.get_social_connection_by(%{company_profile_id: company_profile_id}) do
      nil -> {:error, :no_linkedin_connection}
      connection -> {:ok, connection}
    end
  end

  defp extract_impression_count(%{impression_count: count}) when is_integer(count), do: count
  defp extract_impression_count(%{"impression_count" => count}) when is_integer(count), do: count
  defp extract_impression_count(_), do: 0

  defp update_social_post_analytics(media_id, analytics_data) do
    # Update the LinkedIn social post with fresh analytics data
    # This helps cache the data for future use
    Task.start(fn ->
      case Repo.get_by(Gaia.Interactions.SocialPost, media_id: media_id, platform: :linkedin) do
        # Social post not found, ignore
        nil ->
          :ok

        social_post ->
          case Gaia.Interactions.update_social_post(social_post, %{analytics_data: analytics_data}) do
            {:ok, _} ->
              :ok

            {:error, reason} ->
              Logger.warning("Failed to update LinkedIn social post analytics: #{inspect(reason)}")
          end
      end
    end)
  end

  defp calculate_twitter_views(%{twitter_social_post: %{analytics_data: %{"impression_count" => count}}})
       when is_integer(count),
       do: count

  defp calculate_twitter_views(%{twitter_social_post: %{analytics_data: %{impression_count: count}}})
       when is_integer(count),
       do: count

  defp calculate_twitter_views(_), do: 0
end
