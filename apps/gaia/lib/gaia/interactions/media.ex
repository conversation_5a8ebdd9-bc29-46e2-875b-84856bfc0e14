defmodule Gaia.Interactions.Media do
  @moduledoc """
  Media represents a piece of content that can have multiple distributions:
  - Announcement
  - Update
  - LinkedIn
  - Twitter
  - Email
  """

  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query

  alias Gaia.Repo

  require Ecto.Query

  @distribution_method_types [:automated, :manual]

  schema "interactions_medias" do
    field(:title, :string)
    field(:draft_content, :map)
    field(:target_date, :utc_datetime)
    field(:distribution_announcement_enabled, :boolean, default: false)
    field(:distribution_update_enabled, :boolean, default: false)
    field(:distribution_linkedin_enabled, :boolean, default: false)
    field(:distribution_twitter_enabled, :boolean, default: false)
    field(:distribution_email_enabled, :boolean, default: false)
    field(:primary_distribution, :string)

    field(:invalidated, :boolean, default: false)
    field(:email_distribution_method, Ecto.Enum, values: @distribution_method_types)

    # Cached stats fields
    field(:cached_stats_total_impressions, :integer, default: 0)
    field(:cached_stats, :map)
    field(:cached_stats_last_updated, :utc_datetime)

    belongs_to(:company_profile, Gaia.Companies.Profile)

    has_many(:tags, Gaia.Interactions.MediaTag, where: [invalidated: false])

    has_many(:comments, Gaia.Interactions.MediaComment, foreign_key: :media_id, references: :id)

    has_many(:likes, Gaia.Interactions.MediaLike,
      foreign_key: :media_id,
      references: :id,
      where: [like: true]
    )

    has_many(:ai_media_conversions, Gaia.AI.MediaConversion,
      foreign_key: :media_id,
      references: :id
    )

    has_one(:media_announcement, Gaia.Interactions.MediaAnnouncement,
      foreign_key: :media_id,
      references: :id
    )

    has_one(:prepared_announcement, Gaia.Interactions.PreparedAnnouncement,
      foreign_key: :media_id,
      references: :id
    )

    has_one(:media_update, Gaia.Interactions.MediaUpdate,
      foreign_key: :media_id,
      references: :id
    )

    has_one(:email, Gaia.Comms.Email, foreign_key: :media_id)

    has_one(:distributed_social, Gaia.Flows.DistributedSocial, foreign_key: :media_id)

    has_one(:linkedin_social_post, Gaia.Interactions.SocialPost, where: [platform: :linkedin])
    has_one(:twitter_social_post, Gaia.Interactions.SocialPost, where: [platform: :twitter])

    belongs_to(:created_by_profile_user, Gaia.Companies.ProfileUser,
      foreign_key: :created_by_profile_user_id,
      references: :id
    )

    timestamps()
  end

  @doc false
  def changeset(media, attrs) do
    media
    |> cast(attrs, [
      :company_profile_id,
      :invalidated,
      :email_distribution_method,
      :title,
      :draft_content,
      :target_date,
      :created_by_profile_user_id,
      :distribution_announcement_enabled,
      :distribution_update_enabled,
      :distribution_linkedin_enabled,
      :distribution_twitter_enabled,
      :distribution_email_enabled,
      :primary_distribution,
      :cached_stats_total_impressions,
      :cached_stats,
      :cached_stats_last_updated
    ])
    |> validate_required([:company_profile_id])
    |> maybe_update_primary_distribution()
  end

  # Automatically calculate and set primary_distribution when distribution flags change
  defp maybe_update_primary_distribution(changeset) do
    if distribution_flags_changed?(changeset) do
      # Get the new values from the changeset
      new_data = Ecto.Changeset.apply_changes(changeset)
      primary_distribution = calculate_primary_distribution(new_data)

      Ecto.Changeset.put_change(changeset, :primary_distribution, primary_distribution)
    else
      changeset
    end
  end

  @doc """
  Calculates the primary distribution for a media item based on enabled distribution flags.

  The primary distribution is determined by priority order:
  1. announcement (highest priority)
  2. update
  3. email
  4. linkedin
  5. twitter (lowest priority)

  Returns the highest priority distribution that is enabled, or nil if none are enabled.

  ## Examples

      iex> media = %Media{
      ...>   distribution_announcement_enabled: true,
      ...>   distribution_update_enabled: true
      ...> }
      iex> Media.calculate_primary_distribution(media)
      "announcement"

      iex> media = %Media{
      ...>   distribution_announcement_enabled: false,
      ...>   distribution_linkedin_enabled: true,
      ...>   distribution_twitter_enabled: true
      ...> }
      iex> Media.calculate_primary_distribution(media)
      "linkedin"
  """
  def calculate_primary_distribution(media) do
    cond do
      Map.get(media, :distribution_announcement_enabled, false) -> "announcement"
      Map.get(media, :distribution_update_enabled, false) -> "update"
      Map.get(media, :distribution_email_enabled, false) -> "email"
      Map.get(media, :distribution_linkedin_enabled, false) -> "linkedin"
      Map.get(media, :distribution_twitter_enabled, false) -> "twitter"
      true -> nil
    end
  end

  @doc """
  Checks if any distribution flags have changed in the given changeset.
  Returns true if any distribution-related fields are being updated.
  """
  def distribution_flags_changed?(changeset) do
    distribution_fields = [
      :distribution_announcement_enabled,
      :distribution_update_enabled,
      :distribution_email_enabled,
      :distribution_linkedin_enabled,
      :distribution_twitter_enabled
    ]

    Enum.any?(distribution_fields, fn field ->
      Ecto.Changeset.changed?(changeset, field)
    end)
  end

  def get_distribution_method_types, do: @distribution_method_types

  @doc """
  Resolves the media AI processing status based on the latest MediaConversion
  """
  def media_ai_processing_status(media, _args, _resolution) do
    latest_conversion = get_latest_conversion(media.id)
    get_processing_status(latest_conversion)
  end

  defp get_latest_conversion(media_id) do
    Gaia.AI.MediaConversion
    |> where([c], c.media_id == ^media_id)
    |> order_by([c], desc: c.inserted_at)
    |> limit(1)
    |> Gaia.Repo.one()
  end

  defp get_processing_status(nil), do: {:ok, nil}

  defp get_processing_status(%{oban_job_id: job_id}) when not is_nil(job_id) do
    job = get_oban_job(job_id)
    get_status_from_job(job)
  end

  defp get_processing_status(%{content: content}) when not is_nil(content), do: {:ok, "completed"}
  defp get_processing_status(_), do: {:ok, "failed"}

  defp get_oban_job(job_id) do
    Oban.Job
    |> Ecto.Query.where([j], j.id == ^job_id)
    |> Gaia.Repo.one()
  end

  # Job not found, but we have a job ID - assume completed
  defp get_status_from_job(nil), do: {:ok, "completed"}
  defp get_status_from_job(%{state: "completed"}), do: {:ok, "completed"}
  defp get_status_from_job(%{state: "executing"}), do: {:ok, "processing"}
  defp get_status_from_job(%{state: "retryable"}), do: {:ok, "retrying"}
  defp get_status_from_job(%{state: "discarded"}), do: {:ok, "failed"}
  defp get_status_from_job(%{state: state}), do: {:ok, state}

  @doc """
  Get the latest published distribution date for a media item.
  Checks all distribution types and returns the most recent published date.
  Returns nil if no distributions have been published.
  """
  def get_latest_published_date(media) do
    media =
      Repo.preload(media, [
        :media_announcement,
        :media_update,
        :email,
        :linkedin_social_post,
        :twitter_social_post
      ])

    published_dates = [
      get_media_update_published_date(media),
      get_media_announcement_published_date(media),
      get_email_published_date(media),
      get_linkedin_published_date(media),
      get_twitter_published_date(media)
    ]

    published_dates
    |> Enum.filter(&(&1 != nil))
    |> case do
      [] -> nil
      dates -> Enum.max(dates, NaiveDateTime)
    end
  end

  # Helper functions to get published dates from each distribution type
  defp get_media_update_published_date(%{media_update: %{posted_at: posted_at}}) when not is_nil(posted_at), do: posted_at
  defp get_media_update_published_date(_), do: nil

  defp get_media_announcement_published_date(%{media_announcement: %{posted_at: posted_at}}) when not is_nil(posted_at),
    do: posted_at

  defp get_media_announcement_published_date(_), do: nil

  defp get_email_published_date(%{email: %{sent_at: sent_at}}) when not is_nil(sent_at), do: sent_at
  defp get_email_published_date(_), do: nil

  defp get_linkedin_published_date(%{linkedin_social_post: %{published_at: published_at}}) when not is_nil(published_at),
    do: published_at

  defp get_linkedin_published_date(_), do: nil

  defp get_twitter_published_date(%{twitter_social_post: %{published_at: published_at}}) when not is_nil(published_at),
    do: published_at

  defp get_twitter_published_date(_), do: nil
end
