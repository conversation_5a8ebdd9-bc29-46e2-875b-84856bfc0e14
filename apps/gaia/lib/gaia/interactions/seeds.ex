defmodule Gaia.Interactions.Seeds do
  @moduledoc false
  alias Gaia.Interactions
  alias Gaia.Repo

  def create_media_with_distributions(company_profile, count) do
    company_profile = Repo.preload(company_profile, [:profile_users, :ticker])
    company_profile_user = Enum.at(company_profile.profile_users, 0)

    Enum.map(1..count, fn _index ->
      is_announcement_enabled = Enum.random([true, false])
      is_update_enabled = if is_announcement_enabled, do: false, else: Enum.random([true, false])

      media =
        create_media(%{
          company_profile_id: company_profile.id,
          created_by_profile_user_id: company_profile_user.id,
          distribution_email_enabled: Enum.random([true, false]),
          distribution_linkedin_enabled: Enum.random([true, false]),
          distribution_twitter_enabled: Enum.random([true, false]),
          distribution_announcement_enabled: is_announcement_enabled,
          distribution_update_enabled: is_update_enabled
        })

      # Create announcement for media
      if is_announcement_enabled do
        create_media_announcement(%{
          listing_key: company_profile.ticker.listing_key,
          market_key: Atom.to_string(company_profile.ticker.market_key),
          media_id: media.id,
          posted_at: Faker.DateTime.backward(365)
        })
      end

      # Create update for media
      if is_update_enabled do
        %{id: media_update_id} =
          create_media_update(%{
            media_id: media.id,
            company_profile_id: media.company_profile_id,
            user_id: company_profile_user.id,
            body: Gaia.RichText.Tiptap.create_mining_company_update()
          })

        create_media_update_content(%{
          media_update_id: media_update_id,
          user_id: company_profile_user.id
        })
      end

      media
    end)
  end

  def create_media(%{company_profile_id: _} = attrs) do
    attrs =
      Enum.into(attrs, %{
        email_distribution_method: :automated,
        title: Faker.Company.catch_phrase(),
        state: :draft,
        draft_content: Gaia.RichText.Tiptap.create_mining_company_update(),
        # draft_content: Gaia.RichText.Blocknote.create_dummy_blog_post(),
        created_by_profile_user_id: 1
      })

    {:ok, media} = Interactions.create_media(attrs)
    media
  end

  def create_media_announcement(%{listing_key: _listing_key, market_key: _market_key} = attrs) do
    attrs =
      Enum.into(attrs, %{
        header: Faker.Company.catch_phrase(),
        posted_at: DateTime.utc_now(),
        summary: Faker.Lorem.sentence(),
        rectype: "10000",
        subtypes: ["10007", "10008", "10009"],
        url:
          "https://storage.googleapis.com/leaf-prod/test_data/prep_announcements/test-hub-ticker-link.pdf?id=" <>
            Ecto.UUID.generate()
      })

    {:ok, media_announcement} = Interactions.create_media_announcement(attrs)
    media_announcement
  end

  def create_media_update(%{media_id: media_id, company_profile_id: company_profile_id, user_id: user_id} = attrs) do
    %{
      media_id: media_id,
      company_profile_id: company_profile_id
    }
    |> Interactions.get_media_update_by()
    |> case do
      nil ->
        title = attrs[:title] || Faker.Company.catch_phrase()

        attrs =
          Enum.into(attrs, %{
            last_updated_by_id: user_id,
            posted_by_id: user_id,
            posted_at: DateTime.utc_now(),
            is_draft: false,
            title: title,
            type: :none,
            slug: Slug.slugify(title)
          })

        {:ok, media_update} = Interactions.create_newsflow_media_update(attrs)
        media_update

      %Interactions.MediaUpdate{} = media_update ->
        media_update
    end
  end

  def create_media_update_content(%{media_update_id: media_update_id, user_id: user_id} = attrs) do
    %{
      media_update_id: media_update_id
    }
    |> Interactions.get_media_update_content_by()
    |> case do
      nil ->
        attrs =
          Enum.into(attrs, %{
            updated_by_id: user_id,
            content: """
            <p class="typography-body-regular">#{Faker.Lorem.paragraph()}</p>
            <p class="typography-body-regular"><br></p>
            <p class="typography-body-regular">#{Faker.Lorem.paragraph()}</p>
            <p class="typography-body-regular"><br></p>
            <p class="typography-body-regular">#{Faker.Lorem.paragraph()}</p>
            """
          })

        {:ok, media_update_content} = Interactions.create_media_update_content(attrs)
        media_update_content

      %Interactions.MediaUpdateContent{} = media_update_content ->
        media_update_content
    end
  end

  def fill_content_calendar(%Gaia.Companies.Profile{} = company_profile) do
    medias = Interactions.list_media_by_company_profile_id(company_profile.id)

    months = [
      Date.beginning_of_month(Date.utc_today()),
      Date.beginning_of_month(Date.add(Date.utc_today(), -1 * 30)),
      Date.beginning_of_month(Date.add(Date.utc_today(), 30)),
      Date.beginning_of_month(Date.add(Date.utc_today(), 2 * 30))
    ]

    Enum.reduce(months, medias, fn month, available_medias ->
      selected_medias = Enum.take_random(available_medias, Enum.random(1..5))

      selected_medias
      |> Enum.with_index()
      |> Enum.each(fn {media, _index} ->
        # Create a random time on the given date
        # Business hours
        random_hour = Enum.random(9..17)
        random_minute = Enum.random(0..59)

        # Convert the date to a DateTime with the random time
        target_datetime =
          DateTime.new!(month, Time.new!(random_hour, random_minute, 0), "Etc/UTC")

        Interactions.update_media(media, %{
          target_date: target_datetime
        })
      end)

      available_medias -- selected_medias
    end)
  end

  def delete_all_media do
    if Application.get_env(:helper, :runtime_env) == "development" do
      Repo.delete_all(Interactions.MediaUpdateContent, really_delete: true)
      Repo.delete_all(Interactions.MediaUpdateAttachment, really_delete: true)
      Repo.delete_all(Interactions.MediaLike, really_delete: true)
      Repo.delete_all(Interactions.MediaTag, really_delete: true)
      Repo.delete_all(Interactions.MediaComment, really_delete: true)
      Repo.delete_all(Interactions.MediaCommentLike, really_delete: true)
      Repo.delete_all(Interactions.MediaCommentRead, really_delete: true)
      Repo.delete_all(Interactions.MediaCommentStar, really_delete: true)
      Repo.delete_all(Interactions.MediaSurveyAnswer, really_delete: true)
      Repo.delete_all(Interactions.MediaAnnouncement, really_delete: true)
      Repo.delete_all(Interactions.MediaUpdate, really_delete: true)
      Repo.delete_all(Interactions.Media, really_delete: true)
    end
  end
end
