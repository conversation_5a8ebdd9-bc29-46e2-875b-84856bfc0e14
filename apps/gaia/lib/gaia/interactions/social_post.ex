defmodule Gaia.Interactions.SocialPost do
  @moduledoc """
  These represent social posts to social media platforms (LinkedIn, Twitter, etc.).
  """

  use Ecto.Schema

  import Ecto.Changeset

  @platform_types [
    :linkedin,
    :twitter
  ]

  @status_types [
    :draft,
    :scheduled,
    :published,
    :failed
  ]

  def get_status_types, do: @status_types
  def get_platform_types, do: @platform_types

  schema "interactions_social_posts" do
    field(:content, :map)
    field(:content_formatted, :string)
    field(:social_post_id, :string)
    field(:status, Ecto.Enum, values: @status_types, default: :draft)
    field(:platform, Ecto.Enum, values: @platform_types)
    field(:scheduled_at, :naive_datetime)
    field(:published_at, :naive_datetime)
    field(:error_message, :string)
    field(:analytics_data, :map)
    field(:is_invalidated, :boolean, default: false)
    field(:published_url, :string)

    # Here’s the limit on media attachments for LinkedIn and Twitter (X):
    # LinkedIn
    # Images: Up to 9 images per post.
    # Videos: Only 1 video per post.
    # Documents (PDF, PPT, DOCX, etc.): Only 1 document per post.
    # Twitter (X)
    # Images: Up to 4 images per post.
    # Videos: Only 1 video per post.
    # GIFs: Only 1 GIF per post.
    # Mixed media: You can combine images, GIFs, and videos in a single post (up to 4 items total).
    field(:attachments, {:array, :map})

    belongs_to(:media, Gaia.Interactions.Media)

    timestamps()
  end

  @doc false
  def changeset(social_post, attrs) do
    social_post
    |> cast(attrs, [
      :media_id,
      :content,
      :content_formatted,
      :social_post_id,
      :status,
      :platform,
      :scheduled_at,
      :published_at,
      :error_message,
      :analytics_data,
      :is_invalidated,
      :attachments,
      :published_url
    ])
    |> validate_required([:media_id, :platform])
  end
end
