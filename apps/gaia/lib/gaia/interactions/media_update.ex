defmodule Gaia.Interactions.MediaUpdate do
  @moduledoc """
  The MediaUpdate schema.
  """
  use Ecto.Schema

  import Ecto.Changeset

  alias Gaia.Interactions.MediaUpdate

  @media_update_fields [
    :company_profile_id,
    :invalidated,
    :is_draft,
    :last_updated_by_id,
    :media_id,
    :posted_at,
    :posted_by_id,
    :slug,
    :title,
    :included_types,
    :is_pinned,
    :content_draft,
    :content_published,
    :scheduled_at,
    :preview_secret,
    :thumbnail_url
  ]

  @interactions_media_update_types [:image, :none, :pdf, :url, :video]

  @included_interactions_media_update_types [:image, :none, :pdf, :url, :video, :multi]

  schema "interactions_media_updates" do
    field(:invalidated, :boolean, default: false)
    field(:is_draft, :boolean, default: true)
    field(:posted_at, :naive_datetime)
    field(:slug, :string)
    field(:title, :string)
    field(:included_types, {:array, Ecto.Enum}, values: @interactions_media_update_types)
    field(:is_pinned, :boolean, default: false)
    field(:content_draft, :map)
    field(:content_published, :map)
    field(:scheduled_at, :naive_datetime)
    field(:preview_secret, :string)
    field(:thumbnail_url, :string)
    belongs_to(:company_profile, Gaia.Companies.Profile)
    belongs_to(:last_updated_by, Gaia.Companies.User)
    belongs_to(:media, Gaia.Interactions.Media)
    belongs_to(:posted_by, Gaia.Companies.User)

    has_many(:attachments, Gaia.Interactions.MediaUpdateAttachment, foreign_key: :media_update_id)

    has_one(:content, Gaia.Interactions.MediaUpdateContent, foreign_key: :media_update_id)

    has_one(:distributed_social, through: [:media, :distributed_social])

    has_one(:email, through: [:media, :email])
    has_many(:tags, through: [:media, :tags], where: [invalidated: false])

    timestamps()
  end

  @doc false
  def changeset(media_update, attrs) do
    media_update
    |> cast(attrs, [
      :company_profile_id,
      :media_id,
      :title,
      :slug,
      :is_draft,
      :posted_at,
      :is_pinned,
      :posted_by_id,
      :last_updated_by_id,
      :content_draft,
      :content_published,
      :scheduled_at,
      :preview_secret,
      :included_types,
      :thumbnail_url
    ])
    |> validate_required([:company_profile_id, :media_id, :title, :slug, :is_draft])
    |> maybe_set_preview_secret()
    |> maybe_set_cover_image_from_content_draft()
  end

  # Generate a preview_secret if one doesn't exist
  defp maybe_set_preview_secret(changeset) do
    case get_field(changeset, :preview_secret) do
      nil -> put_change(changeset, :preview_secret, Ecto.UUID.generate())
      _ -> changeset
    end
  end

  # Main difference between this and changeset/2 is that the newsflow version doesn't require a slug.
  def newsflow_changeset(media_update, attrs) do
    media_update
    |> cast(attrs, @media_update_fields)
    |> validate_required([
      :company_profile_id,
      :last_updated_by_id,
      :media_id,
      :title
    ])
    |> unique_constraint(:media_id)
    |> maybe_set_cover_image_from_content_draft()
  end

  def get_types, do: @interactions_media_update_types

  def get_included_types, do: @included_interactions_media_update_types

  @doc """
  Generates a slug for newsflow media updates.
  The old way was to slugify the title and store it in the database.
  The new way is to combine "{hashed_media_update_id}-{title}" and slugify it.
  This way you can change the title over and over and the slug still works.
  """
  def generate_slug(%MediaUpdate{id: id, title: title}) when is_binary(title) and not is_nil(id) do
    generate_slug(id, title)
  end

  def generate_slug(_), do: nil

  def generate_slug(id, title) when is_integer(id) and is_binary(title) and not is_nil(id) do
    Helper.Hashid.encode_id(id) <> "-" <> Slug.slugify(title)
  end

  defp maybe_set_cover_image_from_content_draft(changeset) do
    # User is explicitly setting thumbnail_url to nil, so respect that
    if get_change(changeset, :thumbnail_url) == nil && Map.has_key?(changeset.changes, :thumbnail_url) do
      changeset
    else
      maybe_extract_and_set_thumbnail(changeset)
    end
  end

  defp maybe_extract_and_set_thumbnail(changeset) do
    with nil <- get_field(changeset, :thumbnail_url),
         content_draft when not is_nil(content_draft) <- get_field(changeset, :content_draft),
         cover_url when not is_nil(cover_url) <- extract_first_media_url(content_draft) do
      put_change(changeset, :thumbnail_url, cover_url)
    else
      _ -> changeset
    end
  end

  # Extract the first image or video URL from content_draft
  defp extract_first_media_url(%{"content" => content}) when is_list(content) do
    Enum.find_value(content, fn block ->
      case block do
        # Tiptap image format
        %{"type" => "image", "attrs" => %{"src" => url}} when is_binary(url) ->
          url

        # Alternative image format
        %{"type" => "image", "props" => %{"url" => url}} when is_binary(url) ->
          url

        # Video format
        %{"type" => "video", "props" => %{"url" => url}} when is_binary(url) ->
          url

        # YouTube video format
        %{"type" => "youtube", "attrs" => %{"src" => url}} when is_binary(url) ->
          extract_youtube_thumbnail(url)

        # Check for nested content (like in list items)
        %{"content" => nested_content} when is_list(nested_content) ->
          extract_first_media_url(%{"content" => nested_content})

        _ ->
          nil
      end
    end)
  end

  defp extract_first_media_url(_), do: nil

  # Extract thumbnail URL from a YouTube video URL
  defp extract_youtube_thumbnail(url) when is_binary(url) do
    case extract_youtube_video_id(url) do
      {:ok, video_id} -> "https://img.youtube.com/vi/#{video_id}/0.jpg"
      _ -> nil
    end
  end

  # Extract the video ID from a YouTube URL
  defp extract_youtube_video_id(url) when is_binary(url) do
    cond do
      String.contains?(url, "youtube.com/watch") -> extract_from_standard_youtube_url(url)
      String.contains?(url, "youtu.be/") -> extract_from_short_youtube_url(url)
      String.contains?(url, "youtube.com/embed/") -> extract_from_embed_youtube_url(url)
      true -> {:error, :not_youtube_url}
    end
  end

  # Standard YouTube URL format: https://www.youtube.com/watch?v=VIDEO_ID
  defp extract_from_standard_youtube_url(url) do
    case URI.parse(url) do
      %URI{query: query} when is_binary(query) ->
        case Map.get(URI.decode_query(query), "v") do
          nil -> {:error, :no_video_id}
          video_id -> {:ok, video_id}
        end

      _ ->
        {:error, :invalid_url}
    end
  end

  # Short YouTube URL format: https://youtu.be/VIDEO_ID
  defp extract_from_short_youtube_url(url) do
    case URI.parse(url) do
      %URI{path: path} when is_binary(path) ->
        video_id = String.trim_leading(path, "/")
        if video_id != "", do: {:ok, video_id}, else: {:error, :no_video_id}

      _ ->
        {:error, :invalid_url}
    end
  end

  # YouTube embed URL format: https://www.youtube.com/embed/VIDEO_ID
  defp extract_from_embed_youtube_url(url) do
    case URI.parse(url) do
      %URI{path: path} when is_binary(path) ->
        video_id = String.replace(path, "/embed/", "")
        if video_id != "", do: {:ok, video_id}, else: {:error, :no_video_id}

      _ ->
        {:error, :invalid_url}
    end
  end
end
