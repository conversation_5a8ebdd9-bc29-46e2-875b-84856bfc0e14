defmodule Gaia.Interactions.DuplicateMedia do
  @moduledoc """
  Functions for duplicating media items and their distributions
  """

  import Ecto.Query

  alias Gaia.Comms.Email
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Interactions.MediaTag
  alias Gaia.Interactions.MediaUpdate
  alias Gaia.Interactions.MediaUpdateAttachment
  alias Gaia.Interactions.MediaUpdateContent
  alias Gaia.Interactions.PreparedAnnouncement
  alias Gaia.Interactions.SocialPost
  alias Gaia.Repo

  @timestamp_format "{YYYY}{0M}{0D}{h24}{m}{s}"

  @doc """
  Duplicates a media item and all its associated distributions.
  Returns the duplicated media item.
  """
  def duplicate_media(%Media{} = media) do
    media =
      Repo.preload(media, [
        :media_announcement,
        :media_update,
        :tags,
        :linkedin_social_post,
        :twitter_social_post,
        :email,
        :prepared_announcement,
        media_update: [:attachments, :content]
      ])

    Ecto.Multi.new()
    |> duplicate_media_item(media)
    |> maybe_duplicate_media_announcement(media)
    |> maybe_duplicate_media_update(media)
    |> maybe_duplicate_media_tags(media)
    |> maybe_duplicate_social_posts(media)
    |> maybe_duplicate_email(media)
    |> maybe_duplicate_prepared_announcement(media)
    |> Repo.transaction()
    |> case do
      {:ok, %{media: duplicated_media}} -> {:ok, duplicated_media}
      {:error, _, changeset, _} -> {:error, changeset}
    end
  end

  defp duplicate_media_item(multi, %Media{} = media) do
    # Create a copy of the media item with a new title
    new_title = "Copy of #{media.title}"

    # Prepare attributes for the new media item
    media_attrs =
      media
      |> Map.from_struct()
      |> Map.drop([:__meta__, :id, :inserted_at, :updated_at])
      |> Map.put(:title, new_title)
      # Always set state to draft
      |> Map.put(:state, :draft)
      |> ensure_unique_slug()

    Ecto.Multi.insert(multi, :media, Media.changeset(%Media{}, media_attrs))
  end

  defp maybe_duplicate_media_announcement(multi, %Media{media_announcement: nil}), do: multi

  defp maybe_duplicate_media_announcement(multi, %Media{media_announcement: media_announcement}) do
    Ecto.Multi.run(multi, :media_announcement, fn _repo, %{media: duplicated_media} ->
      # Prepare attributes for the new media announcement
      announcement_attrs =
        media_announcement
        |> Map.from_struct()
        |> Map.drop([:__meta__, :id, :inserted_at, :updated_at])
        |> Map.put(:media_id, duplicated_media.id)

      %MediaAnnouncement{}
      |> MediaAnnouncement.changeset(announcement_attrs)
      |> Repo.insert()
    end)
  end

  defp maybe_duplicate_media_update(multi, %Media{media_update: nil}), do: multi

  defp maybe_duplicate_media_update(multi, %Media{media_update: media_update}) do
    Ecto.Multi.run(multi, :media_update, fn _repo, %{media: duplicated_media} ->
      # Prepare attributes for the new media update
      update_attrs =
        media_update
        |> Map.from_struct()
        |> Map.drop([:__meta__, :id, :inserted_at, :updated_at, :content, :attachments, :slug])
        |> Map.put(:media_id, duplicated_media.id)
        # Always set to draft
        |> Map.put(:is_draft, true)

      # Find the latest media update ID and guess the next one
      latest_id = get_latest_media_update_id()
      next_id = latest_id + 1

      # Generate a slug using the guessed ID
      guessed_slug = Helper.Hashid.encode_id(next_id) <> "-" <> Slug.slugify(update_attrs.title)

      # Add the guessed slug to the attributes
      update_attrs = Map.put(update_attrs, :slug, guessed_slug)

      # Insert the media update with the guessed slug
      {:ok, new_media_update} =
        %MediaUpdate{}
        |> MediaUpdate.changeset(update_attrs)
        |> Repo.insert()

      # Update the slug with the correct ID to ensure it's accurate
      {:ok, new_media_update} =
        new_media_update
        |> MediaUpdate.changeset(%{slug: MediaUpdate.generate_slug(new_media_update)})
        |> Repo.update()

      # Duplicate content if it exists
      if media_update.content do
        content_attrs =
          media_update.content
          |> Map.from_struct()
          |> Map.drop([:__meta__, :id, :inserted_at, :updated_at])
          |> Map.put(:media_update_id, new_media_update.id)

        %MediaUpdateContent{}
        |> MediaUpdateContent.changeset(content_attrs)
        |> Repo.insert()
      end

      # Duplicate attachments if they exist
      if media_update.attachments && length(media_update.attachments) > 0 do
        Enum.each(media_update.attachments, fn attachment ->
          attachment_attrs =
            attachment
            |> Map.from_struct()
            |> Map.drop([:__meta__, :id, :inserted_at, :updated_at])
            |> Map.put(:media_update_id, new_media_update.id)

          %MediaUpdateAttachment{}
          |> MediaUpdateAttachment.changeset(attachment_attrs)
          |> Repo.insert()
        end)
      end

      {:ok, new_media_update}
    end)
  end

  defp maybe_duplicate_media_tags(multi, %Media{tags: []}), do: multi

  defp maybe_duplicate_media_tags(multi, %Media{tags: tags}) do
    Ecto.Multi.run(multi, :tags, fn _repo, %{media: duplicated_media} ->
      tags_result =
        Enum.map(tags, fn tag ->
          tag_attrs =
            tag
            |> Map.from_struct()
            |> Map.drop([:__meta__, :id, :inserted_at, :updated_at])
            |> Map.put(:media_id, duplicated_media.id)

          %MediaTag{}
          |> MediaTag.changeset(tag_attrs)
          |> Repo.insert()
        end)

      {:ok, tags_result}
    end)
  end

  defp maybe_duplicate_social_posts(multi, %Media{linkedin_social_post: nil, twitter_social_post: nil}), do: multi

  defp maybe_duplicate_social_posts(multi, %Media{} = media) do
    multi
    |> maybe_duplicate_linkedin_social_post(media)
    |> maybe_duplicate_twitter_social_post(media)
  end

  defp maybe_duplicate_linkedin_social_post(multi, %Media{linkedin_social_post: nil}), do: multi

  defp maybe_duplicate_linkedin_social_post(multi, %Media{linkedin_social_post: social_post}) do
    Ecto.Multi.run(multi, :linkedin_social_post, fn _repo, %{media: duplicated_media} ->
      duplicate_social_post(social_post, duplicated_media.id)
    end)
  end

  defp maybe_duplicate_twitter_social_post(multi, %Media{twitter_social_post: nil}), do: multi

  defp maybe_duplicate_twitter_social_post(multi, %Media{twitter_social_post: social_post}) do
    Ecto.Multi.run(multi, :twitter_social_post, fn _repo, %{media: duplicated_media} ->
      duplicate_social_post(social_post, duplicated_media.id)
    end)
  end

  defp duplicate_social_post(social_post, new_media_id) do
    # Prepare attributes for the new social post
    social_post_attrs =
      social_post
      |> Map.from_struct()
      |> Map.drop([
        :__meta__,
        :id,
        :inserted_at,
        :updated_at,
        :social_post_id,
        :published_at,
        :published_url,
        :analytics_data
      ])
      |> Map.put(:media_id, new_media_id)
      |> Map.put(:status, :draft)

    # Ensure content and content_formatted are properly duplicated
    social_post_attrs =
      if social_post.content do
        Map.put(social_post_attrs, :content, social_post.content)
      else
        social_post_attrs
      end

    social_post_attrs =
      if social_post.content_formatted do
        Map.put(social_post_attrs, :content_formatted, social_post.content_formatted)
      else
        social_post_attrs
      end

    %SocialPost{}
    |> SocialPost.changeset(social_post_attrs)
    |> Repo.insert()
  end

  # Ensures a unique slug by appending a timestamp
  defp ensure_unique_slug(attrs) do
    if Map.has_key?(attrs, :slug) do
      timestamp = Calendar.strftime(DateTime.utc_now(), @timestamp_format)
      Map.put(attrs, :slug, "#{attrs.slug}-copy-#{timestamp}")
    else
      attrs
    end
  end

  # Get the latest media update ID from the database
  defp get_latest_media_update_id do
    case Repo.one(from m in MediaUpdate, select: max(m.id)) do
      # If no media updates exist yet
      nil -> 0
      id -> id
    end
  end

  # Email duplication
  defp maybe_duplicate_email(multi, %Media{email: nil}), do: multi

  defp maybe_duplicate_email(multi, %Media{email: email}) do
    Ecto.Multi.run(multi, :email, fn _repo, %{media: duplicated_media} ->
      # Prepare attributes for the new email
      email_attrs =
        email
        |> Map.from_struct()
        |> Map.drop([
          :__meta__,
          :id,
          :inserted_at,
          :updated_at,
          :sent_at
        ])
        |> Map.put(:media_id, duplicated_media.id)
        |> Map.put(:is_draft, true)
        |> Map.put(:campaign_name, "Copy of #{email.campaign_name}")

      %Email{}
      |> Email.changeset(email_attrs)
      |> Repo.insert()
    end)
  end

  # Prepared Announcement duplication
  defp maybe_duplicate_prepared_announcement(multi, %Media{prepared_announcement: nil}), do: multi

  defp maybe_duplicate_prepared_announcement(multi, %Media{prepared_announcement: prepared_announcement}) do
    Ecto.Multi.run(multi, :prepared_announcement, fn _repo, %{media: duplicated_media} ->
      # Prepare attributes for the new prepared announcement
      prepared_announcement_attrs =
        prepared_announcement
        |> Map.from_struct()
        |> Map.drop([
          :__meta__,
          :id,
          :inserted_at,
          :updated_at
        ])
        |> Map.put(:media_id, duplicated_media.id)
        |> Map.put(:is_draft, true)

      %PreparedAnnouncement{}
      |> PreparedAnnouncement.changeset(prepared_announcement_attrs)
      |> Repo.insert()
    end)
  end
end
