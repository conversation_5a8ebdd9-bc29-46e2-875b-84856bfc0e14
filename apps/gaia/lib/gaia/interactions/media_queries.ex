defmodule Gaia.Interactions.MediaQueries do
  @moduledoc """
  Media-related query functions for the Interactions context.

  This module contains all the query building and filtering logic for Media entities,
  including complex filtering, ordering, and specialized queries.
  """

  import Ecto.Query, only: [dynamic: 2]
  import Ecto.Query, warn: false

  alias <PERSON>aia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Interactions.MediaComment
  alias Gaia.Interactions.MediaLike
  alias Gaia.Interactions.MediaTag
  alias Gaia.Interactions.MediaUpdate
  alias Gaia.Investors.User
  alias Gaia.Repo

  @doc """
  Builds a query for medias with the given options.

  ## Options

  - `:filters` - List of filter maps with `:key` and `:value`
  - `:orders` - List of order maps with `:key` and `:value`

  ## Examples

      iex> medias_query(%{filters: [%{key: "company_profile_id", value: 1}]})
      #Ecto.Query<...>
  """
  def medias_query(options) do
    base_query = Media

    Enum.reduce(options, base_query, fn
      {:filters, filters}, query -> medias_filter_with(query, filters)
      {:orders, orders}, query -> medias_order_with(query, orders)
      _, query -> query
    end)
  end

  @doc """
  Applies filters to a media query.
  """
  def medias_filter_with(query, filters) do
    Enum.reduce(filters, query, &apply_media_filter/2)
  end

  # Apply individual filter based on key-value pair
  defp apply_media_filter(%{key: "company_profile_id", value: value}, query) do
    where(query, [m], m.company_profile_id == ^value)
  end

  defp apply_media_filter(%{key: "search", value: value}, query) do
    where(query, [m], ilike(m.title, ^"%#{value}%"))
  end

  defp apply_media_filter(%{key: "distribution_announcement_enabled", value: "true"}, query) do
    where(query, [m], m.distribution_announcement_enabled == true)
  end

  defp apply_media_filter(%{key: "distribution_update_enabled", value: "true"}, query) do
    where(query, [m], m.distribution_update_enabled == true)
  end

  defp apply_media_filter(%{key: "distribution_email_enabled", value: "true"}, query) do
    where(query, [m], m.distribution_email_enabled == true)
  end

  defp apply_media_filter(%{key: "distribution_linkedin_enabled", value: "true"}, query) do
    where(query, [m], m.distribution_linkedin_enabled == true)
  end

  defp apply_media_filter(%{key: "distribution_twitter_enabled", value: "true"}, query) do
    where(query, [m], m.distribution_twitter_enabled == true)
  end

  defp apply_media_filter(%{key: "hide_undistributed", value: "true"}, query) do
    apply_hide_undistributed_filter(query)
  end

  defp apply_media_filter(%{key: "status", value: status}, query) do
    apply_status_filter(query, status)
  end

  defp apply_media_filter(%{key: "target_date_start", value: value}, query) do
    apply_target_date_start_filter(query, value)
  end

  defp apply_media_filter(%{key: "target_date_end", value: value}, query) do
    apply_target_date_end_filter(query, value)
  end

  defp apply_media_filter(_, query), do: query

  # Helper functions for complex filters
  defp apply_hide_undistributed_filter(query) do
    # Show only media that has at least one distribution enabled OR has a prepared announcement
    query
    |> join(:left, [m], pa in assoc(m, :prepared_announcement), as: :prepared_announcement)
    |> where(
      [m, prepared_announcement: pa],
      not is_nil(pa.id) or
        m.distribution_announcement_enabled == true or
        m.distribution_update_enabled == true or
        m.distribution_email_enabled == true or
        m.distribution_linkedin_enabled == true or
        m.distribution_twitter_enabled == true
    )
  end

  defp apply_status_filter(query, "published") do
    apply_published_status_filter(query)
  end

  defp apply_status_filter(query, "scheduled") do
    apply_scheduled_status_filter(query)
  end

  defp apply_status_filter(query, "draft") do
    apply_draft_status_filter(query)
  end

  defp apply_status_filter(query, _), do: query

  defp apply_published_status_filter(query) do
    # Filter for published media based on primary_distribution
    query
    |> add_all_distribution_joins()
    |> where(^build_published_status_conditions())
  end

  defp apply_scheduled_status_filter(query) do
    # Filter for scheduled media based on primary_distribution
    query
    |> add_social_and_email_joins()
    |> where(^build_scheduled_status_conditions())
  end

  defp apply_draft_status_filter(query) do
    # Filter for draft media based on primary_distribution
    query
    |> add_all_distribution_joins()
    |> where(^build_draft_status_conditions())
  end

  # Build dynamic conditions for status filters
  defp build_published_status_conditions do
    dynamic(
      [m, media_announcement: ma, media_update: mu, email: e, linkedin_social_post: lsp, twitter_social_post: tsp],
      ^published_announcement_condition() or
        ^published_update_condition() or
        ^published_email_condition() or
        ^published_linkedin_condition() or
        ^published_twitter_condition()
    )
  end

  defp build_scheduled_status_conditions do
    dynamic(
      [m, email: e, linkedin_social_post: lsp, twitter_social_post: tsp],
      ^scheduled_email_condition() or
        ^scheduled_linkedin_condition() or
        ^scheduled_twitter_condition()
    )
  end

  defp build_draft_status_conditions do
    dynamic(
      [m, media_announcement: ma, media_update: mu, email: e, linkedin_social_post: lsp, twitter_social_post: tsp],
      ^draft_announcement_condition() or
        ^draft_update_condition() or
        ^draft_email_condition() or
        ^draft_linkedin_condition() or
        ^draft_twitter_condition() or
        ^draft_no_distribution_condition()
    )
  end

  # Individual condition builders for published status
  defp published_announcement_condition do
    dynamic([m, media_announcement: ma], m.primary_distribution == "announcement" and not is_nil(ma.id))
  end

  defp published_update_condition do
    dynamic([m, media_update: mu], m.primary_distribution == "update" and not is_nil(mu.id) and mu.is_draft == false)
  end

  defp published_email_condition do
    dynamic([m, email: e], m.primary_distribution == "email" and not is_nil(e.id) and not is_nil(e.sent_at))
  end

  defp published_linkedin_condition do
    dynamic(
      [m, linkedin_social_post: lsp],
      m.primary_distribution == "linkedin" and not is_nil(lsp.id) and not is_nil(lsp.published_at)
    )
  end

  defp published_twitter_condition do
    dynamic(
      [m, twitter_social_post: tsp],
      m.primary_distribution == "twitter" and not is_nil(tsp.id) and not is_nil(tsp.published_at)
    )
  end

  # Individual condition builders for scheduled status
  defp scheduled_email_condition do
    dynamic(
      [m, email: e],
      m.primary_distribution == "email" and not is_nil(e.id) and not is_nil(e.scheduled_at) and is_nil(e.sent_at)
    )
  end

  defp scheduled_linkedin_condition do
    dynamic(
      [m, linkedin_social_post: lsp],
      m.primary_distribution == "linkedin" and not is_nil(lsp.id) and not is_nil(lsp.scheduled_at) and
        is_nil(lsp.published_at)
    )
  end

  defp scheduled_twitter_condition do
    dynamic(
      [m, twitter_social_post: tsp],
      m.primary_distribution == "twitter" and not is_nil(tsp.id) and not is_nil(tsp.scheduled_at) and
        is_nil(tsp.published_at)
    )
  end

  # Individual condition builders for draft status
  defp draft_announcement_condition do
    dynamic([m, media_announcement: ma], m.primary_distribution == "announcement" and is_nil(ma.id))
  end

  defp draft_update_condition do
    dynamic([m, media_update: mu], m.primary_distribution == "update" and (is_nil(mu.id) or mu.is_draft == true))
  end

  defp draft_email_condition do
    dynamic(
      [m, email: e],
      m.primary_distribution == "email" and (is_nil(e.id) or (is_nil(e.sent_at) and is_nil(e.scheduled_at)))
    )
  end

  defp draft_linkedin_condition do
    dynamic(
      [m, linkedin_social_post: lsp],
      m.primary_distribution == "linkedin" and (is_nil(lsp.id) or (is_nil(lsp.published_at) and is_nil(lsp.scheduled_at)))
    )
  end

  defp draft_twitter_condition do
    dynamic(
      [m, twitter_social_post: tsp],
      m.primary_distribution == "twitter" and (is_nil(tsp.id) or (is_nil(tsp.published_at) and is_nil(tsp.scheduled_at)))
    )
  end

  defp draft_no_distribution_condition do
    dynamic([m], is_nil(m.primary_distribution))
  end

  # Helper functions for adding joins
  defp add_all_distribution_joins(query) do
    query
    |> join(:left, [m], ma in assoc(m, :media_announcement), as: :media_announcement)
    |> join(:left, [m], mu in assoc(m, :media_update), as: :media_update)
    |> join(:left, [m], e in assoc(m, :email), as: :email)
    |> join(:left, [m], lsp in assoc(m, :linkedin_social_post), as: :linkedin_social_post)
    |> join(:left, [m], tsp in assoc(m, :twitter_social_post), as: :twitter_social_post)
  end

  defp add_social_and_email_joins(query) do
    query
    |> join(:left, [m], e in assoc(m, :email), as: :email)
    |> join(:left, [m], lsp in assoc(m, :linkedin_social_post), as: :linkedin_social_post)
    |> join(:left, [m], tsp in assoc(m, :twitter_social_post), as: :twitter_social_post)
  end

  defp apply_target_date_start_filter(query, value) do
    case Date.from_iso8601(value) do
      {:ok, date} ->
        start_datetime = DateTime.new!(date, ~T[00:00:00], "Etc/UTC")
        where(query, [m], m.target_date >= ^start_datetime)

      _ ->
        query
    end
  end

  defp apply_target_date_end_filter(query, value) do
    case Date.from_iso8601(value) do
      {:ok, date} ->
        end_datetime = DateTime.new!(date, ~T[23:59:59], "Etc/UTC")
        where(query, [m], m.target_date <= ^end_datetime)

      _ ->
        query
    end
  end

  @doc """
  Applies ordering to a media query.
  """
  def medias_order_with(query, orders) do
    Enum.reduce(orders, query, fn
      %{key: "inserted_at", value: "desc"}, query ->
        order_by(query, [m], desc: m.inserted_at)

      %{key: "inserted_at", value: "asc"}, query ->
        order_by(query, [m], asc: m.inserted_at)

      %{key: "target_date", value: "desc"}, query ->
        order_by(query, [m], desc_nulls_last: m.target_date, desc: m.id)

      %{key: "target_date", value: "asc"}, query ->
        order_by(query, [m], asc_nulls_last: m.target_date, asc: m.id)

      %{key: "total_impressions", value: "desc"}, query ->
        order_by(query, [m], desc_nulls_last: m.cached_stats_total_impressions, desc: m.id)

      %{key: "total_impressions", value: "asc"}, query ->
        order_by(query, [m], asc_nulls_last: m.cached_stats_total_impressions, asc: m.id)

      _, query ->
        query
    end)
  end

  @doc """
  Builds a query for media announcements with the given options.
  """
  def media_announcements_query(args) do
    Enum.reduce(args, MediaAnnouncement, fn
      {:filters, filters}, query ->
        media_announcements_filter_with(query, filters)

      {:orders, orders}, query ->
        media_announcements_order_with(query, orders)

      _, query ->
        query
    end)
  end

  @doc """
  Applies filters to a media announcements query.
  """
  def media_announcements_filter_with(query, filter) do
    Enum.reduce(filter, query, fn
      %{key: "company_profile_id", value: value}, query ->
        from(q in query,
          join: m in assoc(q, :media),
          where: m.company_profile_id == ^value
        )

      %{key: "has_active_question", value: "true"}, query ->
        answered_parent_comment_ids_query =
          MediaComment
          |> where([mc], not is_nil(mc.parent_id))
          |> group_by([mc], mc.parent_id)
          |> select([mc], mc.parent_id)

        query
        |> join(:inner, [q], media in assoc(q, :media))
        |> join(:inner, [_q, media], comments in assoc(media, :comments))
        |> where(
          [..., comments],
          is_nil(comments.parent_id) and is_nil(comments.company_author_id) and
            comments.id not in subquery(answered_parent_comment_ids_query) and
            not comments.done
        )
        |> distinct([q], q.id)

      %{key: "listing_key", value: value}, query ->
        from(q in query, where: q.listing_key == ^String.upcase(value))

      %{key: "market_key", value: value}, query ->
        from(q in query, where: q.market_key == ^String.downcase(value))

      %{key: "rectypes", value: rectypes}, query ->
        rectypes_list = String.split(rectypes, ",")
        from(q in query, where: q.rectype in ^rectypes_list)

      %{key: "search", value: search_phrase}, query ->
        media_announcements_filter_by_search(query, search_phrase)

      %{key: "tags", value: tags}, query ->
        tags_filter_query(query, tags)

      %{key: "questions", value: "true"}, query ->
        questions_filter_query(query)

      %{key: "likes", value: "true"}, query ->
        likes_filter_query(query)

      %{key: "news_publishers", value: values}, query ->
        news_publishers = values |> String.split(",") |> Enum.map(&String.trim/1)
        from(q in query, where: fragment("? = ANY(?)", q.news_publisher, ^news_publishers))

      %{key: "is_market_sensitive", value: "true"}, query ->
        from(q in query, where: q.market_sensitive)

      %{key: "is_market_sensitive", value: "false"}, query ->
        from(q in query, where: not q.market_sensitive)

      %{key: "subtype", value: value}, query ->
        from(q in query, where: ^value in q.subtypes)

      %{key: "subtypes", value: values}, query ->
        subtypes =
          values
          |> String.split(",")
          |> Enum.map(&String.trim/1)

        from(q in query,
          where: fragment("? && ?", q.subtypes, type(^subtypes, {:array, :string}))
        )

      %{key: "subtype_exclusions", value: values}, query ->
        chunk =
          Enum.reduce(String.split(values, ","), false, fn
            value, chunk ->
              dynamic([q], ^value not in q.subtypes or ^chunk)
          end)

        from(query, where: ^chunk)

      %{key: "is_distributed", value: "true"}, query ->
        query
        |> join(:inner, [q], media in assoc(q, :media))
        |> where([_q, media], not is_nil(media.email_distribution_method))

      %{key: "is_distributed", value: "false"}, query ->
        query
        |> join(:inner, [q], media in assoc(q, :media))
        |> where([_q, media], is_nil(media.email_distribution_method))

      %{key: "distribution_method", value: "manual"}, query ->
        query
        |> join(:inner, [q], media in assoc(q, :media))
        |> where([_q, media], media.email_distribution_method == :manual)

      %{key: "distribution_method", value: "automated"}, query ->
        query
        |> join(:inner, [q], media in assoc(q, :media))
        |> where([_q, media], media.email_distribution_method == :automated)

      %{key: "posted_at_greater_than", value: value}, query ->
        from(q in query, where: q.posted_at > ^value)

      %{key: "posted_at_less_than", value: value}, query ->
        from(q in query, where: q.posted_at < ^value)

      %{key: "rectype", value: value}, query ->
        from(q in query, where: q.rectype == ^value)

      %{key: "ticker", value: value}, query ->
        from(q in query, where: q.listing_key == ^value)

      %{key: "without_media", value: "true"}, query ->
        from(q in query,
          where:
            is_nil(q.summary) and is_nil(q.social_video_url) and
              is_nil(q.video_url)
        )

      %{key: "video", value: "true"}, query ->
        from(q in query,
          where: not is_nil(q.social_video_url) or not is_nil(q.video_url)
        )

      %{key: "summary", value: "true"}, query ->
        from(q in query,
          where: not is_nil(q.summary)
        )

      %{key: "price_sensitive", value: "true"}, query ->
        from(q in query,
          where: q.market_sensitive
        )

      %{key: "featured", value: "true"}, query ->
        from(q in query,
          where: q.featured_on_hub
        )

      _, query ->
        query
    end)
  end

  @doc """
  Applies ordering to a media announcements query.
  """
  def media_announcements_order_with(query, order) do
    Enum.reduce(order, query, fn
      %{key: "id", value: "asc"}, query ->
        from(q in query, order_by: [asc: :id])

      %{key: "id", value: "desc"}, query ->
        from(q in query, order_by: [desc: :id])

      %{key: "inserted_at", value: "desc"}, query ->
        from(q in query, order_by: [desc: :inserted_at])

      %{key: "inserted_at", value: "asc"}, query ->
        from(q in query, order_by: [asc: :inserted_at])

      %{key: "posted_at", value: "desc"}, query ->
        from(q in query, order_by: [desc: :posted_at])

      %{key: "posted_at", value: "asc"}, query ->
        from(q in query, order_by: [asc: :posted_at])

      %{key: "questions", value: "desc"}, query ->
        questions_sort_query(query)

      %{key: "likes", value: "desc"}, query ->
        likes_sort_query(query)

      %{key: "engagement", value: "desc"}, query ->
        engagement_sort_query(query)

      _, query ->
        query
    end)
  end

  # Helper functions for media announcement filtering and sorting
  defp media_announcements_filter_by_search(query, search_phrase) do
    search_phrase = String.trim(search_phrase)

    if search_phrase != "" do
      search_term = "%#{search_phrase}%"

      from(q in query,
        where:
          ilike(q.header, ^search_term) or
            ilike(q.summary, ^search_term) or
            ilike(q.summary_ai, ^search_term)
      )
    else
      query
    end
  end

  defp questions_filter_query(query) do
    query
    |> join(:inner, [q], media in assoc(q, :media))
    |> join(
      :left,
      [_q, media],
      comments in subquery(
        from(mc in MediaComment,
          where: not is_nil(mc.investor_user_id) and not mc.private,
          group_by: mc.media_id,
          select: %{media_id: mc.media_id, count: count(mc.id)}
        )
      ),
      on: media.id == comments.media_id
    )
    |> where([q, media, comments], comments.count > 0)
  end

  defp likes_filter_query(query) do
    query
    |> join(:inner, [q], media in assoc(q, :media), as: :media)
    |> join(
      :left,
      [q, ..., media: media],
      likes in subquery(
        from(ml in MediaLike,
          group_by: ml.media_id,
          select: %{
            media_id: ml.media_id,
            count: fragment("SUM(CASE WHEN ? THEN 1 ELSE 0 END)", ml.like)
          }
        )
      ),
      on: media.id == likes.media_id
    )
    |> where([q, ..., likes], likes.count > 0)
  end

  defp questions_sort_query(query) do
    query
    |> join(:inner, [q], media in assoc(q, :media))
    |> join(
      :left,
      [_q, media],
      comments in subquery(
        from(mc in MediaComment,
          where:
            not mc.invalidated and is_nil(mc.parent_id) and
              is_nil(mc.company_author_id),
          group_by: mc.media_id,
          select: %{media_id: mc.media_id, count: count(mc.id)}
        )
      ),
      on: media.id == comments.media_id
    )
    |> order_by([q, media, comments],
      desc: coalesce(comments.count, 0),
      desc: q.posted_at
    )
  end

  defp likes_sort_query(query) do
    query
    |> join(:inner, [q], media in assoc(q, :media), as: :media)
    |> join(
      :left,
      [q, ..., media: media],
      likes in subquery(
        from(ml in MediaLike,
          where: ml.like,
          group_by: ml.media_id,
          select: %{
            media_id: ml.media_id,
            count: fragment("SUM(CASE WHEN ? THEN 1 ELSE 0 END)", ml.like)
          }
        )
      ),
      on: media.id == likes.media_id
    )
    |> order_by([q, ..., likes],
      desc: coalesce(likes.count, 0),
      desc: q.posted_at
    )
  end

  defp engagement_sort_query(query) do
    query
    |> join(:inner, [q], media in assoc(q, :media), as: :media)
    |> join(
      :left,
      [_q, media: media],
      comments in subquery(
        from(mc in MediaComment,
          where:
            not mc.invalidated and is_nil(mc.parent_id) and
              is_nil(mc.company_author_id),
          group_by: mc.media_id,
          select: %{media_id: mc.media_id, count: count(mc.id)}
        )
      ),
      on: media.id == comments.media_id
    )
    |> join(
      :left,
      [q, ..., media: media],
      likes in subquery(
        from(ml in MediaLike,
          where: ml.like,
          group_by: ml.media_id,
          select: %{
            media_id: ml.media_id,
            count: fragment("SUM(CASE WHEN ? THEN 1 ELSE 0 END)", ml.like)
          }
        )
      ),
      on: media.id == likes.media_id
    )
    |> order_by([q, ..., comments, likes],
      desc: coalesce(comments.count, 0) + coalesce(likes.count, 0),
      desc: q.posted_at
    )
  end

  def tags_filter_query(query, ""), do: query

  def tags_filter_query(query, tags) do
    search_tags = String.split(tags, ",")

    matching_media_ids_subquery =
      from(t in MediaTag,
        where: t.invalidated == false and t.name in ^search_tags,
        select: t.media_id
      )

    where(query, [q], q.media_id in subquery(matching_media_ids_subquery))
  end

  @doc """
  Gets the latest two announcements for a company.
  """
  def get_company_latest_two_announcements(company_profile_id) do
    %{
      filters: [
        %{key: "company_profile_id", value: company_profile_id}
      ],
      orders: [%{key: "posted_at", value: "desc"}, %{key: "id", value: "desc"}]
    }
    |> media_announcements_query()
    |> limit(2)
    |> Repo.all()
  end

  @doc """
  Gets the latest two market sensitive announcements for a company.
  """
  def get_company_latest_two_market_sensitive_announcements(company_profile_id) do
    %{
      filters: [
        %{key: "company_profile_id", value: company_profile_id},
        %{key: "is_market_sensitive", value: "true"}
      ],
      orders: [%{key: "posted_at", value: "desc"}, %{key: "id", value: "desc"}]
    }
    |> media_announcements_query()
    |> limit(2)
    |> Repo.all()
  end

  @doc """
  Gets the latest announcement for a company by subtype.
  """
  def get_company_latest_announcement_by_subtype(company_profile_id, subtype) do
    %{
      filters: [
        %{key: "company_profile_id", value: company_profile_id},
        %{key: "subtype", value: subtype}
      ],
      orders: [%{key: "posted_at", value: "desc"}, %{key: "id", value: "desc"}]
    }
    |> media_announcements_query()
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Gets the latest announcement for a company by rectypes.
  """
  def get_company_latest_announcement_by_rectypes(company_profile_id, rectypes) do
    %{
      filters: [
        %{key: "company_profile_id", value: company_profile_id},
        %{key: "rectypes", value: rectypes}
      ],
      orders: [%{key: "posted_at", value: "desc"}, %{key: "id", value: "desc"}]
    }
    |> media_announcements_query()
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Gets the oldest media announcement for a ticker.
  """
  def get_oldest_media_announcement(ticker) do
    MediaAnnouncement
    |> where([ma], ma.listing_key == ^String.upcase(ticker))
    |> order_by([ma], ma.posted_at)
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Gets the oldest media announcement date for a ticker.
  """
  def get_oldest_media_announcement_date(ticker) do
    MediaAnnouncement
    |> where([ma], ma.listing_key == ^String.upcase(ticker))
    |> order_by([ma], ma.posted_at)
    |> limit(1)
    |> select([ma], ma.posted_at)
    |> Repo.one()
  end

  @doc """
  Gets all medias per company for analytics purposes.
  """
  def get_all_medias_per_company(company_profile_id) do
    # Step 1: Pull all media and make a cross join with all users
    all_users =
      User
      |> where([i], i.company_profile_id == ^company_profile_id)
      |> select([i], %{investor_user_id: i.id})

    all_anns =
      Media
      |> join(:inner, [m], ima in MediaAnnouncement, on: m.id == ima.media_id)
      |> select([m, ima], %{
        media_id: m.id,
        announcement_id: ima.id,
        update_id: nil,
        media_type: ^"announcement",
        posted_at: ima.posted_at
      })
      |> where([m], m.company_profile_id == ^company_profile_id)

    all_updates =
      Media
      |> join(:inner, [m], imu in MediaUpdate, on: m.id == imu.media_id)
      |> select([m, imu], %{
        media_id: m.id,
        announcement_id: nil,
        update_id: imu.id,
        media_type: ^"update",
        posted_at: imu.posted_at
      })
      |> where([m], m.company_profile_id == ^company_profile_id)

    all_anns
    |> union_all(^all_updates)
    |> subquery()
    |> join(:inner, [m], u in subquery(all_users), on: true)
    |> select([m, u], %{
      media_id: m.media_id,
      investor_user_id: u.investor_user_id,
      announcement_id: m.announcement_id,
      update_id: m.update_id,
      media_type: m.media_type,
      posted_at: m.posted_at
    })
  end
end
