defmodule Gaia.Jobs.RefreshOlderMediaStatsJob do
  @moduledoc """
  Job that refreshes cached stats for older media items (beyond 2 weeks).
  Called daily at midnight by RefreshOlderMediaStats worker.
  """
  use Oban.Worker, queue: :default, max_attempts: 3

  require Logger

  @impl Oban.Worker
  def perform(%Oban.Job{}) do
    Logger.info("Starting refresh of older media stats")

    {:ok, result} = Gaia.Interactions.MediaStatsCache.refresh_older_media_stats()
    Logger.info("Older media stats refresh completed successfully: #{inspect(result)}")
    :ok
  end

  @doc """
  Enqueue a job to refresh older media stats
  """
  def enqueue(opts \\ []) do
    %{}
    |> __MODULE__.new(opts)
    |> Oban.insert()
  end
end
