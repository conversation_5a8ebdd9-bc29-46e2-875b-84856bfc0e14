defmodule Gaia.Jobs.RefreshStaleMediaStatsJob do
  @moduledoc """
  Job that refreshes cached stats for very stale media items.
  Called weekly by RefreshStaleMediaStats worker for cleanup.
  """
  use Oban.Worker, queue: :default, max_attempts: 3

  require Logger

  @impl Oban.Worker
  def perform(%Oban.Job{args: args}) do
    stale_days = Map.get(args, "stale_days", 7)
    max_batches = Map.get(args, "max_batches", 20)

    Logger.info("Starting refresh of stale media stats (#{stale_days} days, #{max_batches} max batches)")

    {:ok, result} = Gaia.Interactions.MediaStatsCache.refresh_stale_media_stats(stale_days, max_batches)
    Logger.info("Stale media stats refresh completed successfully: #{inspect(result)}")
    :ok
  end

  @doc """
  Enqueue a job to refresh stale media stats
  """
  def enqueue(args \\ %{}, opts \\ []) do
    args
    |> __MODULE__.new(opts)
    |> Oban.insert()
  end
end
