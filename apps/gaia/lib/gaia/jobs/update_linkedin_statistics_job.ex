defmodule Gaia.Jobs.UpdateLinkedinStatisticsJob do
  @moduledoc """
  Job to update LinkedIn statistics for all published social posts.

  This job fetches the latest statistics from LinkedIn for all published posts
  and updates the analytics_data field in the social_post table.

  This ensures we have up-to-date data for calculating total impressions
  without having to make API calls when viewing the media table.
  """

  use Oban.Worker, queue: :default, max_attempts: 3

  import Ecto.Query

  alias Gaia.Companies
  alias Gaia.Companies.SocialConnection
  alias Gaia.Interactions
  alias Gaia.Interactions.SocialPost
  alias Gaia.Repo
  alias Gaia.Socials.LinkedIn

  require Logger

  def enqueue(args, opts \\ []) do
    args
    |> new(opts)
    |> Oban.insert()
  end

  @impl Oban.Worker
  def perform(%Oban.Job{}) do
    # Get all published LinkedIn social posts
    query =
      from sp in SocialPost,
        where: sp.platform == :linkedin and sp.status == :published and not is_nil(sp.social_post_id),
        join: m in assoc(sp, :media),
        join: cp in assoc(m, :company_profile),
        join: sc in SocialConnection,
        on: sc.company_profile_id == cp.id,
        where: not is_nil(sc.linkedin_access_token) and not is_nil(sc.linkedin_organisation_id),
        preload: [media: {m, company_profile: {cp, social_connection: sc}}]

    social_posts = Repo.all(query)

    # Process each social post
    results =
      Enum.map(social_posts, fn social_post ->
        process_social_post(social_post)
      end)

    # Count successes and failures
    successes = Enum.count(results, fn result -> elem(result, 0) == :ok end)
    failures = Enum.count(results, fn result -> elem(result, 0) == :error end)

    Logger.info("Updated LinkedIn statistics: #{successes} successes, #{failures} failures")

    :ok
  end

  defp process_social_post(
         %SocialPost{
           social_post_id: post_id,
           media: %{
             company_profile: %{
               social_connection:
                 %SocialConnection{linkedin_access_token: access_token, linkedin_organisation_id: organisation_id} =
                   social_connection
             }
           }
         } = social_post
       ) do
    # Check if LinkedIn is connected
    if Companies.get_is_linkedin_connected(social_connection) do
      case LinkedIn.post_statistics(access_token, organisation_id, post_id) do
        {:ok, post_stats} ->
          # Update the social post with the latest statistics
          case Interactions.update_social_post(social_post, %{analytics_data: post_stats}) do
            {:ok, _updated_post} ->
              {:ok, post_id}

            {:error, reason} ->
              Logger.error("Failed to update social post #{post_id}: #{inspect(reason)}")
              {:error, reason}
          end

        {:error, reason} ->
          Logger.error("Failed to fetch LinkedIn statistics for post #{post_id}: #{inspect(reason)}")
          {:error, reason}
      end
    else
      Logger.info("LinkedIn not connected for post #{post_id}")
      {:error, :linkedin_not_connected}
    end
  end

  defp process_social_post(social_post) do
    Logger.error("Invalid social post data: #{inspect(social_post)}")
    {:error, :invalid_social_post}
  end
end
