defmodule Gaia.Jobs.RefreshRecentMediaStatsJob do
  @moduledoc """
  Job that refreshes cached stats for recent media items (last 2 weeks).
  Called hourly by RefreshRecentMediaStats worker.
  """
  use Oban.Worker, queue: :default, max_attempts: 3

  require Logger

  @impl Oban.Worker
  def perform(%Oban.Job{}) do
    Logger.info("Starting refresh of recent media stats")

    {:ok, result} = Gaia.Interactions.MediaStatsCache.refresh_recent_media_stats()
    Logger.info("Recent media stats refresh completed successfully: #{inspect(result)}")
    :ok
  end

  @doc """
  Enqueue a job to refresh recent media stats
  """
  def enqueue(opts \\ []) do
    %{}
    |> __MODULE__.new(opts)
    |> Oban.insert()
  end
end
