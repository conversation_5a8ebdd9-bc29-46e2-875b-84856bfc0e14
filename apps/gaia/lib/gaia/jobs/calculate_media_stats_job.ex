defmodule Gaia.Jobs.CalculateMediaStatsJob do
  @moduledoc """
  Job that calculates and caches stats for a single media item.

  This job handles the calculation of impressions across all distribution channels
  for a single media item. It's designed to be run in parallel for many media items
  to efficiently handle API rate limits and provide better error isolation.
  """
  use Oban.Worker,
    queue: :media_stats,
    max_attempts: 3,
    tags: ["media", "stats", "cache"]

  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaStatsCache
  alias Gaia.Repo

  require Logger

  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"media_id" => media_id}} = job) do
    Logger.debug("Starting media stats calculation for media ID: #{media_id}")

    case Repo.get(Media, media_id) do
      nil ->
        Logger.warning("Media not found for ID: #{media_id}")
        {:discard, "Media not found"}

      %Media{invalidated: true} ->
        Logger.debug("Skipping invalidated media ID: #{media_id}")
        {:discard, "Media invalidated"}

      media ->
        calculate_stats_for_media(media, job)
    end
  end

  @impl Oban.Worker
  def perform(%Oban.Job{args: args}) do
    Logger.error("Invalid job args for CalculateMediaStatsJob: #{inspect(args)}")
    {:discard, "Invalid job arguments"}
  end

  defp calculate_stats_for_media(media, job) do
    # Check if media has any published distributions
    latest_published = Media.get_latest_published_date(media)

    if is_nil(latest_published) do
      Logger.debug("Media #{media.id} has no published distributions, skipping")
      :ok
    else
      case MediaStatsCache.calculate_and_cache_stats(media) do
        {:ok, updated_media} ->
          Logger.debug(
            "Successfully updated stats for media #{media.id}: #{updated_media.cached_stats_total_impressions} total impressions"
          )

          :ok

        {:error, changeset} ->
          error_details = extract_changeset_errors(changeset)
          Logger.error("Failed to update stats for media #{media.id}: #{inspect(error_details)}")

          # Decide whether to retry or discard based on error type
          if should_retry_error?(error_details) and job.attempt < job.max_attempts do
            {:error, "Database error: #{inspect(error_details)}"}
          else
            {:discard, "Permanent error: #{inspect(error_details)}"}
          end

        other_error ->
          Logger.error("Unexpected error calculating stats for media #{media.id}: #{inspect(other_error)}")
          {:error, "Unexpected error: #{inspect(other_error)}"}
      end
    end
  rescue
    exception ->
      Logger.error("Exception calculating stats for media #{media.id}: #{inspect(exception)}")
      Logger.error("Stacktrace: #{Exception.format_stacktrace(__STACKTRACE__)}")

      # Rate limit errors from LinkedIn API should be retried
      if rate_limit_error?(exception) and job.attempt < job.max_attempts do
        Logger.info("Rate limit detected for media #{media.id}, will retry")
        # Wait 5 minutes before retry
        {:snooze, 300}
      else
        {:error, "Exception: #{inspect(exception)}"}
      end
  end

  defp extract_changeset_errors(%Ecto.Changeset{errors: errors}) do
    Enum.map(errors, fn {field, {message, _}} -> "#{field}: #{message}" end)
  end

  defp extract_changeset_errors(_), do: ["Unknown changeset error"]

  defp should_retry_error?(error_details) do
    # Check if errors indicate temporary issues that should be retried
    error_string = Enum.join(error_details, ", ")

    String.contains?(error_string, "timeout") or
      String.contains?(error_string, "connection") or
      String.contains?(error_string, "network") or
      String.contains?(error_string, "temporary")
  end

  defp rate_limit_error?(exception) do
    # Check if exception indicates rate limiting from external APIs
    case exception do
      %HTTPoison.Error{reason: :timeout} -> true
      %HTTPoison.Error{reason: :econnrefused} -> true
      # Rate limit or service unavailable
      %{status_code: status} when status in [429, 503] -> true
      _ -> false
    end
  end

  @doc """
  Enqueue a job to calculate stats for a specific media item
  """
  def enqueue(args, opts \\ []) do
    default_opts = [queue: :media_stats]
    final_opts = Keyword.merge(default_opts, opts)

    args
    |> __MODULE__.new(final_opts)
    |> Oban.insert()
  end

  @doc """
  Enqueue jobs for multiple media items at once
  """
  def enqueue_batch(media_ids, opts \\ []) when is_list(media_ids) do
    default_opts = [queue: :media_stats]
    final_opts = Keyword.merge(default_opts, opts)

    jobs =
      Enum.map(media_ids, fn media_id -> __MODULE__.new(%{"media_id" => media_id}, final_opts) end)

    Oban.insert_all(jobs)
  end
end
