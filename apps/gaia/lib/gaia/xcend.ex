defmodule Gaia.Xcend do
  @moduledoc """
  Helper functions for Xcend registry integration
  """

  import Gaia.Registers.ImporterHelperV2,
    only: [is_address_part?: 1, parse_number: 1, parse_string: 1, validate_shareholding: 1, validate_share_movement: 1]

  alias Gaia.Companies.Profile
  alias Gaia.Companies.RegistryCredential
  alias Gaia.Markets.Ticker

  @doc """
  Check if we have been given access to the ticker's data

  Current logic is to check if there is any transactions within the past month
  """
  def ticker_access_granted?(%Ticker{listing_key: listing_key, market_key: :asx}) do
    to_date = Helper.ExDay.now_date()
    from_date = Timex.shift(to_date, months: -1)

    with {:ok, %{"accessToken" => access_token}} <- Xcend.authenticate(),
         {:ok, transactions} <- Xcend.transaction_listing(access_token, listing_key, from_date, to_date) do
      Enum.count(transactions) > 0
    else
      _ ->
        false
    end
  end

  @doc """
  Initial import stage
  - Get `yesterday` register report (it contains past shareholders) and insert all shareholdings
  - Get all time transactions, from `1980-01-01` until `yesterday` and insert share movements
    The `yesterday` here is very important because can't guarantee to get all transactions if fetching until today
  - Update share movements and shareholdings counter caches
  - Insert daily holdings

  Daily import stage
  - Get `yesterday` register report (it contains past shareholders) and upsert all shareholdings
  - Get all transactions from `last imported day + 1` until `yesterday` and insert share movements
    The `yesterday` here is very important because can't guarantee to get all transactions if fetching until today
  - Update share movements and shareholdings counter caches
  - Insert daily holdings
  """
  def import_xcend_register(
        %RegistryCredential{
          company_profile: %Profile{id: company_profile_id, ticker: %Ticker{listing_key: listing_key, market_key: :asx}},
          invalidated: false,
          service: :xcend
        },
        %Date{} = from_date,
        %Date{} = to_date
      ) do
    with {:check_dates, true} <- {:check_dates, Timex.compare(from_date, to_date) <= 0},
         {:ok, %{"accessToken" => access_token}} <- Xcend.authenticate(),
         {:ok, register_listing} <- Xcend.register_listing(access_token, listing_key, to_date),
         {:ok, transaction_listing} <- Xcend.transaction_listing(access_token, listing_key, from_date, to_date),
         {:ok, res} <-
           Ecto.Multi.new()
           |> Ecto.Multi.run(:insert_shareholdings, fn _, _ ->
             register_listing
             |> parse_shareholdings(company_profile_id)
             |> Gaia.Registers.insert_all_shareholdings_and_link_to_contact(company_profile_id)
           end)
           |> Ecto.Multi.run(:insert_placeholder_shareholdings, fn _, _ ->
             transaction_listing
             |> parse_placeholder_shareholdings(company_profile_id)
             |> Gaia.Registers.insert_placeholder_shareholdings()
           end)
           |> Ecto.Multi.run(:delete_share_movements, fn _, _ ->
             # Delete share_movements before inserting to avoid duplication
             {deleted, _} =
               Gaia.Registers.delete_share_movements_within_date_range(
                 company_profile_id,
                 from_date,
                 to_date
               )

             {:ok, deleted}
           end)
           |> Ecto.Multi.run(:insert_share_movements, fn _, _ ->
             transaction_listing
             |> parse_share_movements(company_profile_id)
             |> Gaia.Registers.insert_all_share_movements()
           end)
           |> Ecto.Multi.run(:update_share_movements_counter_cache, fn _, _ ->
             {:ok, Gaia.Registers.update_share_movements_counter_cache(company_profile_id)}
           end)
           |> Ecto.Multi.run(:update_shareholdings_counter_cache, fn _, _ ->
             {:ok, Gaia.Registers.update_shareholdings_counter_cache(company_profile_id)}
           end)
           |> Gaia.Repo.transaction(timeout: 900_000) do
      {:ok, res}
    else
      {:check_dates, false} ->
        {:discard, "Do not need to import, from_date is greater than to_date"}

      {:error, error} ->
        {:error, error}

      error ->
        {:error, error}
    end
  end

  defp parse_shareholdings(register_listing, company_profile_id) do
    utc_now = NaiveDateTime.utc_now(:second)

    Enum.map(register_listing, fn row ->
      %{
        company_profile_id: company_profile_id,
        holder_id: parse_string(row["accountNumber"]),
        registry_holder_id: parse_string(row["accountID"]),
        email: parse_string(row["emailAddress"]),
        phone_number: parse_string(row["telephone"]),
        metadata:
          Enum.reduce(
            [
              "bizEntityID",
              "controllingPID",
              "holderTypeName",
              "registrationDetailsLine1",
              "registrationDetailsLine2",
              "registrationDetailsLine3",
              "registrationDetailsLine4",
              "registrationDetailsLine5",
              "registrationDetailsLine6",
              "registrationDetailsLine7"
            ],
            %{},
            &Map.put(&2, &1, Map.get(row, &1))
          ),
        inserted_at: utc_now,
        updated_at: utc_now
      }
      |> Map.merge(parse_account_name_and_address(row))
      |> validate_shareholding()
    end)
  end

  # Map shareholdings and convert them to list to remove duplicates
  # Also excludes shareholdings that are already in our database
  defp parse_placeholder_shareholdings(transaction_listing, company_profile_id) do
    existing_registry_holder_ids = Gaia.Registers.get_shareholding_registry_holder_ids(company_profile_id)
    utc_now = NaiveDateTime.utc_now(:second)

    transaction_listing
    |> Enum.reduce(%{}, fn row, acc ->
      registry_holder_id = parse_string(row["accountID"])

      if registry_holder_id in existing_registry_holder_ids do
        # Skip
        acc
      else
        Map.put(
          acc,
          registry_holder_id,
          validate_shareholding(%{
            account_name:
              case parse_string(row["entityName"]) do
                nil -> nil
                text -> text |> String.replace("\n", " ") |> parse_string()
              end,
            company_profile_id: company_profile_id,
            holder_id: parse_string(row["accountNumber"]),
            registry_holder_id: registry_holder_id,
            inserted_at: utc_now,
            updated_at: utc_now
          })
        )
      end
    end)
    |> Enum.map(fn {_, placeholder_shareholding} -> placeholder_shareholding end)
  end

  defp parse_share_movements(transaction_listing, company_profile_id) do
    utc_now = NaiveDateTime.utc_now(:second)
    mapping = Gaia.Registers.get_registry_holder_id_to_shareholding_mapping(company_profile_id)

    Enum.map(transaction_listing, fn row ->
      # We need to calculate opening balance ourselves because they have been instances where:
      # - Negative opening balance being passed to us as `0`
      # - We can clearly see that the previoustransaction ended up with negative closing balance
      closing_balance = parse_number(row["closingBalance"])
      movement = parse_number(row["quantityOn"]) - parse_number(row["quantityOff"])
      opening_balance = closing_balance - movement

      validate_share_movement(%{
        broker_pid: parse_string(row["controllingPID"]),
        closing_balance: closing_balance,
        company_profile_id: company_profile_id,
        metadata:
          Enum.reduce(
            ["securityCode", "quantityOn", "quantityOff", "entityName", "bizEntityID", "accountID"],
            %{},
            &Map.put(&2, &1, Map.get(row, &1))
          ),
        movement: movement,
        movement_id: parse_movement_id(row["bizTransactionID"]),
        movement_type: parse_string(row["bizTransactionTypeName"]),
        opening_balance: opening_balance,
        settled_at: parse_date(row["bizTransactionDateTime"]),
        shareholding_id: mapping |> Map.get(parse_string(row["accountID"])) |> Map.get(:id),
        transaction_price: nil,
        inserted_at: utc_now,
        updated_at: utc_now
      })
    end)
  end

  # Not sure how to handle overseas address as we do not have example for this
  # The last details line (excluding registrationDetailsLine7) is the `{CITY} {STATE} {POSTCODE}`
  defp parse_account_name_and_address(%{} = register_listing_row) do
    details_lines =
      Enum.filter(
        [
          parse_string(register_listing_row["registrationDetailsLine2"]),
          parse_string(register_listing_row["registrationDetailsLine3"]),
          parse_string(register_listing_row["registrationDetailsLine4"]),
          parse_string(register_listing_row["registrationDetailsLine5"]),
          parse_string(register_listing_row["registrationDetailsLine6"])
        ],
        &(not is_nil(&1))
      )

    # The last non-null element is always city state postcode
    city_state_postcode = Enum.at(details_lines, -1)
    name_address_lines = Enum.slice(details_lines, 0, Enum.count(details_lines) - 1)
    name_first_part = parse_string(register_listing_row["registrationDetailsLine1"])

    {account_name, rest_of_details_lines} = parse_account_name(name_address_lines, name_first_part)
    {address_line_one, address_line_two} = parse_address_lines(rest_of_details_lines)

    city_state_postcode
    |> parse_city_state_postcode()
    |> Enum.into(%{
      account_name: account_name,
      address_line_one: address_line_one,
      address_line_two: address_line_two,
      address_country: "AUSTRALIA"
    })
  end

  defp parse_account_name([head | tail] = details_lines, acc) do
    if is_address_part?(head) do
      # Returns the account name constructed so far and the rest of the details lines to be processed as address
      {parse_string(acc), details_lines}
    else
      parse_account_name(tail, "#{acc} #{head}")
    end
  end

  defp parse_account_name(_, acc), do: {acc, []}

  defp parse_address_lines([head | tail]) do
    address_line_one = parse_string(head)
    address_line_two = tail |> Enum.join(" ") |> parse_string()

    {address_line_one, address_line_two}
  end

  defp parse_address_lines(_), do: {nil, nil}

  # The input format is `{CITY} {STATE} {POSTCODE}`
  # The last two tokens will be state and postcode
  # The city could contains `spaces`
  defp parse_city_state_postcode(input) do
    city_state_postcode_tokens = String.split(input, " ")

    address_city =
      city_state_postcode_tokens
      |> Enum.slice(0, Enum.count(city_state_postcode_tokens) - 2)
      |> Enum.join(" ")

    [address_state, address_postcode] = Enum.slice(city_state_postcode_tokens, -2, 2)

    %{
      address_city: address_city,
      address_postcode: address_postcode,
      address_state: address_state
    }
  end

  defp parse_date(input) when is_binary(input) do
    input
    |> String.slice(0, 10)
    |> Timex.parse!("{YYYY}-{0M}-{0D}")
    |> Timex.to_date()
  end

  defp parse_movement_id(input) when is_number(input), do: input
end
