defmodule Gaia.Workers.UpdateLinkedinStatistics do
  @moduledoc """
  Worker to schedule the UpdateLinkedinStatisticsJob.

  This worker is scheduled to run hourly via Oban.Plugins.Cron.
  It enqueues the UpdateLinkedinStatisticsJob which will update the
  analytics_data field for all published LinkedIn social posts.
  """

  use Oban.Worker,
    queue: :default,
    max_attempts: 1,
    priority: 3

  require Logger

  @impl Oban.Worker
  def perform(_) do
    Logger.info("Scheduling LinkedIn statistics update job")

    Gaia.Jobs.UpdateLinkedinStatisticsJob.enqueue(%{})

    :ok
  end
end
