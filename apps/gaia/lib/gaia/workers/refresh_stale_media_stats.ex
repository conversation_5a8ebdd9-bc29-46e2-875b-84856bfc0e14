defmodule Gaia.Workers.RefreshStaleMediaStats do
  @moduledoc """
  Worker that schedules the refresh of very stale media stats.
  Called weekly to catch up on media items with very old cache.
  """
  use Oban.Worker, queue: :default

  @impl Oban.Worker
  def perform(%Oban.Job{args: args}) do
    stale_days = Map.get(args, "stale_days", 7)
    max_batches = Map.get(args, "max_batches", 20)

    Gaia.Jobs.RefreshStaleMediaStatsJob.enqueue(%{
      "stale_days" => stale_days,
      "max_batches" => max_batches
    })

    :ok
  end
end
