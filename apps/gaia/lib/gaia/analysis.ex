defmodule Gaia.Analysis do
  @moduledoc """
  The Analysis context.
  """

  import Ecto.Query, warn: false

  alias G<PERSON>.Comms.Email
  alias Gaia.Comms.EmailRecipient
  alias Gaia.Companies.Profile
  alias Gaia.Companies.WelcomePage
  alias Gaia.Contacts.Contact
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Interactions.MediaComment
  alias Gaia.Interactions.MediaLike
  alias Gaia.Interactions.MediaSurveyAnswer
  alias Gaia.Interactions.MediaUpdate
  alias Gaia.Investors.User
  alias Gaia.Markets.Ticker
  alias Gaia.Registers.Shareholding
  alias Gaia.Repo
  alias Gaia.Tracking.EmailEvent
  alias Gaia.Tracking.InvestorHub

  def get_media_announcements_with_permission_check(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id,
        company_profile_user_id_or_profile_user: company_profile_user_id_or_profile_user,
        required_permissions: required_permissions
      }) do
    if Gaia.Companies.company_profile_user_permissions_or_check?(
         company_profile_user_id_or_profile_user,
         required_permissions
       ) do
      get_media_announcements(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id})
    else
      []
    end
  end

  def get_media_announcements(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}) do
    MediaAnnouncement
    |> join(:left, [ma], m in assoc(ma, :media))
    |> join(:inner, [ma, m], cp in assoc(m, :company_profile))
    |> where(
      [ma, m],
      m.company_profile_id == ^company_profile_id
    )
    |> where([ma, m], not is_nil(ma.posted_at))
    |> where(
      [ma, m, cp],
      fragment(
        "date_trunc('day', ?) >= ? and date_trunc('day', ?) <= ?",
        ma.posted_at,
        ^start_date,
        ma.posted_at,
        ^end_date
      )
    )
    |> order_by(
      [ma, m, cp],
      fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", ma.posted_at, cp.timezone)
    )
    |> select([ma, m, cp], %{
      date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", ma.posted_at, cp.timezone),
      announcement_id: ma.id,
      company_profile_id: m.company_profile_id,
      header: ma.header
    })
    |> Repo.all()
    |> Enum.group_by(& &1.date)
    |> Enum.into([], fn {k, v} -> %{date: k, announcements: v} end)
  end

  def get_media_updates_with_permission_check(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id,
        company_profile_user_id_or_profile_user: company_profile_user_id_or_profile_user,
        required_permission: required_permission
      }) do
    if Gaia.Companies.company_profile_user_permission_check?(company_profile_user_id_or_profile_user, required_permission) do
      get_media_updates(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id})
    else
      []
    end
  end

  def get_media_updates(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}) do
    MediaUpdate
    |> join(:left, [mu], m in assoc(mu, :media))
    |> join(:inner, [mu, m], cp in assoc(m, :company_profile))
    |> where(
      [mu, m],
      not is_nil(m.email_distribution_method) and
        mu.company_profile_id == ^company_profile_id
    )
    |> where([mu, m], not mu.is_draft and not is_nil(mu.posted_at))
    |> where(
      [mu, m, cp],
      fragment(
        "date_trunc('day', ?) >= ? and date_trunc('day', ?) <= ?",
        mu.posted_at,
        ^start_date,
        mu.posted_at,
        ^end_date
      )
    )
    |> order_by(
      [mu, m, cp],
      fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", mu.posted_at, cp.timezone)
    )
    |> select([mu, m, cp], %{
      date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", mu.posted_at, cp.timezone),
      update_id: mu.id,
      company_profile_id: mu.company_profile_id,
      title: mu.title
    })
    |> Repo.all()
    |> Enum.group_by(& &1.date)
    |> Enum.into([], fn {k, v} -> %{date: k, updates: v} end)
  end

  def unique_visitors_by_date(start_date, company_profile_id) do
    InvestorHub
    |> join(:inner, [ih], cp in assoc(ih, :company_profile))
    |> where(
      [ih],
      like(ih.event, "%_page_viewed") and ih.company_profile_id == ^company_profile_id and
        fragment("date_trunc('day', ?)", ih.inserted_at) >=
          fragment("date_trunc('day', ?)", type(^start_date, :naive_datetime))
    )
    |> group_by([ih, cp], [
      fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", ih.inserted_at, cp.timezone)
    ])
    |> order_by(
      [ih, cp],
      fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", ih.inserted_at, cp.timezone)
    )
    |> select([ih, cp], %{
      date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", ih.inserted_at, cp.timezone),
      total_unique_visitors: count(ih.tracking_token_id, :distinct)
    })
    |> Repo.all()
  end

  defp converted_shareholders_query(start_date, end_date, company_profile_id, interval \\ "day") do
    Contact
    |> where([c], c.company_profile_id == ^company_profile_id)
    |> join(:inner, [c], cp in assoc(c, :company_profile))
    |> where(
      [c],
      (c.lead_identified_at >=
         fragment("date_trunc(?, ?)", ^interval, type(^start_date, :naive_datetime)) and
         c.lead_identified_at <=
           fragment("date_trunc(?, ?)", ^interval, type(^end_date, :naive_datetime)) and
         is_nil(c.imported_at)) or
        (c.nominated_shareholder_identified_at >=
           fragment("date_trunc(?, ?)", ^interval, type(^start_date, :naive_datetime)) and
           c.nominated_shareholder_identified_at <=
             fragment("date_trunc(?, ?)", ^interval, type(^end_date, :naive_datetime)))
    )
    |> group_by(
      [c, cp],
      [
        fragment(
          "date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?)",
          c.lead_identified_at,
          cp.timezone
        ),
        fragment(
          "date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?)",
          c.nominated_shareholder_identified_at,
          cp.timezone
        )
      ]
    )
    |> order_by(
      [c, cp],
      [
        fragment(
          "date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?)",
          c.lead_identified_at,
          cp.timezone
        ),
        fragment(
          "date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?)",
          c.nominated_shareholder_identified_at,
          cp.timezone
        )
      ]
    )
    |> select([c, cp], %{
      total_leads: count(c.id),
      total_converted_shareholders: count(c.lead_converted_at),
      total_nominated_shareholders: count(c.nominated_shareholder_identified_at),
      date:
        "date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?)"
        |> fragment(c.lead_identified_at, cp.timezone)
        |> coalesce(
          fragment(
            "date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?)",
            c.nominated_shareholder_identified_at,
            cp.timezone
          )
        )
    })
  end

  def get_conversion_rate_sma(start_date, end_date, company_profile_id) do
    start_of_date = Timex.beginning_of_day(start_date)

    preceding_date = Timex.shift(start_date, days: -28)

    converted_shareholders = converted_shareholders_query(preceding_date, end_date, company_profile_id)

    %{timezone: timezone} = Gaia.Companies.get_profile!(company_profile_id)

    trailing_average_query =
      from(c in subquery(converted_shareholders),
        right_join:
          d in fragment(
            "select date(date_trunc('day', date AT TIME ZONE 'UTC', ?)) as date from generate_series(?, ?, '1 day') date",
            ^timezone,
            type(^preceding_date, :naive_datetime),
            type(^end_date, :naive_datetime)
          ),
        on:
          d.date ==
            fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", c.date, ^timezone),
        order_by: d.date,
        select: %{
          date: d.date,
          total_converted_shareholders: coalesce(c.total_converted_shareholders, 0),
          total_leads: coalesce(c.total_leads, 0)
        }
      )

    trailing_average_query
    |> subquery()
    |> select([c], %{
      date: c.date,
      conversion_rate_sma:
        fragment(
          "round(
            COALESCE((
              SUM(?) OVER (ORDER BY ? ROWS BETWEEN 27 PRECEDING AND CURRENT ROW)
              /
              NULLIF(SUM(?) OVER (ORDER BY ? ROWS BETWEEN 27 PRECEDING AND CURRENT ROW),0)
            ),0)
            ,2)::float",
          c.total_converted_shareholders,
          c.date,
          c.total_leads,
          c.date
        )
    })
    |> subquery()
    |> where(
      [c],
      c.date >= type(^start_of_date, :naive_datetime) and c.date <= type(^end_date, :naive_datetime)
    )
    |> Repo.all()
  end

  def get_engagement_analysis_overview_audience_breakdown(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      }) do
    start_date
    |> converted_shareholders_query(end_date, company_profile_id, "second")
    |> Repo.all()
  end

  def get_total_audiences_breakdown(
        [
          %{
            total_leads: _total_leads,
            total_converted_shareholders: _total_converted_shareholders,
            total_nominated_shareholders: _total_nominated_shareholders
          }
          | _
        ] = list
      ) do
    Enum.reduce(list, fn item, acc ->
      %{
        total_leads: item.total_leads + acc.total_leads,
        total_converted_shareholders: item.total_converted_shareholders + acc.total_converted_shareholders,
        total_nominated_shareholders: item.total_nominated_shareholders + acc.total_nominated_shareholders
      }
    end)
  end

  def get_total_audiences_breakdown(_) do
    %{
      total_leads: 0,
      total_converted_shareholders: 0,
      total_nominated_shareholders: 0
    }
  end

  def merge_media_announcements_and_updates(
        start_date,
        end_date,
        shareholder_and_leads_list,
        trailing_average,
        unique_visitors_by_date,
        announcements_list,
        updates_list,
        campaigns_list
      ) do
    all_dates_list = start_date |> Date.range(end_date) |> Enum.map(&%{date: &1})

    (all_dates_list ++
       shareholder_and_leads_list ++
       trailing_average ++
       unique_visitors_by_date ++ announcements_list ++ updates_list ++ campaigns_list)
    |> Enum.group_by(&Timex.to_date(&1.date))
    |> Enum.map(fn {key, value} ->
      Enum.reduce(
        value,
        %{
          date: key,
          announcements: [],
          updates: [],
          campaigns: [],
          total_leads: 0,
          total_converted_shareholders: 0,
          total_nominated_shareholders: 0,
          total_unique_visitors: 0,
          conversion_rate_sma: 0
        },
        fn item, acc -> Map.merge(acc, item) end
      )
    end)
    |> Enum.sort_by(&Timex.to_date(&1.date), {:asc, Date})
  end

  def merge_media_announcements_and_updates_is_demo(
        start_date,
        end_date,
        shareholder_and_leads_list,
        trailing_average,
        unique_visitors_by_date,
        announcements_list,
        updates_list,
        campaigns_list
      ) do
    all_dates_list = start_date |> Date.range(end_date) |> Enum.map(&%{date: &1})

    (all_dates_list ++
       shareholder_and_leads_list ++
       trailing_average ++
       unique_visitors_by_date ++ announcements_list ++ updates_list ++ campaigns_list)
    |> Enum.group_by(&Timex.to_date(&1.date))
    |> Enum.map(fn {key, value} ->
      Enum.reduce(
        value,
        %{
          date: key,
          announcements: [],
          updates: [],
          campaigns: [],
          total_leads: :rand.uniform(50),
          total_converted_shareholders: :rand.uniform(10),
          total_nominated_shareholders: :rand.uniform(3),
          total_unique_visitors: 10 + :rand.uniform(40),
          conversion_rate_sma: :rand.uniform() * 0.6
        },
        fn item, acc -> merge(acc, item) end
      )
    end)
    |> Enum.sort_by(&Timex.to_date(&1.date), {:asc, Date})
  end

  defp merge(acc, item) do
    # if both values are numbers, add them
    # if both values are lists (e.g lists of announcements/updates), concat them
    Map.merge(acc, item, fn _key, val1, val2 ->
      case {val1, val2} do
        {a, b} when is_number(a) and is_number(b) -> a + b
        {a, b} when is_list(a) and is_list(b) -> a ++ b
        {a, _b} -> a
      end
    end)
  end

  def get_total_signups(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}) do
    User
    |> where(
      [iu],
      iu.company_profile_id ==
        ^company_profile_id and iu.inserted_at >= ^start_date and
        iu.inserted_at <= ^end_date
    )
    |> Repo.aggregate(:count)
  end

  def get_total_unique_visitors(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}) do
    InvestorHub
    |> where(
      [ih],
      ih.company_profile_id == ^company_profile_id and ih.inserted_at >= ^start_date and
        ih.inserted_at <= ^end_date
    )
    |> where([ih], like(ih.event, "%_page_viewed"))
    |> distinct([ih], ih.tracking_token_id)
    |> Repo.aggregate(:count)
  end

  def get_total_views(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}) do
    InvestorHub
    |> where(
      [ih],
      ih.company_profile_id == ^company_profile_id and
        like(ih.event, "%_page_viewed") and
        ih.inserted_at >= ^start_date and
        ih.inserted_at <= ^end_date
    )
    |> Repo.aggregate(:count)
  end

  # TODO, exclusive or inclusive count (confirm with Nathan)
  def get_signup_breakdown(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}) do
    User
    |> join(:left, [iu], sh in Shareholding, on: sh.contact_id == iu.contact_id)
    |> join(:left, [iu, sh], past in User, on: past.id == iu.id and sh.share_count == 0)
    |> join(:left, [iu, sh, past], lead in User,
      on: lead.id == iu.id and is_nil(sh.id) and lead.is_self_nominated_shareholder == false
    )
    |> join(:left, [iu, sh, past, lead], nominated in User,
      on: nominated.id == iu.id and nominated.is_self_nominated_shareholder == true
    )
    |> join(:left, [iu, sh, past, lead, nominated], existing in User, on: existing.id == iu.id and sh.share_count > 0)
    |> where(
      [iu, sh, past, lead, nominated, existing],
      iu.company_profile_id ==
        ^company_profile_id and iu.inserted_at >= ^start_date and
        iu.inserted_at <= ^end_date
    )
    |> select([iu, sh, past, lead, nominated, existing], %{
      existing_shareholders: count(existing.id, :distinct),
      past_shareholders: count(past.id, :distinct),
      leads: count(lead.id, :distinct),
      nominated_shareholders: count(nominated.id, :distinct)
    })
    |> Repo.one()
  end

  def get_most_engaged_investors(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}) do
    User
    |> where(
      [iu],
      iu.company_profile_id ==
        ^company_profile_id
    )
    |> join(
      :left,
      [iu],
      q in subquery(
        MediaComment
        |> where([mc], mc.inserted_at >= ^start_date and mc.inserted_at <= ^end_date)
        |> group_by([mc], mc.investor_user_id)
        |> select([mc], %{investor_user_id: mc.investor_user_id, count: count(mc.id)})
      ),
      on: iu.id == q.investor_user_id
    )
    |> join(
      :left,
      [iu],
      q in subquery(
        MediaLike
        |> where(
          [ml],
          ml.inserted_at >= ^start_date and
            ml.inserted_at <= ^end_date
        )
        |> group_by([ml], ml.investor_user_id)
        |> select([ml], %{investor_user_id: ml.investor_user_id, count: count(ml.id)})
      ),
      on: iu.id == q.investor_user_id
    )
    |> join(
      :left,
      [iu],
      q in subquery(
        MediaSurveyAnswer
        |> where(
          [ms],
          ms.inserted_at >= ^start_date and
            ms.inserted_at <= ^end_date
        )
        |> group_by([ms], ms.investor_user_id)
        |> select([ms], %{
          investor_user_id: ms.investor_user_id,
          count: count(ms.media_id, :distinct)
        })
      ),
      on: iu.id == q.investor_user_id
    )
    |> order_by([iu, mc, ml, ms],
      desc: coalesce(mc.count, 0) + coalesce(ml.count, 0) + coalesce(ms.count, 0)
    )
    |> select([iu, mc, ml, ms], %{
      investor_user: iu,
      questions: coalesce(mc.count, 0),
      likes: coalesce(ml.count, 0),
      survey_responses: coalesce(ms.count, 0)
    })
    |> limit(5)
    |> Repo.all()
  end

  def get_page_performance(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id} = params) do
    has_welcome_page = WelcomePage |> where([wp], wp.profile_id == ^company_profile_id) |> Repo.exists?()

    total =
      InvestorHub
      |> where(
        [ih],
        ih.company_profile_id == ^company_profile_id and
          ih.event in [
            "announcements_page_viewed",
            "activity_updates_page_viewed",
            "welcomepage_page_viewed"
          ] and
          ih.inserted_at >= ^start_date and
          ih.inserted_at <= ^end_date
      )
      |> select([ih, wp], %{
        home:
          fragment(
            "COALESCE(SUM(CASE WHEN ? THEN 1 ELSE 0 END), 0)",
            ih.event == "announcements_page_viewed"
          ),
        updates:
          fragment(
            "COALESCE(SUM(CASE WHEN ? THEN 1 ELSE 0 END), 0)",
            ih.event == "activity_updates_page_viewed"
          ),
        welcome_page:
          fragment(
            "COALESCE(CASE WHEN ? THEN SUM(CASE WHEN ? THEN 1 ELSE 0 END) ELSE NULL END, 0)",
            ^has_welcome_page,
            ih.event == "welcomepage_page_viewed"
          )
      })
      |> Repo.one()

    result = [
      %{
        name: "Announcements",
        total: Map.get(total, :home),
        unique: get_unique_event_count("announcements_page_viewed", params)
      },
      %{
        name: "Activity updates",
        total: Map.get(total, :updates),
        unique: get_unique_event_count("activity_updates_page_viewed", params)
      }
    ]

    if has_welcome_page do
      result ++
        [
          %{
            name: "Welcome page",
            total: Map.get(total, :welcome_page),
            unique: get_unique_event_count("welcomepage_page_viewed", params)
          }
        ]

      # Tracking events for announcement page
      #  Filter out any bad tracking events, where the first part only contains digits
      # Tracking events for announcement page
      #  Filter out any bad tracking events, where the first part only contains digits
      # Tracking events for announcement page
      #  Filter out any bad tracking events, where the first part only contains digits
      # We can safely cast/join on first part as views query checks its a number

      # HELPERS
    else
      result
    end
  end

  defp get_unique_event_count(event, %{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}) do
    InvestorHub
    |> where(
      [ih],
      ih.company_profile_id == ^company_profile_id and
        ih.event == ^event and
        ih.inserted_at >= ^start_date and
        ih.inserted_at <= ^end_date
    )
    |> distinct([ih], ih.tracking_token_id)
    |> Repo.aggregate(:count)
  end

  def get_engagement(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id} = attrs) do
    views = get_views_for_date_range(start_date, end_date, company_profile_id)

    signups = get_signups_for_date_range(start_date, end_date, company_profile_id)

    emails =
      get_emails_for_date_range(
        start_date,
        end_date,
        company_profile_id,
        Map.get(attrs, :can_remove_welcome_email?, false)
      )

    close = get_interday_timeseries(start_date, end_date, company_profile_id)

    announcements =
      get_media_announcements(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id})

    updates =
      get_media_updates(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      })

    map_dated_lists_to_date_range(
      start_date,
      end_date,
      views,
      signups,
      close,
      emails,
      updates,
      announcements
    )
  end

  def get_media_engagement(%{
        media_id: media_id,
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id,
        media_type: media_type
      }) do
    views = get_views_for_date_range(start_date, end_date, company_profile_id, media_id, media_type)

    map_dated_lists_to_date_range(
      start_date,
      end_date,
      views
    )
  end

  defp get_interday_timeseries(start_date, end_date, company_profile_id) do
    with %Gaia.Markets.Ticker{} = ticker <-
           Gaia.Markets.get_ticker_by(%{company_profile_id: company_profile_id}),
         {:ok, interday_timeseries} <-
           Gaia.MarketData.get_timeseries(
             ticker,
             Timex.to_date(start_date),
             Timex.to_date(end_date)
           ) do
      interday_timeseries
    else
      _ -> []
    end
  end

  defp get_views_for_date_range(start_date, end_date, company_profile_id),
    do:
      InvestorHub
      |> where(
        [ih],
        ilike(ih.event, "%_page_viewed") and ih.inserted_at >= ^start_date and ih.inserted_at <= ^end_date and
          ih.company_profile_id == ^company_profile_id
      )
      |> join(:inner, [ih], cp in assoc(ih, :company_profile))
      |> group_by(
        [ih, cp],
        fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", ih.inserted_at, cp.timezone)
      )
      |> select([ih, cp], %{
        date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", ih.inserted_at, cp.timezone),
        total_views: count(ih.id),
        total_unique_visitors: count(ih.tracking_token_id, :distinct)
      })
      |> Repo.all()

  defp get_views_for_date_range(start_date, end_date, company_profile_id, media_id, media_type)
       when media_type in ["MediaAnnouncement", "MediaUpdate"] do
    event = "#{media_id}_#{media_page_view_type(media_type)}"

    InvestorHub
    |> where(
      [ih],
      ih.event ==
        ^event and ih.inserted_at >= ^start_date and
        ih.inserted_at <= ^end_date and ih.company_profile_id == ^company_profile_id
    )
    |> join(:inner, [ih], cp in assoc(ih, :company_profile))
    |> group_by(
      [ih, cp],
      fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", ih.inserted_at, cp.timezone)
    )
    |> select([ih, cp], %{
      date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", ih.inserted_at, cp.timezone),
      total_views: count(ih.id),
      total_unique_visitors: count(ih.tracking_token_id, :distinct)
    })
    |> Repo.all()
  end

  defp get_views_for_date_range(_, _, _, _, _), do: []

  defp media_page_view_type("MediaAnnouncement"), do: "announcement_page_viewed"
  defp media_page_view_type("MediaUpdate"), do: "activity_update_page_viewed"

  defp get_signups_for_date_range(start_date, end_date, company_profile_id),
    do:
      User
      |> where(
        [i],
        i.inserted_at >= ^start_date and i.inserted_at <= ^end_date and i.company_profile_id == ^company_profile_id
      )
      |> join(:inner, [i], cp in assoc(i, :company_profile))
      |> group_by(
        [i, cp],
        fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", i.inserted_at, cp.timezone)
      )
      |> select([i, cp], %{
        date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", i.inserted_at, cp.timezone),
        signups: count(i.id)
      })
      |> Repo.all()

  def get_emails_for_date_range_with_permission_check(
        start_date,
        end_date,
        company_profile_id,
        company_profile_user_id_or_profile_user,
        required_permission,
        can_remove_welcome_email \\ false
      ) do
    if Gaia.Companies.company_profile_user_permission_check?(company_profile_user_id_or_profile_user, required_permission) do
      get_emails_for_date_range(start_date, end_date, company_profile_id, can_remove_welcome_email)
    else
      []
    end
  end

  def get_emails_for_date_range(start_date, end_date, company_profile_id, can_remove_welcome_email? \\ false) do
    Email
    |> where(
      [e],
      e.company_profile_id == ^company_profile_id and e.sent_at >= ^start_date and
        e.sent_at <= ^end_date
    )
    |> join(:inner, [e], cp in assoc(e, :company_profile))
    |> order_by(
      [e, cp],
      fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", e.sent_at, cp.timezone)
    )
    |> select([e, cp], %{
      date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", e.sent_at, cp.timezone),
      campaign_id: e.id,
      name: e.campaign_name
    })
    |> remove_welcome_email_from_query(can_remove_welcome_email?)
    |> Repo.all()
    |> Enum.group_by(& &1.date)
    |> Enum.into([], fn {k, v} -> %{date: k, campaigns: v} end)
  end

  defp map_dated_lists_to_date_range(start_date, end_date, events, signups, close, campaigns, updates, announcements) do
    all_dates_list = start_date |> Date.range(end_date) |> Enum.map(&%{date: &1})

    (all_dates_list ++
       events ++
       signups ++ close ++ campaigns ++ updates ++ announcements)
    |> Enum.group_by(&Timex.to_date(&1.date))
    |> Enum.map(fn {key, value} ->
      Enum.reduce(
        value,
        %{
          date: key,
          announcements: [],
          updates: [],
          campaigns: [],
          close: nil,
          total_views: 0,
          total_unique_visitors: 0,
          signups: 0
        },
        fn item, acc -> Map.merge(acc, item) end
      )
    end)
    |> Enum.sort_by(&Timex.to_date(&1.date), {:asc, Date})
  end

  defp map_dated_lists_to_date_range(start_date, end_date, events) do
    all_dates_list = start_date |> Date.range(end_date) |> Enum.map(&%{date: &1})

    (all_dates_list ++
       events)
    |> Enum.group_by(&Timex.to_date(&1.date))
    |> Enum.map(fn {key, value} ->
      Enum.reduce(value, %{date: key, total_views: 0, total_unique_visitors: 0}, fn item, acc -> Map.merge(acc, item) end)
    end)
    |> Enum.sort_by(&Timex.to_date(&1.date), {:asc, Date})
  end

  def get_updates_released_in_date_range(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      }) do
    MediaUpdate
    |> join(:inner, [a], m in assoc(a, :media))
    |> where([a, m], not m.invalidated)
    |> where([ma], ma.company_profile_id == ^company_profile_id)
    |> where([ma], ma.posted_at >= ^start_date and ma.posted_at <= ^end_date)
    |> Repo.aggregate(:count)
  end

  def get_announcements_released_in_date_range(%{
        start_date: start_date,
        end_date: end_date,
        listing_key: listing_key,
        market_key: market_key
      }) do
    MediaAnnouncement
    |> join(:inner, [a], m in assoc(a, :media))
    |> where([a, m], not m.invalidated)
    |> where([ma], ma.listing_key == ^listing_key and ma.market_key == ^market_key)
    |> where([ma], ma.posted_at >= ^start_date and ma.posted_at <= ^end_date)
    |> Repo.aggregate(:count)
  end

  def views_query(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}, media_type)
      when media_type in [:announcement, :update] do
    event =
      if media_type == :announcement,
        do: "%_announcement_page_viewed",
        else: "%_update_page_viewed"

    InvestorHub
    |> where([ih], ih.company_profile_id == ^company_profile_id)
    |> where([ih], ih.inserted_at >= ^start_date)
    |> where([ih], ih.inserted_at <= ^end_date)
    |> where([ih], like(ih.event, ^event))
    |> where([ih], fragment("split_part(?, '_', 1) ~ '^\\d+$'", ih.event))
  end

  defp media_type_assoc_on_media(query, :announcement),
    do: join(query, :inner, [mc, m], a in assoc(m, :media_announcement))

  defp media_type_assoc_on_media(query, :update), do: join(query, :inner, [mc, m], a in assoc(m, :media_update))

  def questions_query(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}, media_type)
      when media_type in [:announcement, :update] do
    MediaComment
    |> join(:inner, [mc], m in assoc(mc, :media))
    |> media_type_assoc_on_media(media_type)
    |> where([mc, m, a], not mc.invalidated or m.invalidated)
    |> where(
      [mc, m],
      m.company_profile_id == ^company_profile_id and not is_nil(mc.investor_user_id)
    )
    |> where([mc], mc.inserted_at >= ^start_date and mc.inserted_at <= ^end_date)
  end

  def likes_query(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}, media_type)
      when media_type in [:announcement, :update] do
    MediaLike
    |> join(:inner, [ml], m in assoc(ml, :media))
    |> media_type_assoc_on_media(media_type)
    |> where([ml, m, a], not m.invalidated)
    |> where([ml, m], m.company_profile_id == ^company_profile_id and ml.like)
    |> where([ml], ml.inserted_at >= ^start_date and ml.inserted_at <= ^end_date)
  end

  def survey_query(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}, media_type)
      when media_type in [:announcement, :update] do
    MediaSurveyAnswer
    |> join(:inner, [ms], m in assoc(ms, :media))
    |> media_type_assoc_on_media(media_type)
    |> where([ms, m], not m.invalidated)
    |> where(
      [ms, m],
      m.company_profile_id == ^company_profile_id
    )
    |> join(:inner, [ms, m], cp in assoc(m, :company_profile))
    |> where([ms], ms.inserted_at >= ^start_date and ms.inserted_at <= ^end_date)
    |> group_by([ms, ..., cp], [ms.media_id, cp.timezone])
    |> select([ms, ..., cp], %{
      timezone: cp.timezone,
      inserted_at: fragment("MIN(?)", ms.inserted_at),
      answers: count(ms.investor_user_id, :distinct)
    })
    |> subquery()
  end

  def get_analytics_stats_for_media_type(
        %{
          start_date: start_date,
          end_date: end_date,
          last_period_start_date: last_period_start_date,
          company_profile_id: company_profile_id
        },
        media_type
      )
      when media_type in [:announcement, :update] do
    params = %{
      start_date: last_period_start_date,
      end_date: end_date,
      company_profile_id: company_profile_id
    }

    views_query =
      params
      |> views_query(media_type)
      |> select([ih], %{
        total_views:
          fragment(
            "COALESCE(SUM(CASE WHEN ? THEN 1 ELSE 0 END), 0)",
            ih.inserted_at >= ^start_date
          ),
        total_views_difference:
          fragment(
            "COALESCE(SUM(CASE WHEN ? THEN 1 ELSE 0 END) - SUM(CASE WHEN ? THEN 1 ELSE 0 END), 0)",
            ih.inserted_at >= ^start_date,
            ih.inserted_at < ^start_date
          )
      })
      |> Repo.one()

    questions_query =
      params
      |> questions_query(media_type)
      |> select(
        [mc],
        %{
          total_questions:
            fragment(
              "COALESCE(SUM(CASE WHEN ? THEN 1 ELSE 0 END), 0)",
              mc.inserted_at >= ^start_date
            ),
          total_questions_difference:
            fragment(
              "COALESCE(SUM(CASE WHEN ? THEN 1 ELSE 0 END) - SUM(CASE WHEN ? THEN 1 ELSE 0 END), 0)",
              mc.inserted_at >= ^start_date,
              mc.inserted_at < ^start_date
            )
        }
      )
      |> Repo.one()

    likes_query =
      params
      |> likes_query(media_type)
      |> select(
        [ml],
        %{
          total_likes:
            coalesce(
              fragment("SUM(CASE WHEN ? THEN 1 ELSE 0 END)", ml.inserted_at >= ^start_date),
              0
            ),
          total_likes_difference:
            coalesce(
              fragment(
                "SUM(CASE WHEN ? THEN 1 ELSE 0 END) - SUM(CASE WHEN ? THEN 1 ELSE 0 END)",
                ml.inserted_at >= ^start_date,
                ml.inserted_at < ^start_date
              ),
              0
            )
        }
      )
      |> Repo.one()

    survey_query =
      params
      |> survey_query(media_type)
      |> select(
        [ms],
        %{
          total_survey_responses:
            coalesce(
              fragment(
                "cast(SUM(CASE WHEN ? THEN ? ELSE 0 END) as integer)",
                ms.inserted_at >= ^start_date,
                ms.answers
              ),
              0
            ),
          total_survey_responses_difference:
            coalesce(
              fragment(
                "cast(SUM(CASE WHEN ? THEN ? ELSE 0 END) - SUM(CASE WHEN ? THEN ? ELSE 0 END) as integer)",
                ms.inserted_at >= ^start_date,
                ms.answers,
                ms.inserted_at < ^start_date,
                ms.answers
              ),
              0
            )
        }
      )
      |> Repo.one()

    views_query
    |> Map.merge(likes_query)
    |> Map.merge(questions_query)
    |> Map.merge(survey_query)
  end

  def get_top_three_viewed_announcements(%{
        start_date: start_date,
        end_date: end_date,
        company_profile_id: company_profile_id
      }) do
    InvestorHub
    |> where([ih], ih.company_profile_id == ^company_profile_id)
    |> where([ih], ih.inserted_at >= ^start_date)
    |> where([ih], ih.inserted_at <= ^end_date)
    |> where([ih], like(ih.event, "%_announcement_page_viewed"))
    |> where([ih], fragment("split_part(?, '_', 1) ~ '^\\d+$'", ih.event))
    |> join(:inner, [ih], a in MediaAnnouncement, on: a.id == fragment("cast(split_part(?, '_', 1) as bigint)", ih.event))
    |> group_by([ih, a], a.id)
    |> order_by([ih, a], desc: count(ih.id))
    |> select([ih, a], a)
    |> limit(3)
    |> Repo.all()
  end

  def get_top_three_viewed_updates(%{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id}) do
    InvestorHub
    |> where([ih], ih.company_profile_id == ^company_profile_id)
    |> where([ih], ih.inserted_at >= ^start_date)
    |> where([ih], ih.inserted_at <= ^end_date)
    |> where([ih], like(ih.event, "%_update_page_viewed"))
    |> where([ih], fragment("split_part(?, '_', 1) ~ '^\\d+$'", ih.event))
    |> join(:inner, [ih], a in MediaUpdate, on: a.id == fragment("cast(split_part(?, '_', 1) as bigint)", ih.event))
    |> group_by([ih, a], a.id)
    |> order_by([ih, a], desc: count(ih.id))
    |> select([ih, a], a)
    |> limit(3)
    |> Repo.all()
  end

  def get_announcements_reach_and_engagement(
        %{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id} = params
      ) do
    announcements =
      Media
      |> where([m], m.company_profile_id == ^company_profile_id)
      |> join(:inner, [m], ma in assoc(m, :media_announcement))
      |> where([m, ma], not m.invalidated)
      |> where([m, ma], ma.posted_at >= ^start_date and ma.posted_at <= ^end_date)
      |> select([m, ma], ma)
      |> Repo.all()
      |> Enum.group_by(
        &(&1.posted_at
          |> Helper.ExDay.shift_zone_from_utc()
          |> Timex.to_date())
      )
      |> Enum.into([], fn {k, v} -> %{date: k, announcements: v} end)

    views =
      params
      |> views_query(:announcement)
      |> join(:inner, [v], cp in assoc(v, :company_profile))
      |> group_by(
        [v, cp],
        fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", v.inserted_at, cp.timezone)
      )
      |> select([v, cp], %{
        date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", v.inserted_at, cp.timezone),
        total_views: count(v.id)
      })
      |> Repo.all()

    questions =
      params
      |> questions_query(:announcement)
      |> join(:inner, [mc, m, a], cp in assoc(m, :company_profile))
      |> group_by(
        [mc, m, a, cp],
        fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", mc.inserted_at, cp.timezone)
      )
      |> select([mc, m, a, cp], %{
        date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", mc.inserted_at, cp.timezone),
        total_questions: count(mc.id)
      })
      |> Repo.all()

    likes =
      params
      |> likes_query(:announcement)
      |> join(:inner, [l, m, a], cp in assoc(m, :company_profile))
      |> group_by(
        [l, m, a, cp],
        fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", l.inserted_at, cp.timezone)
      )
      |> select([l, m, a, cp], %{
        date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", l.inserted_at, cp.timezone),
        total_likes: count(l.id)
      })
      |> Repo.all()

    surveys =
      params
      |> survey_query(:announcement)
      |> group_by(
        [s],
        fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", s.inserted_at, s.timezone)
      )
      |> select([s], %{
        date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", s.inserted_at, s.timezone),
        total_survey_responses: type(sum(s.answers), :integer)
      })
      |> Repo.all()

    values_list = announcements ++ views ++ questions ++ likes ++ surveys

    merge_date_range_and_values_list(
      start_date,
      end_date,
      values_list,
      %{
        announcements: [],
        total_views: 0,
        total_questions: 0,
        total_likes: 0,
        total_survey_responses: 0
      }
    )
  end

  def get_updates_reach_and_engagement(
        %{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id} = params
      ) do
    updates =
      Media
      |> where([m], m.company_profile_id == ^company_profile_id)
      |> join(:inner, [m], ma in assoc(m, :media_update))
      |> where([m, ma], not m.invalidated)
      |> where([m, ma], ma.posted_at >= ^start_date and ma.posted_at <= ^end_date)
      |> select([m, ma], ma)
      |> Repo.all()
      |> Enum.group_by(
        &(&1.posted_at
          |> Helper.ExDay.shift_zone_from_utc()
          |> Timex.to_date())
      )
      |> Enum.into([], fn {k, v} -> %{date: k, updates: v} end)

    views =
      params
      |> views_query(:update)
      |> join(:inner, [v], cp in assoc(v, :company_profile))
      |> group_by(
        [v, cp],
        fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", v.inserted_at, cp.timezone)
      )
      |> select([v, cp], %{
        date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", v.inserted_at, cp.timezone),
        total_views: count(v.id)
      })
      |> Repo.all()

    questions =
      params
      |> questions_query(:update)
      |> join(:inner, [mc, m, u], cp in assoc(m, :company_profile))
      |> group_by(
        [mc, m, u, cp],
        fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", mc.inserted_at, cp.timezone)
      )
      |> select([mc, m, u, cp], %{
        date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", mc.inserted_at, cp.timezone),
        total_questions: count(mc.id)
      })
      |> Repo.all()

    likes =
      params
      |> likes_query(:update)
      |> join(:inner, [l, m, u], cp in assoc(m, :company_profile))
      |> group_by(
        [l, m, u, cp],
        fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", l.inserted_at, cp.timezone)
      )
      |> select([l, m, u, cp], %{
        date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", l.inserted_at, cp.timezone),
        total_likes: count(l.id)
      })
      |> Repo.all()

    surveys =
      params
      |> survey_query(:update)
      |> group_by(
        [s],
        fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", s.inserted_at, s.timezone)
      )
      |> select([s], %{
        date: fragment("date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))", s.inserted_at, s.timezone),
        total_survey_responses: type(sum(s.answers), :integer)
      })
      |> Repo.all()

    values_list = updates ++ views ++ questions ++ likes ++ surveys

    merge_date_range_and_values_list(
      start_date,
      end_date,
      values_list,
      %{
        updates: [],
        total_views: 0,
        total_questions: 0,
        total_likes: 0,
        total_survey_responses: 0
      }
    )
  end

  def merge_date_range_and_values_list_for_email_stats(start_date, end_date, value_date_lists, match_pattern) do
    start_date
    |> Date.range(end_date)
    |> Enum.map(&%{date: &1})
    |> Kernel.++(value_date_lists)
    |> Enum.group_by(&Timex.to_date(&1.date))
    |> Enum.map(fn {key, value} ->
      Enum.reduce(value, Map.put(match_pattern, :date, key), fn item, acc -> merge(acc, item) end)
    end)
    |> Enum.sort_by(&Timex.to_date(&1.date), {:asc, Date})
    |> Enum.map(fn item ->
      item
      |> Map.put(:ctr, if(item.sends == 0, do: 0, else: item.clicks / item.sends))
      |> Map.put(:open_rate, if(item.sends == 0, do: 0, else: item.opens / item.sends))
    end)
  end

  def merge_date_range_and_values_list(start_date, end_date, value_date_lists, match_pattern) do
    start_date
    |> Date.range(end_date)
    |> Enum.map(&%{date: &1})
    |> Kernel.++(value_date_lists)
    |> Enum.group_by(&Timex.to_date(&1.date))
    |> Enum.map(fn {key, value} ->
      Enum.reduce(value, Map.put(match_pattern, :date, key), fn item, acc ->
        Map.merge(acc, item)
      end)
    end)
    |> Enum.sort_by(&Timex.to_date(&1.date), {:asc, Date})
  end

  defp email_media_type_assoc_on_email_event(query, :announcement),
    do: join(query, :inner, [ee, te, er, e, m], a in assoc(m, :media_announcement))

  defp email_media_type_assoc_on_email_event(query, :update),
    do: join(query, :inner, [ee, te, er, e, m], a in assoc(m, :media_update))

  defp get_distributed_media_by_media_type(
         %{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id} = _params,
         media_type
       )
       when media_type in [:announcement, :update] do
    media_type
    |> case do
      :announcement -> MediaAnnouncement
      :update -> MediaUpdate
    end
    |> join(:inner, [a], m in assoc(a, :media))
    |> join(:inner, [a, m], e in assoc(m, :email))
    |> where([a, m, e], e.inserted_at >= ^start_date and e.inserted_at <= ^end_date and not is_nil(e.sent_at))
    |> where([a, m], m.company_profile_id == ^company_profile_id)
    |> Repo.all()
  end

  defp resolve_unique_clicks_and_opens_by_media_type(
         %{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id},
         media_type
       )
       when media_type in [:announcement, :update] do
    EmailEvent
    |> join(:inner, [ee], te in assoc(ee, :email))
    |> join(:inner, [ee, te], er in EmailRecipient, on: er.tracking_email_id == te.id)
    |> join(:inner, [ee, te, er], e in assoc(er, :email))
    |> join(:inner, [ee, te, er, e], m in assoc(e, :media))
    |> email_media_type_assoc_on_email_event(media_type)
    |> join(:inner, [ee, te, er, e, m, ...], cp in assoc(m, :company_profile))
    |> where([ee, te], te.company_profile_id == ^company_profile_id)
    |> where([ee, te, er, e], e.inserted_at >= ^start_date and e.inserted_at < ^end_date)
    |> group_by([ee], [ee.email_id, ee.event_type])
    |> group_by(
      [ee, te, ..., cp],
      fragment(
        "date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))",
        te.inserted_at,
        cp.timezone
      )
    )
    |> select([ee, te, er, e, m, u, ..., cp], %{
      email_id: ee.email_id,
      event_type: ee.event_type,
      date:
        fragment(
          "date(date_trunc('day', ? AT TIME ZONE 'UTC' AT TIME ZONE ?))",
          te.inserted_at,
          cp.timezone
        ),
      media_ids: fragment("array_agg(distinct ?)", u.id)
    })
    |> subquery()
    |> group_by([q], [q.date, q.media_ids])
    |> select([q], %{
      date: q.date,
      sends: count(q.email_id, :distinct),
      media_ids: q.media_ids,
      clicks: fragment("SUM(CASE WHEN ? THEN 1 ELSE 0 END)", q.event_type == :Click),
      opens: fragment("SUM(CASE WHEN ? THEN 1 ELSE 0 END)", q.event_type == :Open)
    })
    |> Repo.all()
  end

  def get_email_distribution_statistics_for_media_type(
        %{start_date: start_date, end_date: end_date, company_profile_id: _company_profile_id} = params,
        media_type
      )
      when media_type in [:announcement, :update] do
    distributed_media = get_distributed_media_by_media_type(params, media_type)

    resolved_unique_clicks_and_opens = resolve_unique_clicks_and_opens_by_media_type(params, media_type)

    resolved_click_through_and_open_rates_and_media =
      resolve_media_for_media_type_clicks_and_opens(resolved_unique_clicks_and_opens, distributed_media, media_type)

    default_pattern_to_match = %{
      clicks: 0,
      ctr: 0,
      opens: 0,
      open_rate: 0,
      sends: 0
    }

    pattern_to_match =
      case media_type do
        :announcement -> Map.put(default_pattern_to_match, :announcements, [])
        :update -> Map.put(default_pattern_to_match, :updates, [])
      end

    merge_date_range_and_values_list_for_email_stats(
      start_date,
      end_date,
      resolved_click_through_and_open_rates_and_media,
      pattern_to_match
    )
  end

  defp resolve_media_for_media_type_clicks_and_opens(clicks_and_opens, distributed_media, media_type)
       when media_type in [:announcement, :update] do
    Enum.map(clicks_and_opens, fn %{media_ids: media_ids} = date_values ->
      case media_type do
        :announcement ->
          Map.put(date_values, :announcements, find_media_by_ids(media_ids, distributed_media))

        :update ->
          Map.put(date_values, :updates, find_media_by_ids(media_ids, distributed_media))
      end
    end)
  end

  defp find_media_by_ids(media_ids, distributed_media) do
    media_ids
    |> Enum.map(&find_media_by_id(&1, distributed_media))
    |> Enum.filter(&(not is_nil(&1)))
  end

  defp find_media_by_id(id, distributed_media) do
    Enum.find(distributed_media, &(&1.id == id))
  end

  def get_types_views_and_engagement(
        %{start_date: start_date, end_date: end_date, company_profile_id: company_profile_id} = params
      ) do
    views =
      params
      |> views_query(:update)
      |> join(:inner, [ee], u in MediaUpdate, on: u.id == fragment("cast(split_part(?, '_', 1) as bigint)", ee.event))
      |> group_by(
        [ee, u],
        fragment(
          "CASE WHEN(COALESCE(cardinality(?), 0) > 1) THEN 'multi' else COALESCE(ARRAY_TO_STRING(?, ','), 'none') END",
          u.included_types,
          u.included_types
        )
      )
      |> select([ee, u], %{
        included_media_type:
          fragment(
            "CASE WHEN(COALESCE(cardinality(?), 0) > 1) THEN 'multi' else COALESCE(ARRAY_TO_STRING(?, ','), 'none') END",
            u.included_types,
            u.included_types
          ),
        total_views: count(ee.id)
      })
      |> Repo.all()

    questions =
      params
      |> questions_query(:update)
      |> group_by(
        [mc, m, u],
        fragment(
          "CASE WHEN(COALESCE(cardinality(?), 0) > 1) THEN 'multi' else COALESCE(ARRAY_TO_STRING(?, ','), 'none') END",
          u.included_types,
          u.included_types
        )
      )
      |> select([mc, m, u], %{
        included_media_type:
          fragment(
            "CASE WHEN(COALESCE(cardinality(?), 0) > 1) THEN 'multi' else COALESCE(ARRAY_TO_STRING(?, ','), 'none') END",
            u.included_types,
            u.included_types
          ),
        total_engagement: count(mc.id)
      })
      |> Repo.all()

    likes =
      params
      |> likes_query(:update)
      |> group_by(
        [ml, m, u],
        fragment(
          "CASE WHEN(COALESCE(cardinality(?), 0) > 1) THEN 'multi' else COALESCE(ARRAY_TO_STRING(?, ','), 'none') END",
          u.included_types,
          u.included_types
        )
      )
      |> select([ml, m, u], %{
        included_media_type:
          fragment(
            "CASE WHEN(COALESCE(cardinality(?), 0) > 1) THEN 'multi' else COALESCE(ARRAY_TO_STRING(?, ','), 'none') END",
            u.included_types,
            u.included_types
          ),
        total_engagement: count(ml.id)
      })
      |> Repo.all()

    surveys =
      MediaSurveyAnswer
      |> join(:inner, [ms], m in assoc(ms, :media))
      |> media_type_assoc_on_media(:update)
      |> where([ms, m, u], not m.invalidated)
      |> where(
        [ms, m],
        m.company_profile_id == ^company_profile_id
      )
      |> where([ms], ms.inserted_at >= ^start_date and ms.inserted_at <= ^end_date)
      |> group_by(
        [ms, m, u],
        fragment(
          "CASE WHEN(COALESCE(cardinality(?), 0) > 1) THEN 'multi' else COALESCE(ARRAY_TO_STRING(?, ','), 'none') END",
          u.included_types,
          u.included_types
        )
      )
      |> select([ms, m, u], %{
        included_media_type:
          fragment(
            "CASE WHEN(COALESCE(cardinality(?), 0) > 1) THEN 'multi' else COALESCE(ARRAY_TO_STRING(?, ','), 'none') END",
            u.included_types,
            u.included_types
          ),
        total_engagement: count(ms.investor_user_id, :distinct)
      })
      |> Repo.all()

    reduce_types_views_and_engagement(views ++ questions ++ likes ++ surveys)
  end

  defp reduce_types_views_and_engagement(values) do
    default_values =
      Enum.map(MediaUpdate.get_included_types(), &%{included_media_type: &1, total_views: 0, total_engagement: 0})

    Enum.reduce(values, default_values, &merge_types(&1, &2))
  end

  defp merge_types(%{total_views: total_views, included_media_type: included_media_type}, acc) do
    Enum.map(acc, fn item ->
      if item.included_media_type == String.to_atom(included_media_type) do
        Map.update!(item, :total_views, &(&1 + total_views))
      else
        item
      end
    end)
  end

  defp merge_types(%{total_engagement: total_engagement, included_media_type: included_media_type}, acc) do
    Enum.map(acc, fn item ->
      if item.included_media_type == String.to_atom(included_media_type) do
        Map.update!(item, :total_engagement, &(&1 + total_engagement))
      else
        item
      end
    end)
  end

  def gen_dev_test_params do
    end_date = NaiveDateTime.utc_now(:second)

    %{
      start_date: Timex.shift(end_date, days: -180),
      end_date: end_date,
      company_profile_id: 12
    }
  end

  def get_engaged_visitors_from_investor_user_actions_and_tracking_tokens(%{
        date_range: date_range,
        company_profile_id: company_profile_id
      }) do
    Enum.map(date_range, fn date ->
      valid_token =
        from(
          t in Gaia.Tracking.InvestorHub,
          select: t.investor_user_id,
          where: fragment("?::date = ?::date", t.inserted_at, ^date),
          where: t.company_profile_id == ^company_profile_id,
          where: not is_nil(t.investor_user_id)
        )

      likes =
        from(l in Gaia.Interactions.MediaLike,
          select: l.investor_user_id,
          where: fragment("?::date = ?::date", l.inserted_at, ^date),
          where: not is_nil(l.investor_user_id)
        )

      questions =
        from(l in Gaia.Interactions.MediaComment,
          select: l.investor_user_id,
          where: fragment("?::date = ?::date", l.inserted_at, ^date),
          where: not is_nil(l.investor_user_id)
        )

      follows =
        from(l in Gaia.Hubs.Follow,
          select: l.investor_user_id,
          where: fragment("?::date = ?::date", l.inserted_at, ^date),
          where: not is_nil(l.investor_user_id)
        )

      surveys =
        from(l in Gaia.Interactions.MediaSurveyAnswer,
          select: l.investor_user_id,
          where: fragment("?::date = ?::date", l.inserted_at, ^date),
          where: not is_nil(l.investor_user_id)
        )

      # add sharing announcements here !!

      engaged_investor = likes |> union_all(^questions) |> union_all(^follows) |> union_all(^surveys)

      result = Gaia.Repo.all(intersect(valid_token, ^engaged_investor))

      tracking_tokens =
        Gaia.Repo.all(
          from(t in Gaia.Tracking.InvestorHub,
            select: t.tracking_token_id,
            distinct: true,
            where: fragment("?::date = ?::date", t.inserted_at, ^date) and t.company_profile_id == ^company_profile_id
          )
        )

      returning_visitors =
        Gaia.Repo.all(
          from(t in Gaia.Tracking.Token,
            select: t.id,
            where: fragment("?::date < ?::date", t.inserted_at, ^date) and t.id in ^tracking_tokens
          )
        )

      %{
        date: date,
        unique_visitors: length(tracking_tokens),
        engaged_unique_visitors: length(result),
        returning_visitors: length(returning_visitors)
      }
    end)
  end

  def get_start_date_from_date_range(end_date, date_range) do
    date_range
    |> String.replace("Last ", "")
    |> String.replace("Past ", "")
    |> String.replace(" days", "")
    |> shift_days_back(end_date)
  end

  def get_end_date_from_date_range(start_date, date_range) do
    date_range
    |> String.replace("Last ", "")
    |> String.replace("Past ", "")
    |> String.replace(" days", "")
    |> shift_days(start_date)
  end

  defp shift_days(days, start_date) do
    days = String.to_integer(days)
    Timex.shift(start_date, days: days)
  end

  defp shift_days_back(days, end_date) do
    days = String.to_integer(days)
    Timex.shift(end_date, days: -days)
  end

  def get_total_updates(company_profile_id) do
    MediaUpdate
    |> where([mu], mu.company_profile_id == ^company_profile_id)
    |> Repo.aggregate(:count)
  end

  # For ASX company, benchmark is within market cap groups
  def get_benchmark_data(
        %Profile{latest_market_cap: m_cap, ticker: %Ticker{market_key: :asx}},
        %NaiveDateTime{} = start_date,
        %NaiveDateTime{} = end_date
      ) do
    {min_m_cap, max_m_cap} =
      cond do
        m_cap <= 30_000_000 -> {:min, 30_000_000}
        m_cap <= 100_000_000 -> {30_000_000, 100_000_000}
        true -> {100_000_000, :max}
      end

    Profile
    |> from(as: :profile)
    |> join(:inner, [profile: p], t in assoc(p, :ticker), on: t.market_key == :asx)
    |> where([profile: p], not p.is_demo)
    |> where([profile: p], not p.is_trial)
    |> maybe_add_minimum_market_cap_query(min_m_cap)
    |> maybe_add_maximum_market_cap_query(max_m_cap)
    |> benchmark_query(start_date, end_date)
    |> Repo.all()
    |> post_process_benchmark_data()
  end

  # For UK company, benchmark is across all UK companies
  def get_benchmark_data(%Profile{ticker: %Ticker{}}, %NaiveDateTime{} = start_date, %NaiveDateTime{} = end_date) do
    Profile
    |> from(as: :profile)
    |> join(:inner, [profile: p], t in assoc(p, :ticker), on: t.market_key in [:lse, :aqse])
    |> where([profile: p], not p.is_demo)
    |> where([profile: p], not p.is_trial)
    |> benchmark_query(start_date, end_date)
    |> Repo.all()
    |> post_process_benchmark_data()
  end

  def benchmark_query(base_query, %NaiveDateTime{} = start_date, %NaiveDateTime{} = end_date) do
    select(base_query, [profile: p], %{
      company_profile_id: p.id,
      likes_count:
        fragment(
          """
          (
            SELECT COUNT(ml.id)
            FROM interactions_media_likes ml
            JOIN interactions_medias m
            ON m.id = ml.media_id AND NOT m.invalidated
            WHERE
              m.company_profile_id = ?
              AND ml.like
              AND ml.inserted_at >= ?
              AND ml.inserted_at <= ?
          )
          """,
          p.id,
          ^start_date,
          ^end_date
        ),
      nominated_shareholders_count:
        fragment(
          """
          (
            SELECT COUNT(id)
            FROM contacts_contacts
            WHERE
              company_profile_id = ?
              AND NOT invalidated
              AND is_nominated_shareholder
              AND nominated_shareholder_identified_at >= ?
              AND nominated_shareholder_identified_at <= ?
          )
          """,
          p.id,
          ^start_date,
          ^end_date
        ),
      questions_count:
        fragment(
          """
          (
            SELECT COUNT(mc.id)
            FROM interactions_media_comments mc
            JOIN interactions_medias m
            ON m.id = mc.media_id AND NOT m.invalidated
            WHERE
              m.company_profile_id = ?
              AND NOT mc.invalidated
              AND (NOT mc.investor_user_id IS NULL)
              AND mc.inserted_at >= ?
              AND mc.inserted_at <= ?
          )
          """,
          p.id,
          ^start_date,
          ^end_date
        ),
      surveys_count:
        fragment(
          """
          (
            SELECT COUNT(*)
            FROM (
              SELECT m.id, ms.investor_user_id, MIN(ms.inserted_at) AS inserted_at
              FROM interactions_media_survey_answers ms
              JOIN interactions_medias m
              ON m.id = ms.media_id AND NOT m.invalidated
              WHERE
                m.company_profile_id = ?
                AND ms.inserted_at >= ?
                AND ms.inserted_at <= ?
              GROUP BY m.id, ms.investor_user_id
            ) AS survey_answers
          )
          """,
          p.id,
          ^start_date,
          ^end_date
        ),
      signups_count:
        fragment(
          """
          (
            SELECT COUNT(id)
            FROM investors_users
            WHERE company_profile_id = ? AND inserted_at >= ? AND inserted_at <= ?
          )
          """,
          p.id,
          ^start_date,
          ^end_date
        ),
      unique_visitors_count:
        fragment(
          """
          (
            SELECT COUNT(DISTINCT tracking_token_id) AS unique_tracking_token_count
            FROM tracking_investor_hub
            WHERE company_profile_id = ? AND inserted_at >= ? AND inserted_at <= ?
          )
          """,
          p.id,
          ^start_date,
          ^end_date
        ),
      views_count:
        fragment(
          """
          (
            SELECT COUNT(id)
            FROM tracking_investor_hub
            WHERE
              company_profile_id = ?
              AND inserted_at >= ?
              AND inserted_at <= ?
              AND event LIKE '%_page_viewed'
          )
          """,
          p.id,
          ^start_date,
          ^end_date
        )
    })
  end

  defp maybe_add_minimum_market_cap_query(query, min_m_cap) when is_number(min_m_cap) do
    where(query, [q], q.latest_market_cap > ^min_m_cap)
  end

  defp maybe_add_minimum_market_cap_query(query, :min), do: query

  defp maybe_add_maximum_market_cap_query(query, max_m_cap) when is_number(max_m_cap) do
    where(query, [q], q.latest_market_cap <= ^max_m_cap)
  end

  defp maybe_add_maximum_market_cap_query(query, :max), do: query

  defp post_process_benchmark_data([_ | _] = data) do
    Enum.map(data, &Map.put(&1, :hub_actions_count, &1.likes_count + &1.questions_count + &1.surveys_count))
  end

  defp remove_welcome_email_from_query(query, true) do
    from(q in query, where: q.is_welcome_email == false)
  end

  defp remove_welcome_email_from_query(query, false), do: query
end
