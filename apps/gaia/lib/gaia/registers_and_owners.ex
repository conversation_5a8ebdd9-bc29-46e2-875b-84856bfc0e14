defmodule Gaia.RegistersAndOwners do
  @moduledoc """
  The RegistersAndOwners context.
  """

  import Ecto.Query, warn: false

  alias Gaia.BeneficialOwners.Account
  alias Gaia.Registers.Shareholding

  def shareholdings_and_beneficial_owner_accounts_query_by_company_profile_id(options, company_profile_id) do
    new_filters = [%{key: "company_profile_id", value: company_profile_id} | Map.get(options, :filters, [])]

    query =
      company_profile_id
      |> shareholdings_query()
      |> union_all(^beneficial_owner_accounts_query(company_profile_id))
      |> subquery()

    options
    |> Map.put(:filters, new_filters)
    |> Enum.reduce(query, fn
      {:filters, filters}, query ->
        shareholdings_and_beneficial_owner_accounts_filter_with(query, filters)

      {:orders, orders}, query ->
        shareholding_and_beneficial_owner_accounts_order_with(query, orders)

      _, query ->
        query
    end)
  end

  defp shareholdings_and_beneficial_owner_accounts_filter_with(query, filters) do
    Enum.reduce(filters, query, fn
      %{key: _, value: "undefined"}, query ->
        query

      %{key: _, value: "none"}, query ->
        query

      %{key: _, value: ""}, query ->
        query

      %{key: "company_profile_id", value: company_profile_id}, query ->
        where(query, [q], q.company_profile_id == ^company_profile_id)

      %{key: "account_type", value: type}, query ->
        where(query, [q], q.account_type == ^type)

      %{key: "search", value: search}, query ->
        where(
          query,
          [q],
          ilike(fragment("?", q.account_name), ^"%#{search}%")
        )
    end)
  end

  defp shareholding_and_beneficial_owner_accounts_order_with(query, orders) do
    Enum.reduce(orders, query, fn
      %{key: "account_name", value: "asc"}, query ->
        order_by(query, [q], asc: q.account_name)

      %{key: "account_name", value: "desc"}, query ->
        order_by(query, [q], desc: q.account_name)

      %{key: "share_count", value: "asc"}, query ->
        order_by(query, [q], asc_nulls_first: q.share_count)

      %{key: "share_count", value: "desc"}, query ->
        order_by(query, [q], desc_nulls_last: q.share_count)

      %{key: "contact_id", value: contact_id}, query ->
        contact_id_int = contact_id |> Integer.parse() |> elem(0)

        query
        |> select_merge([q], %{
          current_contact_id:
            fragment(
              """
              CASE
                WHEN ? IS NULL THEN 1
                WHEN (? ->> 'id')::integer = ? THEN 0
                ELSE 1
              END
              """,
              q.contact,
              q.contact,
              ^contact_id_int
            )
        })
        |> subquery()
        |> order_by(
          [q],
          asc: q.current_contact_id
        )
    end)
  end

  def shareholdings_query(company_profile_id) do
    Shareholding
    |> from(as: :shareholding)
    |> join(:left, [sh], c in assoc(sh, :contact), as: :contact)
    |> where([sh], sh.company_profile_id == ^company_profile_id)
    |> select([sh, contact: c], %{
      id: sh.id,
      account_name: sh.account_name,
      share_count: sh.share_count,
      account_type: "shareholding",
      contact:
        fragment(
          "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'email', ?, 'phone_number', ?)",
          c.id,
          c.first_name,
          c.last_name,
          c.email,
          c.phone_number
        ),
      company_profile_id: sh.company_profile_id
    })
  end

  def beneficial_owner_accounts_query(company_profile_id) do
    Account
    |> from(as: :account)
    |> join(:left, [a], c in assoc(a, :contact), as: :contact)
    |> join(:inner, [a], h in assoc(a, :beneficial_owner_holdings), as: :holdings)
    |> join(:inner, [a, c, h], r in assoc(h, :report))
    |> where([a], a.company_profile_id == ^company_profile_id)
    |> order_by([a, c, h, r], desc: r.report_date)
    |> distinct([a, c, h, r], a.id)
    |> select([a, c, h, r], %{
      id: a.id,
      account_name: a.account_name,
      share_count: h.shares,
      account_type: "beneficial_owner_account",
      contact:
        fragment(
          "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'email', ?, 'phone_number', ?)",
          c.id,
          c.first_name,
          c.last_name,
          c.email,
          c.phone_number
        ),
      company_profile_id: a.company_profile_id
    })
  end
end
