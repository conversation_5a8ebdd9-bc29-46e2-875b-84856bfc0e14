defmodule Gaia.Socials.Twitter do
  @moduledoc """
  Twitter Client

  Used for Athena OAuth and distributions

  Decided not to put this client as a separate umbrella app for now
  Main reason is because this has not been built with multi-tenancy in mind
  2nd reason is to avoid confusion as we are also using Ueberauth to handle OAuth for Hermes

  We are using Twitter OAuth v1.0a as it supports both v2 API and Ads API
  We are using Twitter v2 API
  """

  import Gaia.Socials.Helper

  @base_url "https://api.twitter.com"
  @base_url_uploads "https://upload.twitter.com"

  @acceptable_status_codes [200, 201]

  defp twitter_client_id, do: Application.get_env(:athena, :twitter_client_id)
  defp twitter_client_secret, do: Application.get_env(:athena, :twitter_client_secret)

  def request_token(%{} = callback_query) do
    authorization_params_input = %{
      oauth_callback:
        "#{Application.get_env(:helper, :athena_web_url)}"
        |> URI.merge("/oauth/twitter/callback")
        |> URI.append_query(encode_query(callback_query))
        |> URI.to_string()
    }

    request_url =
      @base_url
      |> URI.parse()
      |> URI.merge("/oauth/request_token")
      |> URI.to_string()

    request_url
    |> HTTPoison.post("", [
      {"Authorization", get_authorization(request_url, authorization_params_input)}
    ])
    |> handle_response(response_type: :query)
  end

  def get_authorize_url(token) do
    @base_url
    |> URI.parse()
    |> URI.merge("/oauth/authorize")
    |> URI.append_query(encode_query(%{oauth_token: token}))
    |> URI.to_string()
  end

  def access_token(token, verifier) do
    @base_url
    |> URI.parse()
    |> URI.merge("/oauth/access_token")
    |> URI.append_query(encode_query(%{oauth_token: token, oauth_verifier: verifier}))
    |> URI.to_string()
    |> HTTPoison.post("")
    |> handle_response(response_type: :query)
  end

  def new_tweet(content, access_token, access_token_secret) do
    opts = [access_token_secret: access_token_secret]

    request_url =
      @base_url
      |> URI.parse()
      |> URI.merge("/2/tweets")
      |> URI.to_string()

    request_url
    |> HTTPoison.post(Poison.encode!(%{text: content}), [
      {"Accept", "*/*"},
      {"Authorization", get_authorization(request_url, %{oauth_token: access_token}, opts)},
      {"Content-Type", "application/json"}
    ])
    |> handle_response()
  end

  def tweet_info(tweet_id, access_token, access_token_secret) do
    opts = [access_token_secret: access_token_secret]

    request_url =
      @base_url
      |> URI.parse()
      |> URI.merge("/2/tweets/#{tweet_id}")
      |> URI.to_string()

    request_url
    |> HTTPoison.get([
      {"Accept", "*/*"},
      {"Authorization", get_authorization(request_url, %{oauth_token: access_token}, opts)},
      {"Content-Type", "application/json"}
    ])
    |> handle_response()
  end

  def new_tweet(content, access_token, access_token_secret, thumbnail_url) do
    opts = [access_token_secret: access_token_secret]

    with {:ok, %{"media_id_string" => media_id}} <-
           upload_media(thumbnail_url, access_token, access_token_secret) do
      request_url =
        @base_url
        |> URI.parse()
        |> URI.merge("/2/tweets")
        |> URI.to_string()

      body = %{text: content, media: %{media_ids: [media_id]}}

      request_url
      |> HTTPoison.post(Poison.encode!(body), [
        {"Accept", "*/*"},
        {"Authorization", get_authorization(request_url, %{oauth_token: access_token}, opts)},
        {"Content-Type", "application/json"}
      ])
      |> handle_response()
    end
  end

  def new_tweet_with_attachments(content, access_token, access_token_secret, attachments)
      when is_nil(attachments) or attachments == [] do
    # If attachments is nil or an empty list, just post a text-only tweet
    new_tweet(content, access_token, access_token_secret)
  end

  #  Attachment {
  #   orderId: number;
  #   bytes: number;
  #   format: string;
  #   name: string;
  #   cloudinaryPublicId: string;
  #   cloudinaryUrl: string;
  # }
  def new_tweet_with_attachments(content, access_token, access_token_secret, attachments) do
    opts = [access_token_secret: access_token_secret]

    media_ids =
      attachments
      |> Enum.sort_by(& &1["orderId"])
      |> Enum.map(
        &(&1["cloudinaryUrl"]
          |> upload_media(access_token, access_token_secret)
          |> case do
            {:ok, %{"media_id_string" => media_id}} -> media_id
            _ -> nil
          end)
      )
      |> Enum.filter(&(not is_nil(&1)))

    request_url =
      @base_url
      |> URI.parse()
      |> URI.merge("/2/tweets")
      |> URI.to_string()

    # Only include media parameter if there are media IDs
    # This prevents the Twitter API error when media_ids is an empty array
    body =
      if Enum.empty?(media_ids) do
        # If no valid media IDs were found, just post a text-only tweet
        %{text: content}
      else
        %{text: content, media: %{media_ids: media_ids}}
      end

    request_url
    |> HTTPoison.post(Poison.encode!(body), [
      {"Accept", "*/*"},
      {"Authorization", get_authorization(request_url, %{oauth_token: access_token}, opts)},
      {"Content-Type", "application/json"}
    ])
    |> handle_response()
  end

  def upload_media(image_url, access_token, access_token_secret) do
    opts = [access_token_secret: access_token_secret]

    request_url =
      @base_url_uploads
      |> URI.parse()
      |> URI.merge("/1.1/media/upload.json")
      |> URI.to_string()

    {:ok, image_data} = HTTPoison.get(image_url)

    body =
      {:multipart,
       [
         {"media", image_data.body}
       ]}

    request_url
    |> HTTPoison.post(body, [
      {"Accept", "*/*"},
      {"Authorization", get_authorization(request_url, %{oauth_token: access_token}, opts)}
    ])
    |> handle_response()
  end

  def get_statistics(access_token, access_token_secret, tweet_id, start_time, end_time) do
    # Specify GET method in the options
    opts = [access_token_secret: access_token_secret, method: "GET"]

    params = %{
      "tweet_ids" => Enum.join([tweet_id], ","),
      "end_time" => end_time,
      "start_time" => start_time,
      "granularity" => "Total",
      "requested_metrics" =>
        Enum.join(
          [
            "Impressions",
            "Engagements",
            "Likes",
            "Retweets",
            "Replies",
            "DetailExpands",
            "Follows",
            "UserProfileClicks"
          ],
          ","
        )
    }

    # Build base URL without query params
    base_url =
      @base_url
      |> URI.parse()
      |> URI.merge("/2/insights/historical")
      |> URI.to_string()

    # Encode query params separately
    query_string = URI.encode_query(params)

    # Generate authorization before adding query params to URL
    auth_header = get_authorization(base_url, %{oauth_token: access_token}, opts)

    # Build full URL with query params
    request_url = "#{base_url}?#{query_string}"

    # Make request with OAuth 1.0a authorization
    request_url
    |> HTTPoison.get([
      {"Authorization", auth_header}
    ])
    |> handle_response()
  end

  # Ref: https://developer.twitter.com/en/docs/authentication/oauth-1-0a/authorizing-a-request
  defp get_authorization(request_url, %{} = authorization_params_input, opts \\ []) do
    access_token_secret = Keyword.get(opts, :access_token_secret, "")
    method = Keyword.get(opts, :method, "POST")

    # Build the authorization params
    authorization_params = authorization_params(authorization_params_input)

    # Encode the string before creating signature.
    encoded_authorization_params = authorization_params |> encode_query() |> encode()

    # Create the OAuth signature.
    signature_base = "#{method}&#{encode(request_url)}&#{encoded_authorization_params}"
    signing_key = "#{twitter_client_secret()}&#{access_token_secret}"
    oauth_signature = :hmac |> :crypto.mac(:sha, signing_key, signature_base) |> Base.encode64()

    # Construct the Authorization header.
    # PS: We join each key value pairs using a comma and not ampersand
    authorization_string =
      oauth_signature
      |> authorization_params_with_signature(authorization_params, authorization_params_input)
      |> Enum.map_join(",", fn {key, value} -> encode_query(%{key => value}) end)

    "OAuth #{authorization_string}"
  end

  # Keys need to be ordered alphabetically
  # Elixir map do no guarantee keys ordering. Some versions might work for small map.
  # So cannot use Map.merge or Enum.into
  defp authorization_params(%{oauth_callback: callback}) do
    [
      oauth_callback: callback,
      oauth_consumer_key: twitter_client_id(),
      oauth_nonce: Ecto.UUID.generate(),
      oauth_signature_method: "HMAC-SHA1",
      oauth_timestamp: DateTime.utc_now() |> Timex.to_unix() |> to_string(),
      oauth_version: "1.0"
    ]
  end

  defp authorization_params(%{oauth_token: token}) do
    [
      oauth_consumer_key: twitter_client_id(),
      oauth_nonce: Ecto.UUID.generate(),
      oauth_signature_method: "HMAC-SHA1",
      oauth_timestamp: DateTime.utc_now() |> Timex.to_unix() |> to_string(),
      oauth_token: token,
      oauth_version: "1.0"
    ]
  end

  defp authorization_params_with_signature(oauth_signature, authorization_params, %{oauth_callback: _}) do
    [
      oauth_callback: Keyword.get(authorization_params, :oauth_callback),
      oauth_consumer_key: Keyword.get(authorization_params, :oauth_consumer_key),
      oauth_nonce: Keyword.get(authorization_params, :oauth_nonce),
      oauth_signature: oauth_signature,
      oauth_signature_method: Keyword.get(authorization_params, :oauth_signature_method),
      oauth_timestamp: Keyword.get(authorization_params, :oauth_timestamp),
      oauth_version: Keyword.get(authorization_params, :oauth_version)
    ]
  end

  defp authorization_params_with_signature(oauth_signature, authorization_params, %{oauth_token: _}) do
    [
      oauth_consumer_key: Keyword.get(authorization_params, :oauth_consumer_key),
      oauth_nonce: Keyword.get(authorization_params, :oauth_nonce),
      oauth_signature: oauth_signature,
      oauth_signature_method: Keyword.get(authorization_params, :oauth_signature_method),
      oauth_timestamp: Keyword.get(authorization_params, :oauth_timestamp),
      oauth_version: Keyword.get(authorization_params, :oauth_version),
      oauth_token: Keyword.get(authorization_params, :oauth_token)
    ]
  end

  defp handle_response(_httpoison_response, opts \\ [])

  defp handle_response({:ok, %HTTPoison.Response{status_code: status_code, body: resp_body}}, opts)
       when status_code in @acceptable_status_codes do
    # Different endpoints return different format of responses
    opts
    |> Keyword.get(:response_type, :json)
    |> case do
      :query -> {:ok, decode_query(resp_body)}
      :json -> {:ok, Poison.decode!(resp_body)}
    end
  end

  defp handle_response({:ok, %HTTPoison.Response{status_code: status_code, body: resp_body}}, _) do
    {:error, "Status code: #{status_code}, body: #{resp_body}"}
  end

  defp handle_response({:error, %HTTPoison.Error{reason: reason}}, _) do
    {:error, reason}
  end
end
