defmodule Gaia.Socials.LinkedIn do
  @moduledoc """
  LinkedIn Client (Version 2025)

  Used for Athena OAuth and distributions for sharing content to LinkedIn company pages.

  This module provides functions for:
  - OAuth authentication flow
  - Posting content to LinkedIn company pages
  - Retrieving organizations the user has admin access to
  - Uploading media (images, videos, documents)
  - Retrieving post statistics

  Decided not to put this client as a separate umbrella app for now
  Main reason is because this has not been built with multi-tenancy in mind
  2nd reason is to avoid confusion as we are also using Ueberauth to handle OAuth for Hermes
  """

  import Gaia.Socials.Helper

  require Logger

  @base_url "https://www.linkedin.com"
  @api_base_url "https://api.linkedin.com/rest/"
  @api_upload_url "https://api.linkedin.com/rest/images?action=initializeUpload"

  @x_restli_protocol_version "2.0.0"
  @linkedin_version "202501"

  # Status codes considered successful for LinkedIn API responses
  @acceptable_status_codes [200, 201]

  # Default timeout for HTTP requests in milliseconds
  @default_timeout 30_000

  defp linkedin_client_id, do: Application.get_env(:athena, :linkedin_client_id)
  defp linkedin_client_secret, do: Application.get_env(:athena, :linkedin_client_secret)

  @doc """
  Generates the authorization URL for the LinkedIn OAuth flow.

  The state parameter is recommended by the LinkedIn documentation as a way to prevent CSRF attacks.

  ## Parameters
    * `redirect` - The redirect path after authorization
    * `ticker` - The company ticker symbol
    * `state` - A random string to prevent CSRF attacks

  ## Returns
    * A string containing the full authorization URL

  ## Reference
    * https://learn.microsoft.com/en-us/linkedin/shared/authentication/authorization-code-flow?tabs=HTTPS1#member-approves-request
  """
  @spec get_authorization_url(String.t(), String.t(), String.t()) :: String.t()
  def get_authorization_url(redirect, ticker, state) do
    params = %{
      "response_type" => "code",
      "client_id" => linkedin_client_id(),
      "redirect_uri" => get_callback_url(redirect, ticker),
      "scope" =>
        "r_liteprofile r_ads_reporting r_organization_social rw_organization_admin w_member_social r_emailaddress r_ads w_organization_social rw_ads r_basicprofile r_organization_admin r_1st_connections_size",
      "state" => state
    }

    @base_url
    |> URI.merge("/oauth/v2/authorization")
    |> URI.append_query(encode_query(params))
    |> URI.to_string()
  end

  # Helper function to build standard LinkedIn API headers.
  #
  # Parameters:
  #   * `access_token` - The OAuth access token
  #   * `content_type` - The content type (defaults to "application/json")
  #
  # Returns:
  #   * A list of HTTP headers
  @spec build_headers(String.t(), String.t()) :: list()
  defp build_headers(access_token, content_type \\ "application/json") do
    [
      {"Content-Type", content_type},
      {"X-Restli-Protocol-Version", @x_restli_protocol_version},
      {"LinkedIn-Version", @linkedin_version},
      {"Authorization", "Bearer #{access_token}"}
    ]
  end

  # Helper function to handle HTTP responses from LinkedIn API.
  #
  # Parameters:
  #   * `response` - The HTTP response
  #   * `opts` - Options for handling the response
  #     * `:context` - String describing the API call context for better error messages
  #     * `:response_type` - Type of response to decode (:json, :query, or other)
  #
  # Returns:
  #   * `{:ok, result}` on success
  #   * `{:error, reason}` on failure
  @spec handle_response({:ok, HTTPoison.Response.t()} | {:error, HTTPoison.Error.t()}, keyword()) ::
          {:ok, any()} | {:error, String.t()}
  defp handle_response(response, opts \\ []) do
    context = Keyword.get(opts, :context, "API call")

    case response do
      {:ok, %HTTPoison.Response{status_code: status_code, body: resp_body}}
      when status_code in @acceptable_status_codes ->
        decode_response(resp_body, opts)

      {:ok, %HTTPoison.Response{status_code: status_code, body: resp_body}} ->
        error_msg = "Status code: #{status_code}, body: #{inspect(resp_body)}"
        Logger.error("LinkedIn API error in #{context}: #{error_msg}")
        {:error, error_msg}

      {:error, %HTTPoison.Error{reason: reason}} ->
        Logger.error("LinkedIn API request failed in #{context}: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # Helper function to decode API responses.
  #
  # Parameters:
  #   * `body` - The response body
  #   * `opts` - Options for decoding
  #
  # Returns:
  #   * `{:ok, decoded}` on success
  #   * `{:error, reason}` on failure
  @spec decode_response(String.t(), keyword()) :: {:ok, any()} | {:error, String.t() | any()}
  defp decode_response(body, opts) do
    response_type = Keyword.get(opts, :response_type, :json)

    case response_type do
      :json ->
        safe_decode_json(body)

      :query ->
        {:ok, decode_query(body)}

      _ ->
        {:ok, body}
    end
  end

  # Simple wrapper around Jason.decode with basic error handling
  @spec safe_decode_json(String.t()) :: {:ok, any()} | {:error, String.t()}
  defp safe_decode_json(json) do
    Jason.decode(json)
  rescue
    e ->
      Logger.error("Error decoding JSON: #{inspect(e)}")
      {:error, "Error decoding JSON"}
  end

  @doc """
  Exchanges an authorization code for an access token.

  ## Parameters
    * `code` - The authorization code received from LinkedIn
    * `redirect` - The redirect path used in the authorization request
    * `ticker` - The company ticker symbol

  ## Returns
    * `{:ok, token_data}` on success with token information
    * `{:error, reason}` on failure

  ## Token data includes:
    * `access_token` - The OAuth access token
    * `expires_in` - Token expiration time in seconds
    * `refresh_token` - Token used to refresh the access token
  """
  @spec access_token(String.t(), String.t(), String.t()) :: {:ok, map()} | {:error, any()}
  def access_token(code, redirect, ticker) do
    body =
      URI.encode_query(%{
        "client_id" => linkedin_client_id(),
        "client_secret" => linkedin_client_secret(),
        "code" => code,
        "grant_type" => "authorization_code",
        "redirect_uri" => get_callback_url(redirect, ticker)
      })

    headers = [{"Content-Type", "application/x-www-form-urlencoded"}]
    url = @base_url |> URI.merge("/oauth/v2/accessToken") |> URI.to_string()

    url
    |> HTTPoison.post(body, headers, recv_timeout: @default_timeout)
    |> handle_response()
  end

  @doc """
  Refreshes an access token using a refresh token.

  ## Parameters
    * `refresh_token` - The refresh token received when obtaining the access token

  ## Returns
    * `{:ok, token_data}` on success with new token information
    * `{:error, reason}` on failure

  ## Token data includes:
    * `access_token` - The new OAuth access token
    * `expires_in` - Token expiration time in seconds
    * `refresh_token` - New refresh token
  """
  @spec refresh_access_token(String.t()) :: {:ok, map()} | {:error, any()}
  def refresh_access_token(refresh_token) do
    body =
      URI.encode_query(%{
        "client_id" => linkedin_client_id(),
        "client_secret" => linkedin_client_secret(),
        "grant_type" => "refresh_token",
        "refresh_token" => refresh_token
      })

    headers = [{"Content-Type", "application/x-www-form-urlencoded"}]
    url = @base_url |> URI.merge("/oauth/v2/accessToken") |> URI.to_string()

    url
    |> HTTPoison.post(body, headers, recv_timeout: @default_timeout)
    |> handle_response()
  end

  # Builds the callback URL for the OAuth flow.
  #
  # Parameters:
  #   * `redirect` - The redirect path after authorization
  #   * `ticker` - The company ticker symbol
  #
  # Returns:
  #   * A string containing the full callback URL
  @spec get_callback_url(String.t(), String.t()) :: String.t()
  defp get_callback_url(redirect, ticker) do
    callback_params = %{provider: "linkedin", redirect: redirect, ticker: ticker}

    "#{Application.get_env(:helper, :athena_web_url)}"
    |> URI.merge("/oauth/linkedin/callback")
    |> URI.append_query(encode_query(callback_params))
    |> URI.to_string()
  end

  @doc """
  Finds LinkedIn organizations where the user has administrator access.

  Only users with ADMINISTRATOR, DIRECT_SPONSORED_CONTENT_POSTER or CONTENT_ADMIN roles
  can share posts to company pages.

  ## Parameters
    * `access_token` - The OAuth access token

  ## Returns
    * `{:ok, organizations}` on success with list of organizations
    * `{:error, reason}` on failure

  ## Reference
    * https://learn.microsoft.com/en-us/linkedin/marketing/community-management/organizations/organization-access-control-by-role?view=li-lms-2024-01&tabs=http
  """
  @spec find_linkedin_organisations(String.t()) :: {:ok, list(map())} | {:error, any()}
  def find_linkedin_organisations(access_token) do
    query_params = %{
      "q" => "roleAssignee",
      "role" => "ADMINISTRATOR",
      "state" => "APPROVED",
      "fields" => "organization",
      "count" => "50",
      "start" => "0"
    }

    url =
      @api_base_url
      |> URI.merge("organizationAcls")
      |> URI.append_query(encode_query(query_params))
      |> URI.to_string()

    headers = build_headers(access_token)
    opts = [context: "finding organizations"]

    request_opts = [recv_timeout: @default_timeout]

    with {:ok, decoded_resp_body} <-
           url |> HTTPoison.get(headers, request_opts) |> handle_response(opts) do
      org_ids = extract_organization_ids(decoded_resp_body)
      batch_administered_organization_lookup(org_ids, access_token)
    end
  end

  # Extracts organization IDs from the API response
  #
  # Parameters:
  #   * `response` - The decoded API response
  #
  # Returns:
  #   * List of organization IDs
  @spec extract_organization_ids(map()) :: list(String.t())
  defp extract_organization_ids(response) do
    response
    |> get_in(["elements"])
    |> Kernel.||([])
    |> Enum.map(fn element ->
      element |> get_in(["organization"]) |> String.replace("urn:li:organization:", "")
    end)
  end

  # Looks up details for a batch of organization IDs.
  #
  # Parameters:
  #   * `ids` - List of organization IDs
  #   * `access_token` - The OAuth access token
  #
  # Returns:
  #   * `{:ok, organizations}` on success with list of organization details
  #   * `{:error, reason}` on failure
  @spec batch_administered_organization_lookup(list(String.t()), String.t()) ::
          {:ok, list(map())} | {:error, any()}
  defp batch_administered_organization_lookup(ids, _access_token) when ids == [] do
    {:ok, []}
  end

  defp batch_administered_organization_lookup(ids, access_token) do
    url =
      @api_base_url
      |> URI.merge("organizations")
      |> URI.append_query("ids=List(#{Enum.join(ids, ",")})")
      |> URI.to_string()

    headers = build_headers(access_token)
    opts = [context: "looking up organizations"]

    request_opts = [recv_timeout: @default_timeout]

    with {:ok, decoded_resp_body} <-
           url |> HTTPoison.get(headers, request_opts) |> handle_response(opts) do
      results = get_in(decoded_resp_body, ["results"])

      if is_map(results) do
        organizations =
          Enum.map(results, fn {org_id, org} ->
            %{id: "urn:li:organization:#{org_id}", name: get_in(org, ["localizedName"])}
          end)

        {:ok, organizations}
      else
        Logger.error("LinkedIn API returned unexpected format for organizations: #{inspect(decoded_resp_body)}")
        {:ok, []}
      end
    end
  end

  @doc """
  Shares a post with an article link to a LinkedIn company page.

  ## Parameters
    * `access_token` - The OAuth access token
    * `commentary` - The text commentary for the post
    * `organization_urn` - The URN of the organization to post as
    * `link` - The URL to share
    * `title` - The title of the shared link
    * `thumbnail_url` - Optional URL to an image to use as thumbnail

  ## Returns
    * `{:ok, post_id}` on success with the ID of the created post
    * `{:error, reason}` on failure
  """
  @spec share_post_to_feed(
          String.t(),
          String.t(),
          String.t(),
          String.t(),
          String.t(),
          String.t() | nil
        ) ::
          {:ok, String.t()} | {:error, any()}
  def share_post_to_feed(access_token, commentary, organization_urn, link, title, thumbnail_url) do
    url = @api_base_url |> URI.merge("posts") |> URI.to_string()
    headers = build_headers(access_token)

    # Upload thumbnail if provided
    image_urn = get_thumbnail_urn(thumbnail_url, access_token, organization_urn)

    # Build the article content
    article_content = %{
      "source" => link,
      "title" => title
    }

    # Add thumbnail if available
    article_content =
      if image_urn, do: Map.put(article_content, "thumbnail", image_urn), else: article_content

    # Create request body
    request_body = %{
      "author" => organization_urn,
      "commentary" => commentary,
      "visibility" => "PUBLIC",
      "distribution" => %{
        "feedDistribution" => "MAIN_FEED",
        "targetEntities" => [],
        "thirdPartyDistributionChannels" => []
      },
      "content" => %{
        "article" => article_content
      },
      "lifecycleState" => "PUBLISHED",
      "isReshareDisabledByAuthor" => false
    }

    # Encode the map to JSON
    body = Jason.encode!(request_body)

    with {:ok, %HTTPoison.Response{status_code: status_code, headers: resp_headers}}
         when status_code in @acceptable_status_codes <-
           HTTPoison.post(url, body, headers, recv_timeout: @default_timeout),
         {:get_share_id, {key, post_id}} when key == "x-restli-id" and is_binary(post_id) <-
           {:get_share_id, Enum.find(resp_headers, fn {key, _value} -> key == "x-restli-id" end)} do
      # Clean up the thumbnail in the background
      if thumbnail_url do
        Task.start(fn -> delete_thumbnail(thumbnail_url) end)
      end

      {:ok, post_id}
    else
      {:get_share_id, _} ->
        error_msg = "Cannot find the id of the shared post in response headers"
        Logger.error("LinkedIn API error: #{error_msg}")
        {:error, error_msg}

      {:ok, %HTTPoison.Response{status_code: status_code, body: resp_body}} ->
        error_msg = "Status code: #{status_code}, body: #{resp_body}"
        Logger.error("LinkedIn API error when sharing post: #{error_msg}")
        {:error, error_msg}

      {:error, %HTTPoison.Error{reason: reason}} ->
        Logger.error("LinkedIn API request failed when sharing post: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  Shares a post to a LinkedIn company page without attachments.

  This is a specialized version of the function that handles the case when attachments is nil.
  For LinkedIn, we need to provide a valid content type even when there are no attachments.

  ## Parameters
    * `access_token` - The OAuth access token
    * `commentary` - The text commentary for the post
    * `organization_urn` - The URN of the organization to post as
    * `attachments` - Must be nil for this function clause

  ## Returns
    * `{:ok, post_id}` on success with the ID of the created post
    * `{:error, reason}` on failure
  """
  @spec share_post_to_feed_with_attachments(String.t(), String.t(), String.t(), nil) ::
          {:ok, String.t()} | {:error, any()}
  def share_post_to_feed_with_attachments(access_token, commentary, organization_urn, attachments)
      when is_nil(attachments) do
    url = @api_base_url |> URI.merge("posts") |> URI.to_string()
    headers = build_headers(access_token)

    # For LinkedIn, we need to provide a valid content type even when there are no attachments
    # Using "article" type with minimal required fields
    request_body = %{
      "author" => organization_urn,
      "commentary" => commentary,
      "visibility" => "PUBLIC",
      "distribution" => %{
        "feedDistribution" => "MAIN_FEED",
        "targetEntities" => [],
        "thirdPartyDistributionChannels" => []
      },
      "content" => %{
        "article" => %{
          "source" => "https://#{Application.get_env(:helper, :athena_web_url)}",
          # Providing a minimal title
          "title" => " "
        }
      },
      "lifecycleState" => "PUBLISHED",
      "isReshareDisabledByAuthor" => false
    }

    # Encode the map to JSON
    body = Jason.encode!(request_body)

    with {:ok, %HTTPoison.Response{status_code: status_code, headers: resp_headers}}
         when status_code in @acceptable_status_codes <-
           HTTPoison.post(url, body, headers, recv_timeout: @default_timeout),
         {:get_share_id, {key, post_id}} when key == "x-restli-id" and is_binary(post_id) <-
           {:get_share_id, Enum.find(resp_headers, fn {key, _value} -> key == "x-restli-id" end)} do
      {:ok, post_id}
    else
      {:get_share_id, _} ->
        error_msg = "Cannot find the id of the shared post in response headers"
        Logger.error("LinkedIn API error: #{error_msg}")
        {:error, error_msg}

      {:ok, %HTTPoison.Response{status_code: status_code, body: resp_body}} ->
        error_msg = "Status code: #{status_code}, body: #{resp_body}"
        Logger.error("LinkedIn API error when sharing post without attachments: #{error_msg}")
        {:error, error_msg}

      {:error, %HTTPoison.Error{reason: reason}} ->
        Logger.error("LinkedIn API request failed when sharing post without attachments: #{inspect(reason)}")

        {:error, reason}
    end
  end

  # This function handles the case when attachments are provided
  @spec share_post_to_feed_with_attachments(String.t(), String.t(), String.t(), list(map())) ::
          {:ok, String.t()} | {:error, any()}
  def share_post_to_feed_with_attachments(access_token, commentary, organization_urn, attachments) do
    url = @api_base_url |> URI.merge("posts") |> URI.to_string()
    headers = build_headers(access_token)

    # Process and upload all media attachments
    medias =
      attachments
      |> Enum.sort_by(& &1["orderId"])
      |> Enum.map(&get_file_urn(&1, access_token, organization_urn))
      |> Enum.filter(&(not is_nil(&1)))

    # Handle the content format differently based on number of media items
    # LinkedIn API requires a specific format for media content
    content =
      case medias do
        [single_media] ->
          # For a single media item, use it directly
          %{media: single_media}

        multiple_media when multiple_media != [] ->
          # For multiple media items, use the multiImage format
          # According to the LinkedIn API documentation for multiImage posts
          # https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/multiimage-post-api
          %{multiImage: %{images: multiple_media}}

        _ ->
          # Fallback for no media
          %{media: nil}
      end

    # Create request body
    request_body = %{
      "author" => organization_urn,
      "commentary" => commentary,
      "visibility" => "PUBLIC",
      "distribution" => %{
        "feedDistribution" => "MAIN_FEED",
        "targetEntities" => [],
        "thirdPartyDistributionChannels" => []
      },
      "content" => content,
      "lifecycleState" => "PUBLISHED",
      "isReshareDisabledByAuthor" => false
    }

    # Encode the map to JSON
    body = Jason.encode!(request_body)

    with {:ok, %HTTPoison.Response{status_code: status_code, headers: resp_headers}}
         when status_code in @acceptable_status_codes <-
           HTTPoison.post(url, body, headers, recv_timeout: @default_timeout),
         {:get_share_id, {key, post_id}} when key == "x-restli-id" and is_binary(post_id) <-
           {:get_share_id, Enum.find(resp_headers, fn {key, _value} -> key == "x-restli-id" end)} do
      {:ok, post_id}
    else
      {:get_share_id, _} ->
        error_msg = "Cannot find the id of the shared post in response headers"
        Logger.error("LinkedIn API error: #{error_msg}")
        {:error, error_msg}

      {:ok, %HTTPoison.Response{status_code: status_code, body: resp_body}} ->
        error_msg = "Status code: #{status_code}, body: #{resp_body}"
        Logger.error("LinkedIn API error when sharing post with attachments: #{error_msg}")
        {:error, error_msg}

      {:error, %HTTPoison.Error{reason: reason}} ->
        Logger.error("LinkedIn API request failed when sharing post with attachments: #{inspect(reason)}")

        {:error, reason}
    end
  end

  # Processes a file attachment and uploads it to LinkedIn, returning the file URN.
  #
  # Parameters:
  #   * `file` - Map containing file information
  #   * `access_token` - The OAuth access token
  #   * `organization_urn` - The URN of the organization to upload as
  #
  # Returns:
  #   * `%{id: file_urn}` on success with the URN of the uploaded file
  #   * `nil` on failure
  @spec get_file_urn(map(), String.t(), String.t()) :: map() | nil
  defp get_file_urn(
         %{"cloudinaryUrl" => file_url, "resourceType" => resource_type} = file,
         access_token,
         organization_urn
       ) do
    file_type = get_file_type(resource_type)

    with {:ok, upload_url, file_urn} <-
           initialize_file_upload(access_token, organization_urn, file),
         {:ok, true} <- upload_file(upload_url, file_url, file_type, file_urn) do
      %{id: file_urn}
    else
      error ->
        Logger.error("Failed to upload file to LinkedIn: #{inspect(error)}")
        nil
    end
  end

  # Initializes a file upload to LinkedIn.
  #
  # Parameters:
  #   * `access_token` - The OAuth access token
  #   * `organization_urn` - The URN of the organization to upload as
  #   * `file` - Map containing file information
  #
  # Returns:
  #   * `{:ok, upload_url, file_urn}` on success
  #   * `{:error, reason}` on failure
  @spec initialize_file_upload(String.t(), String.t(), map()) ::
          {:ok, String.t(), String.t()} | {:error, any()}
  defp initialize_file_upload(access_token, organization_urn, file) do
    headers = build_headers(access_token)
    file_type = get_file_type(file["resourceType"])
    request_body = build_request_body(organization_urn, file_type, file)
    api_upload_url = "https://api.linkedin.com/rest/#{file_type}s?action=initializeUpload"

    with {:ok, response} <- make_upload_request(api_upload_url, request_body, headers) do
      extract_upload_details(response, file_type)
    end
  end

  # Builds the request body for file upload initialization.
  #
  # Parameters:
  #   * `organization_urn` - The URN of the organization to upload as
  #   * `file_type` - The type of file (image, video, document)
  #   * `file` - Map containing file information
  #
  # Returns:
  #   * Map containing the request body
  @spec build_request_body(String.t(), String.t(), map()) :: map()
  defp build_request_body(organization_urn, file_type, file) do
    request_args = build_request_args(organization_urn, file_type, file)
    %{"initializeUploadRequest" => request_args}
  end

  # Builds the request arguments for file upload initialization.
  #
  # Parameters:
  #   * `organization_urn` - The URN of the organization to upload as
  #   * `file_type` - The type of file (image, video, document)
  #   * `file` - Map containing file information
  #
  # Returns:
  #   * Map containing the request arguments
  @spec build_request_args(String.t(), String.t(), map()) :: map()
  defp build_request_args(organization_urn, "video", file) do
    %{
      "owner" => organization_urn,
      "fileSizeBytes" => file["bytes"],
      "uploadCaptions" => true,
      "uploadThumbnail" => false
    }
  end

  defp build_request_args(organization_urn, _file_type, _file) do
    %{"owner" => organization_urn}
  end

  # Makes an upload request to LinkedIn.
  #
  # Parameters:
  #   * `url` - The URL to make the request to
  #   * `body` - The request body
  #   * `headers` - The request headers
  #
  # Returns:
  #   * `{:ok, response}` on success
  #   * `{:error, reason}` on failure
  @spec make_upload_request(String.t(), map(), list()) :: {:ok, map()} | {:error, any()}
  defp make_upload_request(url, body, headers) do
    case HTTPoison.post(url, Jason.encode!(body), headers, recv_timeout: @default_timeout) do
      {:ok, %HTTPoison.Response{status_code: status_code, body: body}}
      when status_code in @acceptable_status_codes ->
        safe_decode_json(body)

      {:ok, %HTTPoison.Response{status_code: status_code, body: body}} ->
        error_msg = "Status code: #{status_code}, body: #{body}"
        Logger.error("LinkedIn API error during upload request: #{error_msg}")
        {:error, error_msg}

      {:error, reason} ->
        Logger.error("LinkedIn API request failed during upload: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # Extracts upload details from the LinkedIn response.
  #
  # Parameters:
  #   * `response` - The response from LinkedIn
  #   * `file_type` - The type of file (image, video, document)
  #
  # Returns:
  #   * `{:ok, upload_url, file_urn}` on success
  #   * `{:error, reason}` on failure
  @spec extract_upload_details(map(), String.t()) ::
          {:ok, String.t(), String.t()} | {:error, String.t()}
  defp extract_upload_details(response, file_type) do
    response_value = Map.get(response, "value", %{})

    upload_url = get_upload_url(response_value, file_type)
    file_urn = Map.get(response_value, file_type)

    if upload_url && file_urn do
      {:ok, upload_url, file_urn}
    else
      {:error, "Invalid Parameters in LinkedIn upload response"}
    end
  end

  # Gets the upload URL from the LinkedIn response.
  #
  # Parameters:
  #   * `response_value` - The value field from the LinkedIn response
  #   * `file_type` - The type of file (image, video, document)
  #
  # Returns:
  #   * The upload URL or nil
  @spec get_upload_url(map(), String.t()) :: String.t() | nil
  defp get_upload_url(response_value, "video") do
    response_value
    |> Map.get("uploadInstructions", [])
    |> Enum.at(0, %{})
    |> Map.get("uploadUrl")
  end

  defp get_upload_url(response_value, _file_type) do
    Map.get(response_value, "uploadUrl")
  end

  # Maps resource types to LinkedIn file types.
  #
  # Parameters:
  #   * `file_format` - The resource type from the file
  #
  # Returns:
  #   * The LinkedIn file type
  @spec get_file_type(String.t()) :: String.t()
  defp get_file_type("raw") do
    "document"
  end

  defp get_file_type(file_format), do: file_format

  # Uploads a file to LinkedIn.
  #
  # Parameters:
  #   * `upload_url` - The URL to upload to
  #   * `file_url` - The URL of the file to upload
  #   * `file_type` - The type of file (image, video, document)
  #   * `file_urn` - The URN of the file
  #
  # Returns:
  #   * `{:ok, true}` on success
  #   * `{:error, reason}` on failure with reason as string or error details
  @spec upload_file(String.t(), String.t() | nil, String.t(), String.t()) ::
          {:ok, boolean()} | {:error, String.t() | any()}
  defp upload_file(_upload_url, file_url, _file_type, _file_urn) when is_nil(file_url) do
    {:ok, false}
  end

  defp upload_file(upload_url, file_url, file_type, file_urn) do
    headers =
      case file_type do
        "image" -> [{"Content-Type", "image/png"}, {"x-li-img-Urn", file_urn}]
        "video" -> [{"Content-Type", "video/mp4"}, {"x-li-video-Urn", file_urn}]
        _ -> [{"Content-Type", "application/pdf"}, {"x-li-doc-Urn", file_urn}]
      end

    try do
      with {:ok, %HTTPoison.Response{body: file_data, status_code: status_code}}
           when status_code in 200..299 <-
             HTTPoison.get(file_url, [], recv_timeout: @default_timeout),
           {:ok, %HTTPoison.Response{status_code: put_status_code}}
           when put_status_code in 200..299 <-
             HTTPoison.put(upload_url, file_data, headers, recv_timeout: @default_timeout) do
        {:ok, true}
      else
        {:ok, %HTTPoison.Response{status_code: status_code}} ->
          Logger.error("Failed to upload file to LinkedIn: status code #{status_code}")
          {:error, "HTTP status code: #{status_code}"}

        {:error, reason} ->
          Logger.error("Failed to upload file to LinkedIn: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      e ->
        Logger.error("Exception during file upload to LinkedIn: #{inspect(e)}")
        {:error, "Exception: #{inspect(e)}"}
    end
  end

  @doc """
  Retrieves statistics for a LinkedIn post.

  The organizationalEntityShareStatistics endpoint returns share data only within
  the past 12 months, using a rolling 12-month window.

  ## Parameters
    * `access_token` - The OAuth access token
    * `organisation_urn` - The URN of the organization that made the post
    * `post_urn` - The URN of the post to get statistics for. Supported formats:
      - `urn:li:share:...` - For standard LinkedIn shares
      - `urn:li:ugcPost:...` - For user-generated content posts

  ## Returns
    * `{:ok, post_stats}` on success with a map of statistics
    * `{:error, reason}` on failure

  ## Statistics include:
    * `id` - The URN of the post
    * `impression_count` - Number of impressions
    * `like_count` - Number of likes
    * `comment_count` - Number of comments
    * `share_count` - Number of shares
    * `clicked_count` - Number of clicks
    * `engagement` - Engagement rate

  ## Reference
    * https://learn.microsoft.com/en-us/linkedin/marketing/community-management/organizations/share-statistics?view=li-lms-2024-02&tabs=http
  """
  @spec post_statistics(String.t(), String.t(), String.t()) ::
          {:ok, map()} | {:error, any()}
  def post_statistics(access_token, organisation_urn, post_urn) do
    url = build_statistics_url(organisation_urn, post_urn)
    headers = build_headers(access_token)

    with {:ok, %HTTPoison.Response{status_code: status_code, body: resp_body}}
         when status_code in @acceptable_status_codes <-
           HTTPoison.get(url, headers),
         {:ok, decoded_resp_body} <- safe_decode_json(resp_body) do
      process_statistics_response(decoded_resp_body, post_urn)
    else
      {:ok, %HTTPoison.Response{status_code: status_code, body: resp_body}} ->
        error_msg = "Status code: #{status_code}, body: #{inspect(resp_body)}"
        Logger.error("LinkedIn API error in post_statistics: #{error_msg}")
        {:error, error_msg}

      {:error, %HTTPoison.Error{reason: reason}} ->
        Logger.error("LinkedIn API request failed in post_statistics: #{inspect(reason)}")
        {:error, reason}

      error ->
        Logger.error("Error in post_statistics: #{inspect(error)}")
        {:error, "Unexpected error in post_statistics"}
    end
  end

  # Builds the URL for fetching post statistics
  @spec build_statistics_url(String.t(), String.t()) :: String.t()
  defp build_statistics_url(organisation_urn, post_urn) do
    # Determine the correct param key based on the post_urn type
    param_key =
      cond do
        String.starts_with?(post_urn, "urn:li:share:") -> "shares"
        String.starts_with?(post_urn, "urn:li:ugcPost:") -> "ugcPosts"
        # Fallback to shares if unknown type
        true -> "shares"
      end

    # Build the query string so that List(...) is not percent-encoded
    base_params = [
      {"q", "organizationalEntity"},
      {"organizationalEntity", organisation_urn}
    ]

    # Manually build the List(...) param without encoding parentheses, but encode the URN
    encoded_urn = URI.encode_www_form(post_urn)
    list_param = "#{param_key}=List(#{encoded_urn})"

    # Encode the base params, then append the List(...) param
    base_query =
      Enum.map_join(base_params, "&", fn {k, v} -> "#{URI.encode_www_form(k)}=#{URI.encode_www_form(v)}" end)

    query_string = base_query <> "&" <> list_param

    @api_base_url
    |> URI.merge("organizationalEntityShareStatistics")
    |> URI.to_string()
    |> Kernel.<>("?#{query_string}")
  end

  # Processes the response from the statistics API
  @spec process_statistics_response(map(), String.t()) :: {:ok, map()}
  defp process_statistics_response(decoded_resp_body, post_urn) do
    elements = get_in(decoded_resp_body, ["elements"])

    if elements && length(elements) > 0 do
      post_stats = extract_post_statistics(elements, post_urn)
      {:ok, post_stats}
    else
      # If no elements are returned, create a default stats object with zeros
      default_stats = create_default_statistics(post_urn)

      # Log this situation for debugging
      Logger.info("No statistics found for LinkedIn post: #{post_urn}, returning default stats")

      {:ok, default_stats}
    end
  end

  # Extracts statistics from the API response
  @spec extract_post_statistics(list(), String.t()) :: map()
  defp extract_post_statistics(elements, post_urn) do
    elements
    |> Enum.map(fn element ->
      # Get the ID from either "share" or "ugcPost" field depending on which is present
      # For UGC posts, the API might still return the ID in the "share" field
      id = get_in(element, ["share"]) || get_in(element, ["ugcPost"]) || post_urn

      %{
        id: id,
        impression_count: get_in(element, ["totalShareStatistics", "impressionCount"]) || 0,
        like_count: get_in(element, ["totalShareStatistics", "likeCount"]) || 0,
        comment_count: get_in(element, ["totalShareStatistics", "commentCount"]) || 0,
        share_count: get_in(element, ["totalShareStatistics", "shareCount"]) || 0,
        clicked_count: get_in(element, ["totalShareStatistics", "clickCount"]) || 0,
        engagement: get_in(element, ["totalShareStatistics", "engagement"]) || 0.0
      }
    end)
    |> List.first()
  end

  # Creates default statistics when none are available
  @spec create_default_statistics(String.t()) :: map()
  defp create_default_statistics(post_urn) do
    %{
      id: post_urn,
      impression_count: 0,
      like_count: 0,
      comment_count: 0,
      share_count: 0,
      clicked_count: 0,
      engagement: 0.0
    }
  end

  # Uploads a thumbnail image to LinkedIn and returns the image URN.
  #
  # Parameters:
  #   * `picture_url` - URL of the thumbnail image
  #   * `access_token` - The OAuth access token
  #   * `organization_urn` - The URN of the organization to upload as
  #
  # Returns:
  #   * The image URN on success
  #   * `nil` on failure
  @spec get_thumbnail_urn(String.t() | nil, String.t(), String.t()) :: String.t() | nil
  defp get_thumbnail_urn(picture_url, _access_token, _organization_urn) when is_nil(picture_url) do
    nil
  end

  defp get_thumbnail_urn(picture_url, access_token, organization_urn) do
    with {:ok, upload_url, image_urn} <- initialize_upload(access_token, organization_urn),
         {:ok, true} <- upload_picture(upload_url, picture_url, image_urn) do
      image_urn
    else
      error ->
        Logger.error("Failed to upload thumbnail to LinkedIn: #{inspect(error)}")
        nil
    end
  end

  # Initializes an image upload to LinkedIn.
  #
  # Parameters:
  #   * `access_token` - The OAuth access token
  #   * `organization_urn` - The URN of the organization to upload as
  #
  # Returns:
  #   * `{:ok, upload_url, image_urn}` on success
  #   * `{:error, reason}` on failure
  @spec initialize_upload(String.t(), String.t()) ::
          {:ok, String.t(), String.t()} | {:error, any()}
  defp initialize_upload(access_token, organization_urn) do
    headers = build_headers(access_token)

    body = %{
      "initializeUploadRequest" => %{
        "owner" => organization_urn
      }
    }

    case HTTPoison.post(@api_upload_url, Jason.encode!(body), headers, recv_timeout: @default_timeout) do
      {:ok, %HTTPoison.Response{status_code: status_code, body: body}}
      when status_code in @acceptable_status_codes ->
        with {:ok, decoded_body} <- safe_decode_json(body) do
          value = Map.get(decoded_body, "value", %{})
          upload_url = Map.get(value, "uploadUrl")
          image_urn = Map.get(value, "image")

          if upload_url && image_urn do
            {:ok, upload_url, image_urn}
          else
            error_msg =
              "Missing uploadUrl or image in LinkedIn response: #{inspect(decoded_body)}"

            Logger.error(error_msg)
            {:error, error_msg}
          end
        end

      {:ok, %HTTPoison.Response{status_code: status_code, body: body}} ->
        error_msg = "Status code: #{status_code}, body: #{body}"
        Logger.error("LinkedIn API error during thumbnail upload initialization: #{error_msg}")
        {:error, error_msg}

      {:error, reason} ->
        Logger.error("LinkedIn API request failed during thumbnail upload initialization: #{inspect(reason)}")

        {:error, reason}
    end
  end

  # Uploads a picture to LinkedIn.
  #
  # Parameters:
  #   * `upload_url` - The URL to upload to
  #   * `picture_url` - The URL of the picture to upload
  #   * `image_urn` - The URN of the image
  #
  # Returns:
  #   * `{:ok, true}` on success
  #   * `{:error, reason}` on failure with reason as string or error details
  @spec upload_picture(String.t(), String.t() | nil, String.t()) ::
          {:ok, boolean()} | {:error, String.t() | any()}
  defp upload_picture(_upload_url, picture_url, _image_urn) when is_nil(picture_url) do
    {:ok, false}
  end

  defp upload_picture(upload_url, picture_url, image_urn) do
    headers = [
      {"Content-Type", "image/png"},
      {"x-li-img-Urn", image_urn}
    ]

    try do
      with {:ok, %HTTPoison.Response{body: picture_data, status_code: status_code}}
           when status_code in 200..299 <-
             HTTPoison.get(picture_url, [], recv_timeout: @default_timeout),
           {:ok, %HTTPoison.Response{status_code: put_status_code}}
           when put_status_code in 200..299 <-
             HTTPoison.put(upload_url, picture_data, headers, recv_timeout: @default_timeout) do
        {:ok, true}
      else
        {:ok, %HTTPoison.Response{status_code: status_code}} ->
          Logger.error("Failed to upload picture to LinkedIn: status code #{status_code}")
          {:error, "HTTP status code: #{status_code}"}

        {:error, reason} ->
          Logger.error("Failed to upload picture to LinkedIn: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      e ->
        Logger.error("Exception during picture upload to LinkedIn: #{inspect(e)}")
        {:error, "Exception: #{inspect(e)}"}
    end
  end

  # Deletes a thumbnail from storage after it has been uploaded to LinkedIn.
  #
  # Parameters:
  #   * `file_url` - The URL of the thumbnail to delete
  #
  # Returns:
  #   * `{:ok, message}` on success
  #   * `{:error, reason}` on failure
  @spec delete_thumbnail(String.t() | nil) :: {:ok, String.t()} | {:error, any()}
  defp delete_thumbnail(file_url) when is_nil(file_url), do: {:ok, "No thumbnail to delete"}

  defp delete_thumbnail(file_url) do
    case GoogleAPI.Storage.delete_object_by_url(file_url) do
      {:ok, _} ->
        {:ok, "Thumbnail deleted"}

      {:error, reason} ->
        Logger.error("Error deleting thumbnail: #{inspect(reason)}")
        {:error, reason}
    end
  end
end
