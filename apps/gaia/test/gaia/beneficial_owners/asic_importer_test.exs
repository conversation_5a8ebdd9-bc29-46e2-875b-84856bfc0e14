defmodule Gaia.BeneficialOwners.AsicImporterTest do
  use Gaia.DataCase, async: true

  alias <PERSON><PERSON>.BeneficialOwners.AsicImporter
  alias Gaia.BeneficialOwners.AsicMember
  alias Gaia.BeneficialOwners.AsicParser
  alias Gaia.Repo

  @company_files [
    "ROCKLANDS CORPORATE ADVISORY SERVICES PTY LIMITED.pdf",
    "CHIFLEY PORTFOLIOS PTY LIMITED.pdf",
    "MELBOR PTY. LTD..pdf",
    "RESOURCE & LAND MANAGEMENT SERVICES PTY. LTD..pdf",
    "OMNILAB MEDIA INVESTMENTS PTY LIMITED.pdf",
    "A.B.L. FIDUCIARY CORPORATION PTY. LTD..pdf",
    "JOHN SHEARER (HOLDINGS) PTY LIMITED.pdf",
    "BLACK CAT SYNDICATE LIMITED.pdf",
    "PAYNE GEOLOGICAL SERVICES PTY LTD.pdf",
    "DAVID SMITH & CO PTY LTD.pdf",
    "DICK SMITH INVESTMENTS PTY LTD.pdf",
    "JANE HANSEN SUPER PTY LIMITED.pdf",
    "LIANGROVE MEDIA PTY LIMITED.pdf",
    "LISA MARINA PTY LTD.pdf",
    "SKUNKY INVESTMENTS PTY LTD.pdf",
    "SORTIE PTY LTD.pdf",
    "THOMAS BROWN & SONS PTY. LIMITED.pdf",
    "VASMORE PTY LTD.pdf",
    "OLD SOUTH ROAD PTY LIMITED.pdf"
  ]

  defp pdf_path(filename) do
    Path.expand("files/#{filename}", __DIR__)
  end

  defp assert_member_structure(member) do
    case member.name do
      "GRAHAME EDGAR MAPP" ->
        assert member == %{
                 name: "GRAHAME EDGAR MAPP",
                 address_line_one: "'(grahame Mapp)' Level 14",
                 address_line_two: "60 Margaret Street",
                 address_city: "SYDNEY",
                 address_state: "NSW",
                 address_postcode: "2000",
                 address_country: "Australia",
                 shares: [
                   %{
                     document_number: "0001078A",
                     share_class: "ORD",
                     beneficially_held: false,
                     number_held: Decimal.new("1.0000")
                   }
                 ]
               }

      "MAPP GROUP HOLDINGS PTY LIMITED" ->
        assert member == %{
                 name: "MAPP GROUP HOLDINGS PTY LIMITED",
                 acn: "***********",
                 address_line_one: "Suite 903 Level 9",
                 address_line_two: "50 Pitt Street",
                 address_city: "SYDNEY",
                 address_state: "NSW",
                 address_postcode: "2000",
                 address_country: "Australia",
                 shares: [
                   %{
                     document_number: "7EBG65790",
                     share_class: "CUMP",
                     beneficially_held: true,
                     number_held: Decimal.new("40000.0000")
                   },
                   %{
                     document_number: "7EBG65790",
                     share_class: "ORD",
                     beneficially_held: true,
                     number_held: Decimal.new("100999.0000")
                   }
                 ]
               }

      _ ->
        assert false
    end
  end

  describe "ASIC Importer" do
    Enum.each(@company_files, fn filename ->
      test "parses and imports #{filename}" do
        pdf_path = pdf_path(unquote(filename))

        entry = %{
          client_name: unquote(filename),
          client_type: "application/pdf"
        }

        parsed = AsicParser.parse_pdf(pdf_path)
        assert parsed.organisation.acn != nil

        # BLACK CAT has no holders, being public
        unless unquote(filename) == "BLACK CAT SYNDICATE LIMITED.pdf" do
          assert length(parsed.members) > 0
        end

        {:ok, result} = AsicImporter.process_uploaded_file(pdf_path, entry)
        assert result[:organisation].acn == parsed.organisation.acn

        if unquote(filename) == "ROCKLANDS CORPORATE ADVISORY SERVICES PTY LIMITED.pdf" do
          assert parsed.organisation.acn == "***********"
        end

        if unquote(filename) == "LIANGROVE MEDIA PTY LIMITED.pdf" do
          assert parsed.organisation.acn == "***********"
        end

        Enum.each(parsed.members, fn parsed_member ->
          db_members =
            Repo.all(
              from m in AsicMember,
                where: m.name == ^parsed_member.name,
                preload: [:asic_member_share_structures]
            )

          assert length(db_members) > 0

          Enum.each(parsed_member.shares, fn share ->
            matching_share_structure =
              Enum.any?(db_members, fn db_member ->
                matching_structures =
                  Enum.filter(db_member.asic_member_share_structures, fn join ->
                    Decimal.equal?(join.number_held, share.number_held) and
                      join.beneficially_held == share.beneficially_held and
                      join.document_number == share.document_number
                  end)

                length(matching_structures) > 0
              end)

            assert matching_share_structure
          end)
        end)

        if unquote(filename) == "OMNILAB MEDIA INVESTMENTS PTY LIMITED.pdf" do
          Enum.each(parsed.members, fn parsed_member ->
            assert_member_structure(parsed_member)
          end)
        end

        if unquote(filename) == "CHIFLEY PORTFOLIOS PTY LIMITED.pdf" do
          assert parsed.organisation ==
                   %{
                     status: "Registered",
                     company_name: "CHIFLEY PORTFOLIOS PTY LIMITED",
                     acn: "***********",
                     class: "Limited By Shares",
                     abn: "***********",
                     registered_in: "New South Wales",
                     registration_date: ~D[1975-07-28],
                     next_review_date: ~D[2026-01-12],
                     name_start_date: ~D[2004-04-23],
                     previous_state_number: "17968348",
                     company_type: "Australian Proprietary Company",
                     subclass: "Proprietary Company",
                     extract_expiry_date: ~D[2026-06-03]
                   }

          assert parsed.share_structures ==
                   [
                     %{
                       description: "CLASS A SHARES",
                       class: "A",
                       number_issued: 2,
                       document_number: "2E6923656"
                     },
                     %{
                       description: "CLASS B SHARES",
                       class: "B",
                       number_issued: 1,
                       document_number: "0E0360651"
                     }
                   ]

          assert parsed.members ==
                   [
                     %{
                       name: "DAVID ROSS HANNON",
                       address_city: "MOSMAN",
                       address_country: "Australia",
                       address_line_one: "12 Musgrave Street",
                       address_line_two: nil,
                       address_postcode: "2088",
                       address_state: "NSW",
                       shares: [
                         %{
                           beneficially_held: false,
                           document_number: "6ERB47434",
                           number_held: Decimal.new("0.3333"),
                           share_class: "A"
                         },
                         %{
                           beneficially_held: true,
                           document_number: "6ERB47434",
                           number_held: Decimal.new("1.0000"),
                           share_class: "A"
                         },
                         %{
                           beneficially_held: true,
                           document_number: "6ERB47434",
                           number_held: Decimal.new("1.0000"),
                           share_class: "B"
                         }
                       ]
                     },
                     %{
                       name: "JAMES ALAN DICK",
                       address_city: "KOGARAH",
                       address_country: "Australia",
                       address_line_one: "33 Rocky Point Road",
                       address_line_two: nil,
                       address_postcode: "2217",
                       address_state: "NSW",
                       shares: [
                         %{
                           beneficially_held: false,
                           document_number: "6ERB47434",
                           number_held: Decimal.new("0.3333"),
                           share_class: "A"
                         }
                       ]
                     },
                     %{
                       name: "JULIANNE MARY HOWARD",
                       address_city: "FRESHWATER",
                       address_country: "Australia",
                       address_line_one: "10 Charles Street",
                       address_line_two: nil,
                       address_postcode: "2096",
                       address_state: "NSW",
                       shares: [
                         %{
                           beneficially_held: false,
                           document_number: "6ERB47434",
                           number_held: Decimal.new("0.3333"),
                           share_class: "A"
                         }
                       ]
                     }
                   ]
        end
      end
    end)
  end
end
