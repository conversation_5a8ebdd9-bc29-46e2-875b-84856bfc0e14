defmodule Gaia.Interactions.MediaChangesetTest do
  use Gaia.DataCase, async: true

  alias Gaia.Interactions.Media

  describe "changeset/2 with primary distribution calculation" do
    test "automatically sets primary_distribution when distribution flags change" do
      media = %Media{}

      attrs = %{
        company_profile_id: 1,
        distribution_announcement_enabled: true,
        distribution_update_enabled: false,
        distribution_email_enabled: false,
        distribution_linkedin_enabled: false,
        distribution_twitter_enabled: false
      }

      changeset = Media.changeset(media, attrs)

      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :primary_distribution) == "announcement"
    end

    test "sets primary_distribution to 'update' when only update is enabled" do
      media = %Media{}

      attrs = %{
        company_profile_id: 1,
        distribution_announcement_enabled: false,
        distribution_update_enabled: true,
        distribution_email_enabled: false,
        distribution_linkedin_enabled: false,
        distribution_twitter_enabled: false
      }

      changeset = Media.changeset(media, attrs)

      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :primary_distribution) == "update"
    end

    test "sets primary_distribution to 'linkedin' when linkedin and twitter are enabled" do
      media = %Media{}

      attrs = %{
        company_profile_id: 1,
        distribution_announcement_enabled: false,
        distribution_update_enabled: false,
        distribution_email_enabled: false,
        distribution_linkedin_enabled: true,
        distribution_twitter_enabled: true
      }

      changeset = Media.changeset(media, attrs)

      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :primary_distribution) == "linkedin"
    end

    test "sets primary_distribution to nil when no distributions are enabled" do
      media = %Media{}

      attrs = %{
        company_profile_id: 1,
        distribution_announcement_enabled: false,
        distribution_update_enabled: false,
        distribution_email_enabled: false,
        distribution_linkedin_enabled: false,
        distribution_twitter_enabled: false
      }

      changeset = Media.changeset(media, attrs)

      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :primary_distribution) == nil
    end

    test "does not change primary_distribution when distribution flags are not changed" do
      media = %Media{
        company_profile_id: 1,
        primary_distribution: "existing_value"
      }

      attrs = %{
        title: "New title"
      }

      changeset = Media.changeset(media, attrs)

      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :primary_distribution) == nil
    end

    test "updates primary_distribution when existing media has distribution flags changed" do
      media = %Media{
        company_profile_id: 1,
        primary_distribution: "announcement",
        distribution_announcement_enabled: true,
        distribution_update_enabled: false,
        distribution_email_enabled: false,
        distribution_linkedin_enabled: false,
        distribution_twitter_enabled: false
      }

      # Change to only enable update
      attrs = %{
        distribution_announcement_enabled: false,
        distribution_update_enabled: true
      }

      changeset = Media.changeset(media, attrs)

      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :primary_distribution) == "update"
    end
  end
end
