defmodule Gaia.Interactions.MediaStatsCacheTest do
  use Gaia.DataCase, async: false

  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaStatsCache

  setup do
    company_profile = company_profile()
    {:ok, company_profile: company_profile}
  end

  describe "get_recent_media_for_refresh/1" do
    test "returns empty list when no recent media exists", %{company_profile: _company_profile} do
      result = MediaStatsCache.get_recent_media_for_refresh(10)
      assert result == []
    end

    test "filters media by invalidated flag", %{company_profile: company_profile} do
      # Create valid media with recent distribution
      {:ok, valid_media} = create_media_with_recent_announcement(company_profile)

      # Create invalidated media
      {:ok, invalid_media} = create_media_with_recent_announcement(company_profile)
      invalid_media |> Media.changeset(%{invalidated: true}) |> Repo.update!()

      result = MediaStatsCache.get_recent_media_for_refresh(10)

      # Should only return valid media (if any are found)
      media_ids = Enum.map(result, & &1.id)

      if valid_media.id in media_ids do
        refute invalid_media.id in media_ids
      end
    end

    test "filters media by cache staleness", %{company_profile: company_profile} do
      # Create media with recent cache update
      {:ok, fresh_media} = create_media_with_recent_announcement(company_profile)
      fresh_media |> Media.changeset(%{cached_stats_last_updated: DateTime.utc_now()}) |> Repo.update!()

      # Create media with stale cache
      {:ok, stale_media} = create_media_with_recent_announcement(company_profile)
      two_hours_ago = DateTime.add(DateTime.utc_now(), -2 * 60 * 60, :second)
      stale_media |> Media.changeset(%{cached_stats_last_updated: two_hours_ago}) |> Repo.update!()

      result = MediaStatsCache.get_recent_media_for_refresh(10)

      # Should only return stale media (if any are found)
      media_ids = Enum.map(result, & &1.id)

      if stale_media.id in media_ids do
        refute fresh_media.id in media_ids
      end
    end

    test "respects batch size limit", %{company_profile: company_profile} do
      # Create multiple media items
      for _ <- 1..5 do
        create_media_with_recent_announcement(company_profile)
      end

      result = MediaStatsCache.get_recent_media_for_refresh(3)
      assert length(result) <= 3
    end

    test "handles database errors gracefully" do
      # Test with invalid batch size to potentially trigger error
      result = MediaStatsCache.get_recent_media_for_refresh(-1)
      assert is_list(result)
    end

    test "preloads all required associations", %{company_profile: company_profile} do
      {:ok, _media} = create_media_with_recent_announcement(company_profile)

      result = MediaStatsCache.get_recent_media_for_refresh(10)

      if length(result) > 0 do
        media = hd(result)
        # Verify associations are loaded (not NotLoaded structs)
        assert %Ecto.Association.NotLoaded{} != media.media_announcement
        assert %Ecto.Association.NotLoaded{} != media.media_update
        assert %Ecto.Association.NotLoaded{} != media.email
        assert %Ecto.Association.NotLoaded{} != media.linkedin_social_post
        assert %Ecto.Association.NotLoaded{} != media.twitter_social_post
      end
    end
  end

  describe "get_older_media_for_refresh/1" do
    test "returns empty list when no older media exists" do
      result = MediaStatsCache.get_older_media_for_refresh(10)
      assert result == []
    end

    test "filters by daily cache staleness", %{company_profile: company_profile} do
      # Create media with fresh daily cache
      {:ok, fresh_media} = create_media_with_recent_announcement(company_profile)
      fresh_media |> Media.changeset(%{cached_stats_last_updated: DateTime.utc_now()}) |> Repo.update!()

      # Create media with stale daily cache (2 days ago)
      {:ok, stale_media} = create_media_with_recent_announcement(company_profile)
      two_days_ago = DateTime.add(DateTime.utc_now(), -2 * 24 * 60 * 60, :second)
      stale_media |> Media.changeset(%{cached_stats_last_updated: two_days_ago}) |> Repo.update!()

      result = MediaStatsCache.get_older_media_for_refresh(10)

      # Should only return stale media (if any are found)
      media_ids = Enum.map(result, & &1.id)

      if stale_media.id in media_ids do
        refute fresh_media.id in media_ids
      end
    end

    test "only returns media with published distributions", %{company_profile: company_profile} do
      # Create media with published distribution
      {:ok, _published_media} = create_media_with_recent_announcement(company_profile)

      # Create media without any published distributions (draft only)
      {:ok, _draft_media} = create_draft_only_media(company_profile)

      result = MediaStatsCache.get_older_media_for_refresh(10)

      # Should only return published media - verify this by checking that all returned media have published distributions
      for media <- result do
        published_date = Media.get_latest_published_date(media)
        assert published_date != nil, "Media #{media.id} should have a published distribution"
      end
    end

    test "handles database errors gracefully" do
      result = MediaStatsCache.get_older_media_for_refresh(-1)
      assert is_list(result)
    end
  end

  describe "get_stale_media_for_refresh/2" do
    test "returns empty list when no stale media exists" do
      result = MediaStatsCache.get_stale_media_for_refresh(7, 10)
      assert result == []
    end

    test "filters by custom stale_days parameter", %{company_profile: company_profile} do
      # Create media with cache from 3 days ago
      {:ok, recent_media} = create_media_with_recent_announcement(company_profile)
      three_days_ago = DateTime.add(DateTime.utc_now(), -3 * 24 * 60 * 60, :second)
      recent_media |> Media.changeset(%{cached_stats_last_updated: three_days_ago}) |> Repo.update!()

      # Create media with cache from 10 days ago
      {:ok, old_media} = create_media_with_recent_announcement(company_profile)
      ten_days_ago = DateTime.add(DateTime.utc_now(), -10 * 24 * 60 * 60, :second)
      old_media |> Media.changeset(%{cached_stats_last_updated: ten_days_ago}) |> Repo.update!()

      # With stale_days = 7, should only return 10-day-old media
      result = MediaStatsCache.get_stale_media_for_refresh(7, 10)
      media_ids = Enum.map(result, & &1.id)

      assert old_media.id in media_ids
      refute recent_media.id in media_ids
    end

    test "respects batch size limit", %{company_profile: company_profile} do
      # Create multiple stale media items
      for _ <- 1..5 do
        {:ok, media} = create_media_with_recent_announcement(company_profile)
        old_date = DateTime.add(DateTime.utc_now(), -10 * 24 * 60 * 60, :second)
        media |> Media.changeset(%{cached_stats_last_updated: old_date}) |> Repo.update!()
      end

      result = MediaStatsCache.get_stale_media_for_refresh(7, 3)
      assert length(result) <= 3
    end

    test "handles database errors gracefully" do
      result = MediaStatsCache.get_stale_media_for_refresh(-1, -1)
      assert is_list(result)
    end
  end

  describe "calculate_and_cache_stats/1" do
    test "calculates stats from basic media", %{company_profile: company_profile} do
      {:ok, media} = create_media_with_recent_announcement(company_profile)

      {:ok, updated_media} = MediaStatsCache.calculate_and_cache_stats(media)

      assert is_integer(updated_media.cached_stats_total_impressions)
      assert is_map(updated_media.cached_stats)
      assert is_integer(updated_media.cached_stats.email_views)
      assert is_integer(updated_media.cached_stats.announcement_views)
      assert is_integer(updated_media.cached_stats.update_views)
      assert is_integer(updated_media.cached_stats.linkedin_views)
      assert is_integer(updated_media.cached_stats.twitter_views)
      assert not is_nil(updated_media.cached_stats_last_updated)
    end

    test "handles missing associations by preloading them", %{company_profile: company_profile} do
      {:ok, media} = create_media_with_recent_announcement(company_profile)

      # Get media without preloaded associations
      bare_media = Repo.get!(Media, media.id)

      # Should still work by auto-preloading
      {:ok, updated_media} = MediaStatsCache.calculate_and_cache_stats(bare_media)

      assert not is_nil(updated_media.cached_stats_last_updated)
      assert is_map(updated_media.cached_stats)
    end

    test "handles missing distributions gracefully", %{company_profile: company_profile} do
      {:ok, media} = create_draft_only_media(company_profile)

      {:ok, updated_media} = MediaStatsCache.calculate_and_cache_stats(media)

      assert updated_media.cached_stats_total_impressions == 0
      assert updated_media.cached_stats.email_views == 0
      assert updated_media.cached_stats.announcement_views == 0
      assert updated_media.cached_stats.update_views == 0
      assert updated_media.cached_stats.linkedin_views == 0
      assert updated_media.cached_stats.twitter_views == 0
    end
  end

  describe "refresh_recent_media_stats/0" do
    test "enqueues jobs for recent media", %{company_profile: company_profile} do
      # Create some recent media
      for _ <- 1..3 do
        create_media_with_recent_announcement(company_profile)
      end

      {:ok, result} = MediaStatsCache.refresh_recent_media_stats()

      assert is_integer(result.jobs_enqueued)
      assert is_integer(result.success)
      assert is_integer(result.errors)
      assert result.success >= 0
      assert result.errors >= 0
    end

    test "handles empty media list" do
      {:ok, result} = MediaStatsCache.refresh_recent_media_stats()

      assert result.jobs_enqueued == 0
      assert result.success == 0
      assert result.errors == 0
    end
  end

  describe "refresh_older_media_stats/1" do
    test "processes batches of older media", %{company_profile: company_profile} do
      # Create some older media
      for _ <- 1..5 do
        {:ok, media} = create_media_with_recent_announcement(company_profile)
        old_date = DateTime.add(DateTime.utc_now(), -2 * 24 * 60 * 60, :second)
        media |> Media.changeset(%{cached_stats_last_updated: old_date}) |> Repo.update!()
      end

      {:ok, result} = MediaStatsCache.refresh_older_media_stats(2)

      assert is_integer(result.batches)
      assert is_integer(result.jobs_enqueued)
      assert is_integer(result.success)
      assert is_integer(result.errors)
      assert result.batches >= 0
    end

    test "stops when no more media found" do
      {:ok, result} = MediaStatsCache.refresh_older_media_stats(5)

      assert result.batches == 0
      assert result.jobs_enqueued == 0
    end
  end

  describe "refresh_stale_media_stats/2" do
    test "processes batches of stale media", %{company_profile: company_profile} do
      # Create some stale media
      for _ <- 1..3 do
        {:ok, media} = create_media_with_recent_announcement(company_profile)
        old_date = DateTime.add(DateTime.utc_now(), -10 * 24 * 60 * 60, :second)
        media |> Media.changeset(%{cached_stats_last_updated: old_date}) |> Repo.update!()
      end

      {:ok, result} = MediaStatsCache.refresh_stale_media_stats(7, 2)

      assert is_integer(result.batches)
      assert is_integer(result.jobs_enqueued)
      assert is_integer(result.success)
      assert is_integer(result.errors)
    end

    test "respects stale_days parameter", %{company_profile: company_profile} do
      # Create media that's 5 days old
      {:ok, media} = create_media_with_recent_announcement(company_profile)
      five_days_ago = DateTime.add(DateTime.utc_now(), -5 * 24 * 60 * 60, :second)
      media |> Media.changeset(%{cached_stats_last_updated: five_days_ago}) |> Repo.update!()

      # Should not process with stale_days = 7
      {:ok, result} = MediaStatsCache.refresh_stale_media_stats(7, 1)
      assert result.jobs_enqueued == 0

      # Should process with stale_days = 3
      {:ok, result} = MediaStatsCache.refresh_stale_media_stats(3, 1)
      assert result.jobs_enqueued > 0
    end
  end

  # Helper functions for creating test data
  defp create_media_with_recent_announcement(company_profile) do
    ticker = Repo.preload(company_profile, :ticker).ticker

    {:ok, media} =
      Gaia.Interactions.create_media(%{
        company_profile_id: company_profile.id,
        email_distribution_method: :automated
      })

    # Create a recent announcement
    {:ok, _announcement} =
      Gaia.Interactions.create_media_announcement(%{
        media_id: media.id,
        header: "Test Announcement",
        summary: "Test summary",
        posted_at: DateTime.utc_now(),
        listing_key: ticker.listing_key,
        market_key: Atom.to_string(ticker.market_key),
        rectype: "10000",
        subtypes: ["10007"]
      })

    {:ok, Repo.preload(media, [:media_announcement, :media_update, :email, :linkedin_social_post, :twitter_social_post])}
  end

  defp create_draft_only_media(company_profile) do
    {:ok, media} =
      Gaia.Interactions.create_media(%{
        company_profile_id: company_profile.id,
        email_distribution_method: :automated
      })

    {:ok, Repo.preload(media, [:media_announcement, :media_update, :email, :linkedin_social_post, :twitter_social_post])}
  end

  describe "Date/Time Helper Functions (Integration)" do
    test "functions work correctly through public interface" do
      # Test that date/time helpers work by verifying the functions don't crash
      batch_size = 1

      result1 = MediaStatsCache.get_recent_media_for_refresh(batch_size)
      assert is_list(result1)

      result2 = MediaStatsCache.get_older_media_for_refresh(batch_size)
      assert is_list(result2)

      result3 = MediaStatsCache.get_stale_media_for_refresh(7, batch_size)
      assert is_list(result3)
    end
  end
end
