defmodule Gaia.AnalysisTest do
  use Gaia.DataCase, async: false

  alias Gaia.Analysis
  alias Gaia.Comms

  describe "get_engagement/1" do
    setup [:base_setup, :default_email]

    test "should return email campaign", %{
      company_profile: company_profile,
      email: %{id: email_id, campaign_name: name}
    } do
      [engagement | _] =
        get_engagement(%{
          company_profile_id: company_profile.id,
          start_date: Timex.shift(DateTime.utc_now(), days: -1),
          end_date: DateTime.utc_now()
        })

      assert [%{name: ^name, campaign_id: ^email_id}] = engagement.campaigns
    end

    test "returns only non-welcome investor emails", %{
      company_profile: company_profile,
      profile_user: profile_user,
      email: %{id: email_id, campaign_name: name}
    } do
      attrs = %{
        campaign_name: Faker.Pokemon.name(),
        subject: Faker.Pokemon.name(),
        email_html: "<p>#{Faker.Lorem.paragraph()}</p>",
        last_updated_by: profile_user.id,
        company_profile_id: company_profile.id,
        is_draft: false,
        invalidated: false,
        is_welcome_email: true,
        sent_at: Timex.shift(DateTime.utc_now(), days: -1)
      }

      Comms.create_email(attrs)

      [engagement | _] =
        get_engagement(%{
          company_profile_id: company_profile.id,
          start_date: Timex.shift(DateTime.utc_now(), days: -1),
          end_date: DateTime.utc_now(),
          can_remove_welcome_email?: true
        })

      assert [%{name: ^name, campaign_id: ^email_id}] = engagement.campaigns
    end

    test "returns all emails", %{
      company_profile: company_profile,
      profile_user: profile_user,
      email: %{id: email_id, campaign_name: name}
    } do
      attrs = %{
        campaign_name: Faker.Pokemon.name(),
        subject: Faker.Pokemon.name(),
        email_html: "<p>#{Faker.Lorem.paragraph()}</p>",
        last_updated_by: profile_user.id,
        company_profile_id: company_profile.id,
        is_draft: false,
        invalidated: false,
        is_welcome_email: true,
        sent_at: Timex.shift(DateTime.utc_now(), days: -1)
      }

      {:ok, %{id: welcome_id, campaign_name: welcome_name}} = Comms.create_email(attrs)

      [engagement | _] =
        get_engagement(%{
          company_profile_id: company_profile.id,
          start_date: Timex.shift(DateTime.utc_now(), days: -1),
          end_date: DateTime.utc_now(),
          can_remove_welcome_email?: false
        })

      assert Enum.any?(engagement.campaigns, &(&1.campaign_id == email_id))
      assert Enum.any?(engagement.campaigns, &(&1.campaign_id == welcome_id))

      assert Enum.any?(engagement.campaigns, &(&1.name == name))
      assert Enum.any?(engagement.campaigns, &(&1.name == welcome_name))

      assert length(engagement.campaigns) == 2
    end
  end

  defp get_engagement(props) do
    props
    |> Analysis.get_engagement()
    |> Enum.filter(fn engagement -> length(engagement.campaigns) > 0 end)
  end

  defp base_setup(_context) do
    company_profile = company_profile()

    profile_user = company_profile_user(%{profile_id: company_profile.id})
    logged_in_user = profile_user.user

    [company_profile: company_profile, logged_in_user: logged_in_user, profile_user: profile_user]
  end

  defp default_email(%{company_profile: company_profile, profile_user: profile_user}) do
    attrs = %{
      campaign_name: Faker.Pokemon.name(),
      subject: Faker.Pokemon.name(),
      email_html: "<p>#{Faker.Lorem.paragraph()}</p>",
      last_updated_by: profile_user.id,
      company_profile_id: company_profile.id,
      is_draft: false,
      invalidated: false,
      is_welcome_email: false,
      sent_at: Timex.shift(DateTime.utc_now(), days: -1)
    }

    {:ok, email} = Comms.create_email(attrs)

    [email: email]
  end
end
