defmodule Gaia.BeneficialOwnersTest do
  use Gaia.DataCase

  alias Gaia.BeneficialOwners
  alias Gaia.BeneficialOwners.Report

  describe "create_report/1" do
    setup [:base_setup, :report_attrs_setup]

    test "return created report with valid param", %{attrs: attrs, company: company} do
      assert {:ok, %Report{} = report} = BeneficialOwners.create_report(attrs)
      assert report.report_date == attrs.report_date
      assert report.company_profile_id == company.id
    end

    test "returns error changeset with invalid params" do
      assert {:error, %Ecto.Changeset{}} = BeneficialOwners.create_report(%{})
    end
  end

  describe "list_reports/1" do
    setup [:base_setup, :report_attrs_setup]

    test "returns all reports for a company", %{company: company, attrs: attrs} do
      {:ok, report} = BeneficialOwners.create_report(attrs)
      report = Repo.preload(report, :report_details)
      assert BeneficialOwners.reports_for_company_with_details(company.id) == [report]
    end

    test "returns empty list if no reports found" do
      assert BeneficialOwners.reports_for_company_with_details(0) == []
    end

    test "returns reports with it's details", %{company: company, attrs: attrs} do
      {:ok, %Report{} = report} = BeneficialOwners.create_report(attrs)

      details_attrs = %{
        registered_holder_name: Faker.Person.name(),
        beneficial_owner_name: Faker.Pokemon.name(),
        beneficial_owner_holdings: Enum.random(1000..10_000),
        # Note this is the correct field name
        beneficial_owner_report_id: report.id
      }

      {:ok, details} = BeneficialOwners.create_report_detail(details_attrs)

      assert [%_{report_details: [^details]}] =
               BeneficialOwners.reports_for_company_with_details(company.id)
    end
  end

  describe "upsert_candidates_multi/3" do
    setup [:base_setup, :report_setup]

    test "inserts multiple candidates as children of a parent", %{
      company: _company,
      report: report
    } do
      # Create a parent candidate first
      {:ok, parent_candidate} =
        BeneficialOwners.create_report_candidate(%{
          report_id: report.id,
          account_name: "Parent Company",
          shares: 10_000,
          layer: 1,
          status: :done
        })

      # Create a list of candidates to insert
      candidates = [
        %{
          "account_name" => "Child Company 1",
          "shares" => "5000",
          "address_line_one" => "123 Main St",
          "address_city" => "Sydney",
          "address_state" => "NSW",
          "address_postcode" => "2000",
          "address_country" => "Australia"
        },
        %{
          "account_name" => "Child Company 2",
          "shares" => "3000",
          "address_line_one" => "456 High St",
          "address_city" => "Melbourne",
          "address_state" => "VIC",
          "address_postcode" => "3000",
          "address_country" => "Australia"
        }
      ]

      # Call the function
      {:ok, result} =
        BeneficialOwners.upsert_candidates_multi(candidates, parent_candidate, report.id)

      # Verify the result
      assert {2, inserted_candidates} = result.candidates
      assert length(inserted_candidates) == 2

      # Verify the candidates were inserted correctly
      for candidate <- inserted_candidates do
        assert candidate.report_id == report.id
        assert candidate.parent_id == parent_candidate.id
        assert candidate.layer == parent_candidate.layer + 1

        # Find the corresponding input candidate
        input_candidate = Enum.find(candidates, &(&1["account_name"] == candidate.account_name))
        assert candidate.shares == String.to_integer(input_candidate["shares"])
        assert candidate.address_line_one == input_candidate["address_line_one"]
        assert candidate.address_city == input_candidate["address_city"]
        assert candidate.address_state == input_candidate["address_state"]
        assert candidate.address_postcode == input_candidate["address_postcode"]
        assert candidate.address_country == input_candidate["address_country"]
      end

      # Verify the candidates are in the database
      db_candidates =
        Gaia.BeneficialOwners.ReportCandidate
        |> where([rc], rc.report_id == ^report.id and rc.parent_id == ^parent_candidate.id)
        |> Repo.all()

      assert length(db_candidates) == 2
    end

    test "updates two candidates with the same account name", %{company: _company, report: report} do
      # Create a parent candidate first
      {:ok, parent_candidate} =
        BeneficialOwners.create_report_candidate(%{
          report_id: report.id,
          account_name: "Parent Company",
          shares: 10_000,
          layer: 1,
          status: :done
        })

      # Create an initial candidate
      initial_candidate = %{
        "account_name" => "Existing Child",
        "shares" => "5000",
        "address_line_one" => "123 Main St",
        "address_city" => "Sydney",
        "address_state" => "NSW",
        "address_postcode" => "2000",
        "address_country" => "Australia"
      }

      # Insert the initial candidate
      {:ok, initial_result} =
        BeneficialOwners.upsert_candidates_multi([initial_candidate], parent_candidate, report.id)

      {1, [inserted_candidate]} = initial_result.candidates

      # Create an updated version of the same candidate
      updated_candidate = %{
        # Same name to trigger conflict
        "account_name" => "Existing Child",
        # Updated shares
        "shares" => "7500",
        # Updated address
        "address_line_one" => "789 New St",
        "address_city" => "Brisbane",
        "address_state" => "QLD",
        "address_postcode" => "4000",
        "address_country" => "Australia"
      }

      # Insert the updated candidate
      {:ok, update_result} =
        BeneficialOwners.upsert_candidates_multi([updated_candidate], parent_candidate, report.id)

      {1, [updated_db_candidate]} = update_result.candidates

      # Verify a new candidate was created
      assert updated_db_candidate.id != inserted_candidate.id
      assert updated_db_candidate.shares == 7500
      assert updated_db_candidate.address_line_one == "789 New St"
      assert updated_db_candidate.address_city == "Brisbane"
      assert updated_db_candidate.address_state == "QLD"
      assert updated_db_candidate.address_postcode == "4000"

      # Verify there's still only one candidate in the database
      db_candidates =
        Gaia.BeneficialOwners.ReportCandidate
        |> where([rc], rc.report_id == ^report.id and rc.parent_id == ^parent_candidate.id)
        |> Repo.all()

      assert length(db_candidates) == 2
    end

    test "sums shares when upserting candidates with the same account name", %{
      company: _company,
      report: report
    } do
      # Create a parent candidate first
      {:ok, parent_candidate} =
        BeneficialOwners.create_report_candidate(%{
          report_id: report.id,
          account_name: "Parent Company",
          shares: 10_000,
          layer: 1,
          status: :done
        })

      # Create an initial candidate
      initial_candidate = %{
        "account_name" => "Summing Child",
        "shares" => "1000",
        "address_line_one" => "123 Main St",
        "address_city" => "Sydney",
        "address_state" => "NSW",
        "address_postcode" => "2000",
        "address_country" => "Australia"
      }

      # Insert the initial candidate
      {:ok, initial_result} =
        BeneficialOwners.upsert_candidates_multi([initial_candidate], parent_candidate, report.id)

      {1, [inserted_candidate]} = initial_result.candidates
      assert inserted_candidate.shares == 1000

      # Create multiple candidates with the same account name but different shares
      additional_candidates = [
        %{
          "account_name" => "Summing Child",
          "shares" => "2000",
          "address_line_one" => "123 Main St",
          "address_city" => "Sydney",
          "address_state" => "NSW",
          "address_postcode" => "2000",
          "address_country" => "Australia"
        },
        %{
          "account_name" => "Summing Child",
          "shares" => "3000",
          "address_line_one" => "123 Main St",
          "address_city" => "Sydney",
          "address_state" => "NSW",
          "address_postcode" => "2000",
          "address_country" => "Australia"
        }
      ]

      # Insert the additional candidates
      {:ok, update_result} =
        BeneficialOwners.upsert_candidates_multi(
          additional_candidates,
          parent_candidate,
          report.id
        )

      {2, updated_candidates} = update_result.candidates

      # Both candidates should have the same ID (they're the same record)
      assert Enum.all?(updated_candidates, fn c -> c.id == inserted_candidate.id end)

      # The last candidate should have the sum of all shares (1000 + 2000 + 3000 = 6000)
      last_candidate = List.last(updated_candidates)
      assert last_candidate.shares == 6000

      # Verify there's still only one candidate in the database
      db_candidates =
        Gaia.BeneficialOwners.ReportCandidate
        |> where(
          [rc],
          rc.report_id == ^report.id and rc.parent_id == ^parent_candidate.id and
            rc.account_name == "Summing Child"
        )
        |> Repo.all()

      assert length(db_candidates) == 1
      [db_candidate] = db_candidates
      assert db_candidate.shares == 6000
    end

    test "handles empty strings in candidate data", %{company: _company, report: report} do
      # Create a parent candidate first
      {:ok, parent_candidate} =
        BeneficialOwners.create_report_candidate(%{
          report_id: report.id,
          account_name: "Parent Company",
          shares: 10_000,
          layer: 1,
          status: :done
        })

      # Create a candidate with empty strings
      candidate_with_empty_strings = %{
        "account_name" => "Empty Fields Company",
        "shares" => "1000",
        "address_line_one" => "",
        "address_line_two" => "",
        "address_city" => "",
        "address_state" => "",
        "address_postcode" => "",
        "address_country" => ""
      }

      # Insert the candidate
      {:ok, result} =
        BeneficialOwners.upsert_candidates_multi(
          [candidate_with_empty_strings],
          parent_candidate,
          report.id
        )

      {1, [inserted_candidate]} = result.candidates

      # Verify empty strings were converted to nil
      assert inserted_candidate.account_name == "Empty Fields Company"
      assert inserted_candidate.shares == 1000
      assert is_nil(inserted_candidate.address_line_one)
      assert is_nil(inserted_candidate.address_line_two)
      assert is_nil(inserted_candidate.address_city)
      assert is_nil(inserted_candidate.address_state)
      assert is_nil(inserted_candidate.address_postcode)
      assert is_nil(inserted_candidate.address_country)
    end

    test "handles nominee and ASIC candidates", %{company: _company, report: report} do
      # Create a parent candidate first
      {:ok, parent_candidate} =
        BeneficialOwners.create_report_candidate(%{
          report_id: report.id,
          account_name: "Parent Company",
          shares: 10_000,
          layer: 1,
          status: :done
        })

      # Create a nominee contact
      nominee_contact =
        Repo.insert!(%Gaia.BeneficialOwners.NomineeContact{
          account_name: "Nominee Company",
          email: "<EMAIL>",
          contact_name: "Nominee Contact"
        })

      # Create a candidate with the same name as the nominee contact
      nominee_candidate = %{
        "account_name" => "Nominee Company",
        "shares" => "2000",
        "address_line_one" => "123 Nominee St",
        "address_city" => "Sydney",
        "address_state" => "NSW",
        "address_postcode" => "2000",
        "address_country" => "Australia"
      }

      # Insert the candidate
      {:ok, result} =
        BeneficialOwners.upsert_candidates_multi([nominee_candidate], parent_candidate, report.id)

      {1, [inserted_candidate]} = result.candidates

      # Verify the candidate was identified as a nominee
      assert inserted_candidate.type == :nominee
      assert inserted_candidate.nominee_contact_id == nominee_contact.id
    end
  end

  describe "create_report_candidates_for_uk/2" do
    setup [:base_setup, :report_setup, :uk_nominee_contacts_setup]

    test "creates report candidates for UK nominee accounts", %{
      company: company,
      report: report,
      uk_nominees: uk_nominees
    } do
      # Call the function
      result = BeneficialOwners.create_report_candidates_for_uk(company.id, report.id)

      # Verify the result
      assert {:ok, count} = result
      assert count > 0

      # Verify that candidates were created in the database
      candidates =
        Gaia.BeneficialOwners.ReportCandidate
        |> where([rc], rc.report_id == ^report.id)
        |> Repo.all()

      # Verify we have the expected number of candidates
      assert length(candidates) == count

      # Verify all candidates have the correct attributes
      for candidate <- candidates do
        # Find the corresponding nominee contact
        nominee = Enum.find(uk_nominees, &(&1.account_name == candidate.account_name))

        # Verify the candidate has the correct attributes
        assert candidate.report_id == report.id
        assert candidate.shares == 0
        assert candidate.layer == 1
        assert candidate.type == :nominee
        assert candidate.status == :pending

        # If we have a nominee contact, verify the nominee_contact_id and address fields
        if nominee do
          assert candidate.nominee_contact_id == nominee.id
          assert candidate.address_line_one == nominee.address_line_one
          assert candidate.address_city == nominee.address_city
          assert candidate.address_country == nominee.address_country
        end
      end
    end

    test "creates candidates for all UK nominees in the predefined list", %{
      company: company,
      report: report
    } do
      # The predefined list of UK nominees in the function
      uk_nominees = [
        "LAWSHARE NOMINEES LIMITED",
        "BARCLAYS DIRECT INVESTING NOMINEES LIMITED",
        "CHARLES STANLEY & CO. LIMITED",
        "CREDO CAPITAL",
        "EQUINITI GROUP PLC",
        "GHC NOMINEES LIMITED",
        "HSDL NOMINEES LIMITED",
        "HARGREAVES LANSDOWN NOMINEES LIMITED",
        "INTERACTIVE INVESTOR SERVICES NOMINEES LIMITED",
        "INTERACTIVE BROKERS (U.K.) NOMINEE LIMITED",
        "JIM NOMINEES LIMITED",
        "FERLIM NOMINEES LIMITED",
        "REDMAYNE (NOMINEES) LIMITED",
        "PEEL HUNT NOMINEES LIMITED",
        "TRADING 212 UK LIMITED",
        "WINTERFLOOD SECURITIES LIMITED"
      ]

      # Call the function
      {:ok, _count} = BeneficialOwners.create_report_candidates_for_uk(company.id, report.id)

      # Get the candidates from the database
      candidates =
        Gaia.BeneficialOwners.ReportCandidate
        |> where([rc], rc.report_id == ^report.id)
        |> Repo.all()

      # Verify that all UK nominees in the predefined list have a candidate
      for nominee_name <- uk_nominees do
        candidate = Enum.find(candidates, &(&1.account_name == nominee_name))
        assert not is_nil(candidate), "Expected to find a candidate for #{nominee_name}"
      end
    end
  end

  describe "update_report_candidate_status/2" do
    setup [:base_setup, :report_setup, :candidates_with_different_statuses_setup]

    test "updates parent and only pending children", %{
      report: _report,
      parent_candidate: parent,
      pending_child: pending_child,
      done_child: done_child,
      failed_child: failed_child
    } do
      # Call the function to update status to :done
      {:ok, result} = BeneficialOwners.update_report_candidate_status(parent.id, :done)

      # Verify the parent was updated
      updated_parent = Gaia.BeneficialOwners.get_report_candidate!(parent.id)
      assert updated_parent.status == :done

      # Verify only the pending child was updated
      updated_pending_child = Gaia.BeneficialOwners.get_report_candidate!(pending_child.id)
      updated_done_child = Gaia.BeneficialOwners.get_report_candidate!(done_child.id)
      updated_failed_child = Gaia.BeneficialOwners.get_report_candidate!(failed_child.id)

      assert updated_pending_child.status == :done
      # This should remain unchanged
      assert updated_done_child.status == :done
      # This should remain unchanged
      assert updated_failed_child.status == :failed

      # Verify the result contains the correct count of updated children
      assert result.update_children.count == 1
    end
  end

  describe "beneficial_owner_latest_holdings_by_contact/3" do
    setup [:base_setup, :holdings_setup]

    test "Ensure returns holdings for a contact within date range", %{
      contact: contact,
      start_date: start_date,
      end_date: end_date,
      report1: report1,
      report2: report2
    } do
      result =
        BeneficialOwners.beneficial_owner_latest_holdings_by_contact(
          contact,
          start_date,
          end_date
        )

      merged_holdings = BeneficialOwners.merge_beneficial_owner_holdings_by_account_name(result)

      [first_result, second_result] = merged_holdings

      assert first_result.date == report2.report_date
      assert first_result.balance == 3000
      assert first_result.id == "#{contact.id}-#{report2.report_date}"

      assert second_result.date == report1.report_date
      assert second_result.balance == 1500
      assert second_result.id == "#{contact.id}-#{report1.report_date}"
    end

    test "Ensure returns empty list when no holdings found for contact", %{
      start_date: start_date,
      end_date: end_date
    } do
      result =
        BeneficialOwners.beneficial_owner_latest_holdings_by_contact(
          contact(company_profile()),
          start_date,
          end_date
        )

      assert result == []
    end

    test "Ensure it filters by start_date correctly", %{
      contact: contact,
      end_date: end_date,
      report2: report2
    } do
      late_start_date = Date.add(report2.report_date, -1)

      result =
        BeneficialOwners.beneficial_owner_latest_holdings_by_contact(
          contact,
          late_start_date,
          end_date
        )

      merged_holdings = BeneficialOwners.merge_beneficial_owner_holdings_by_account_name(result)

      assert length(merged_holdings) == 1
      assert hd(merged_holdings).date == report2.report_date
    end

    test "Ensure filters by end_date correctly", %{
      contact: contact,
      start_date: start_date,
      report1: report1
    } do
      early_end_date = Date.add(report1.report_date, 30)

      result =
        BeneficialOwners.beneficial_owner_latest_holdings_by_contact(
          contact,
          start_date,
          early_end_date
        )

      merged_holdings = BeneficialOwners.merge_beneficial_owner_holdings_by_account_name(result)

      assert length(merged_holdings) == 1
      assert hd(merged_holdings).date == report1.report_date
    end

    test "Ensure aggregates shares correctly for same report date", %{
      contact: contact,
      start_date: start_date,
      end_date: end_date,
      company: company,
      account1: account1,
      report1: %{id: report_id, report_date: report_date}
    } do
      candidate_extra =
        Repo.insert!(%Gaia.BeneficialOwners.ReportCandidate{
          report_id: report_id,
          account_name: "Extra Candidate",
          shares: 500,
          layer: 1
        })

      Repo.insert!(%Gaia.BeneficialOwners.Holding{
        beneficial_owner_account_id: account1.id,
        report_id: report_id,
        company_profile_id: company.id,
        candidate_id: candidate_extra.id,
        shares: 500
      })

      holdings =
        BeneficialOwners.beneficial_owner_latest_holdings_by_contact(contact, start_date, end_date)

      merged_holdings = BeneficialOwners.merge_beneficial_owner_holdings_by_account_name(holdings)

      report1_result = Enum.find(merged_holdings, &(&1.date == report_date))

      # 1000 + 500 + 500 = 1500 shares (now expected as 2000 due to inserted extra)
      assert report1_result.balance == 2000
    end
  end

  defp base_setup(_) do
    [company: company_profile()]
  end

  defp report_setup(%{company: company}) do
    report =
      Repo.insert!(%Report{
        report_date: Faker.Date.backward(30),
        company_profile_id: company.id,
        is_user_uploaded: false
      })

    [report: report]
  end

  defp report_attrs_setup(%{company: company}) do
    attrs = %{
      report_date: Faker.Date.backward(30),
      company_profile_id: company.id,
      is_user_uploaded: false
    }

    [attrs: attrs]
  end

  defp uk_nominee_contacts_setup(%{company: _company, report: _report}) do
    # Create some UK nominee contacts that match the ones in the function
    uk_nominees = [
      %{
        account_name: "LAWSHARE NOMINEES LIMITED",
        email: "<EMAIL>",
        contact_name: "Lawshare Contact",
        address_line_one: "1 Lawshare Street",
        address_city: "Manchester",
        address_state: "Greater Manchester",
        address_postcode: "M1 1AA",
        address_country: "United Kingdom"
      },
      %{
        account_name: "BARCLAYS DIRECT INVESTING NOMINEES LIMITED",
        email: "<EMAIL>",
        contact_name: "Barclays Contact",
        address_line_one: "1 Churchill Place",
        address_city: "London",
        address_state: "London",
        address_postcode: "E14 5HP",
        address_country: "United Kingdom"
      },
      %{
        account_name: "INTERACTIVE INVESTOR SERVICES NOMINEES LIMITED",
        email: "<EMAIL>",
        contact_name: "Interactive Investor Contact",
        address_line_one: "Exchange Quay",
        address_city: "Manchester",
        address_state: "Greater Manchester",
        address_postcode: "M5 3EB",
        address_country: "United Kingdom"
      }
    ]

    # Insert the nominee contacts
    inserted_nominees =
      Enum.map(uk_nominees, fn nominee_attrs ->
        Repo.insert!(%Gaia.BeneficialOwners.NomineeContact{
          account_name: nominee_attrs.account_name,
          email: nominee_attrs.email,
          contact_name: nominee_attrs.contact_name,
          address_line_one: nominee_attrs.address_line_one,
          address_city: nominee_attrs.address_city,
          address_state: nominee_attrs.address_state,
          address_postcode: nominee_attrs.address_postcode,
          address_country: nominee_attrs.address_country
        })
      end)

    [uk_nominees: inserted_nominees]
  end

  defp candidates_with_different_statuses_setup(%{company: _company, report: report}) do
    # Create a parent candidate
    parent_candidate =
      Repo.insert!(%Gaia.BeneficialOwners.ReportCandidate{
        report_id: report.id,
        account_name: "Parent Company",
        shares: 10_000,
        layer: 1,
        status: :pending
      })

    # Create a child with status :pending
    pending_child =
      Repo.insert!(%Gaia.BeneficialOwners.ReportCandidate{
        report_id: report.id,
        account_name: "Pending Child",
        shares: 5_000,
        layer: 2,
        parent_id: parent_candidate.id,
        status: :pending
      })

    # Create a child with status :done
    done_child =
      Repo.insert!(%Gaia.BeneficialOwners.ReportCandidate{
        report_id: report.id,
        account_name: "Done Child",
        shares: 3_000,
        layer: 2,
        parent_id: parent_candidate.id,
        status: :done
      })

    # Create a child with status :failed
    failed_child =
      Repo.insert!(%Gaia.BeneficialOwners.ReportCandidate{
        report_id: report.id,
        account_name: "Failed Child",
        shares: 2_000,
        layer: 2,
        parent_id: parent_candidate.id,
        status: :failed
      })

    [
      parent_candidate: parent_candidate,
      pending_child: pending_child,
      done_child: done_child,
      failed_child: failed_child
    ]
  end

  defp holdings_setup(%{company: company}) do
    contact = contact(company)

    # Create beneficial owner accounts
    account1 =
      Repo.insert!(%Gaia.BeneficialOwners.Account{
        contact_id: contact.id,
        company_profile_id: company.id,
        account_name: "Account 1"
      })

    account2 =
      Repo.insert!(%Gaia.BeneficialOwners.Account{
        contact_id: contact.id,
        company_profile_id: company.id,
        account_name: "Account 2"
      })

    # Create reports with different dates
    start_date = ~D[2023-01-01]
    end_date = ~D[2023-12-31]

    report1 =
      Repo.insert!(%Gaia.BeneficialOwners.Report{
        company_profile_id: company.id,
        report_date: ~D[2023-03-01],
        type: :completed
      })

    report2 =
      Repo.insert!(%Gaia.BeneficialOwners.Report{
        company_profile_id: company.id,
        report_date: ~D[2023-09-01],
        type: :completed
      })

    # Create report candidates
    candidate1 =
      Repo.insert!(%Gaia.BeneficialOwners.ReportCandidate{
        report_id: report1.id,
        account_name: "Candidate 1",
        shares: 1000,
        layer: 1
      })

    candidate2 =
      Repo.insert!(%Gaia.BeneficialOwners.ReportCandidate{
        report_id: report1.id,
        account_name: "Candidate 2",
        shares: 500,
        layer: 1
      })

    candidate3 =
      Repo.insert!(%Gaia.BeneficialOwners.ReportCandidate{
        report_id: report2.id,
        account_name: "Candidate 3",
        shares: 2000,
        layer: 1
      })

    candidate4 =
      Repo.insert!(%Gaia.BeneficialOwners.ReportCandidate{
        report_id: report2.id,
        account_name: "Candidate 4",
        shares: 1000,
        layer: 1
      })

    # Create holdings
    Repo.insert!(%Gaia.BeneficialOwners.Holding{
      beneficial_owner_account_id: account1.id,
      report_id: report1.id,
      company_profile_id: company.id,
      candidate_id: candidate1.id,
      shares: 1000
    })

    Repo.insert!(%Gaia.BeneficialOwners.Holding{
      beneficial_owner_account_id: account2.id,
      report_id: report1.id,
      company_profile_id: company.id,
      candidate_id: candidate2.id,
      shares: 500
    })

    Repo.insert!(%Gaia.BeneficialOwners.Holding{
      beneficial_owner_account_id: account1.id,
      report_id: report2.id,
      company_profile_id: company.id,
      candidate_id: candidate3.id,
      shares: 2000
    })

    Repo.insert!(%Gaia.BeneficialOwners.Holding{
      beneficial_owner_account_id: account2.id,
      report_id: report2.id,
      company_profile_id: company.id,
      candidate_id: candidate4.id,
      shares: 1000
    })

    [
      contact: contact,
      account1: account1,
      account2: account2,
      report1: report1,
      report2: report2,
      candidate1: candidate1,
      start_date: start_date,
      end_date: end_date
    ]
  end
end
