import analytics from '@analytics';
import { ClockIcon, HandThumbUpIcon } from '@heroicons/react-v2/24/outline';
import { Typography } from '../../../index';

export const QuickSort: React.FC<{
  orders: {
    key: string;
    value: string;
  }[];
  setOrder: (order: string) => void;
}> = ({ orders, setOrder }) => {
  const isLatestSelected = orders[0].key === 'posted_at';
  const isPopularSelected = orders[0].key === 'engagement';

  const handleLatestClick = () => {
    if (!isLatestSelected) {
      analytics.track('hermes_deep_search', {
        feature: 'announcements',
        hubs_version: '2',
        sort: 'latest',
      });
      setOrder('posted_at');
    }
  };

  const handlePopularClick = () => {
    if (!isPopularSelected) {
      analytics.track('hermes_deep_search', {
        feature: 'announcements',
        hubs_version: '2',
        sort: 'most popular',
      });
      setOrder('engagement');
    }
  };

  return (
    <div className="flex w-full flex-col items-center justify-end gap-4 md:w-auto md:flex-row">
      <div className="hidden items-center gap-2 text-hubs-secondary md:flex">
        <Typography className="text-sm font-medium text-inherit">
          Sort by:
        </Typography>
      </div>
      <div
        aria-label="Sort announcements"
        className="flex w-full overflow-hidden rounded-lg border border-gray-100 md:w-auto"
        role="group"
      >
        <button
          aria-label="Sort by latest announcements"
          aria-pressed={isLatestSelected}
          className={`flex h-[48px] w-1/2 items-center justify-center gap-2 px-6 text-base transition-colors md:h-auto md:w-auto md:py-2.5 md:text-sm ${
            isLatestSelected
              ? 'bg-gray-800 text-white'
              : 'bg-white text-gray-700 hover:bg-gray-50'
          }`}
          title="Sort announcements by most recent"
          type="button"
          onClick={handleLatestClick}
        >
          <ClockIcon aria-hidden="true" className="h-5 w-5" />
          <span className="min-w-[60px] text-center">Latest</span>
        </button>
        <button
          aria-label="Sort by most popular announcements"
          aria-pressed={isPopularSelected}
          className={`flex h-[48px] w-1/2 items-center justify-center gap-2 px-6 text-base transition-colors md:h-auto md:w-auto md:py-2.5 md:text-sm ${
            isPopularSelected
              ? 'bg-gray-800 text-white'
              : 'bg-white text-gray-700 hover:bg-gray-50'
          }`}
          title="Sort announcements by engagement level"
          type="button"
          onClick={handlePopularClick}
        >
          <HandThumbUpIcon aria-hidden="true" className="h-5 w-5" />
          <span className="min-w-[60px] text-center">
            <span className="hidden md:inline">Most </span>
            <span className="md:lowercase">Popular</span>
          </span>
        </button>
      </div>
    </div>
  );
};
