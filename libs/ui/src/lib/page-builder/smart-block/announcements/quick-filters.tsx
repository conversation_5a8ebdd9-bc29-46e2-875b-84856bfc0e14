import React, { useRef, useState, useEffect } from 'react';
import analytics from '@analytics';
import {
  CurrencyDollarIcon,
  MegaphoneIcon,
  NewspaperIcon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  VideoCameraIcon,
} from '@heroicons/react-v2/24/outline';
import { CalendarIcon, StarIcon } from '@heroicons/react-v2/24/solid';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { useOnClickOutside } from 'usehooks-ts';
import { useGetAnnouncementTypesQuery } from '../../../index';
import {
  Button,
  Typography,
  QuickFilterType,
  QuickFilterWithLabel,
} from '../../../index';
import { QuickFiltersMobile } from './quick-filters-mobile';

type SetAppliedFiltersType = React.Dispatch<
  React.SetStateAction<QuickFilterType[]>
>;

const FilterDropdown: React.FC<{
  appliedFilters: QuickFilterType[];
  isUK: boolean;
  setAppliedFilters: SetAppliedFiltersType;
}> = ({ appliedFilters, isUK, setAppliedFilters }) => {
  const [isOpen, setIsOpen] = useState(false);
  const ref = useRef(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const { data, loading } = useGetAnnouncementTypesQuery();

  // No need for scroll handler with the simpler approach
  const [asxGroupOptions, setAsxGroupOptions] = useState<
    QuickFilterWithLabel[]
  >([]);
  const [asxSpecificOptions, setAsxSpecificOptions] = useState<
    QuickFilterWithLabel[]
  >([]);
  const [lseOptions, setLseOptions] = useState<QuickFilterWithLabel[]>([]);

  // Process announcement types from the backend when data is loaded
  useEffect(() => {
    if (data?.announcementTypes) {
      // Process ASX group options
      const groupOptions: QuickFilterWithLabel[] = [];
      const specificOptions: QuickFilterWithLabel[] = [];
      const lseOpts: QuickFilterWithLabel[] = [];

      // Process the data from the backend
      if (data.announcementTypes.list) {
        // Group by rectype
        const groupedByRectype: Record<
          string,
          Array<{
            isMarketSensitive?: boolean;
            isRecommended?: boolean;
            subtype: string;
            value: string;
          }>
        > = {};

        data.announcementTypes.list.forEach((type) => {
          const rectype = type.rectype;
          const subtypes = type.subtypes || [];

          // Initialize the group if it doesn't exist
          if (!groupedByRectype[rectype]) {
            groupedByRectype[rectype] = [];
          }

          // Add subtypes to the group
          subtypes.forEach((subtype) => {
            groupedByRectype[rectype].push({
              isMarketSensitive: subtype.isMarketSensitive,
              isRecommended: subtype.isRecommended,
              subtype: subtype.subtype,
              value: subtype.value,
            });
          });
        });

        // Create ASX group options for all rectypes from the backend
        // Periodic Reports
        if (groupedByRectype['Periodic Reports']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Periodic Reports',
            value: groupedByRectype['Periodic Reports'].map((st) => st.value),
          });
        }

        // Annual Reports (special case - not a direct rectype)
        const annualReportsValues = [
          ...(groupedByRectype['Periodic Reports'] || [])
            .filter((st) => st.value === '03001')
            .map((st) => st.value),
        ];
        if (annualReportsValues.length > 0) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Annual Reports',
            value: annualReportsValues,
          });
        }

        // Distribution Announcement (Dividend Notices)
        if (groupedByRectype['Distribution Announcement']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Dividend Notices',
            value: groupedByRectype['Distribution Announcement'].map(
              (st) => st.value
            ),
          });
        }

        // Security Holder Details
        if (
          groupedByRectype['Security Holder Details'] ||
          groupedByRectype['Shareholder Details']
        ) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Security Holder Notices',
            value: (
              groupedByRectype['Security Holder Details'] ||
              groupedByRectype['Shareholder Details'] ||
              []
            )
              .filter((st) => st.value.startsWith('0200'))
              .map((st) => st.value),
          });
        }

        // Company Administration
        if (groupedByRectype['Company Administration']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Company Administration',
            value: groupedByRectype['Company Administration'].map(
              (st) => st.value
            ),
          });
        }

        // Takeover Announcements
        if (groupedByRectype['Takeover Announcements']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Takeover Announcements',
            value: groupedByRectype['Takeover Announcements'].map(
              (st) => st.value
            ),
          });
        }

        // Quarterly Activities Reports
        if (groupedByRectype['Quarterly Activities Reports']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Quarterly Activities Reports',
            value: groupedByRectype['Quarterly Activities Reports'].map(
              (st) => st.value
            ),
          });
        }

        // Quarterly Cash Flow Reports
        if (groupedByRectype['Quarterly Cash Flow Reports']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Quarterly Cash Flow Reports',
            value: groupedByRectype['Quarterly Cash Flow Reports'].map(
              (st) => st.value
            ),
          });
        }

        // Asset Acquisition & Disposal
        if (groupedByRectype['Asset Acquisition & Disposal']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Asset Acquisition & Disposal',
            value: groupedByRectype['Asset Acquisition & Disposal'].map(
              (st) => st.value
            ),
          });
        }

        // Notice Of Meeting
        if (groupedByRectype['Notice Of Meeting']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Notice Of Meeting',
            value: groupedByRectype['Notice Of Meeting'].map((st) => st.value),
          });
        }

        // ASX Announcement
        if (groupedByRectype['ASX Announcement']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'ASX Announcement',
            value: groupedByRectype['ASX Announcement'].map((st) => st.value),
          });
        }

        // Progress Reports
        if (
          groupedByRectype['Progress Reports'] ||
          groupedByRectype['Progress Report']
        ) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Progress Reports',
            value: (
              groupedByRectype['Progress Reports'] ||
              groupedByRectype['Progress Report'] ||
              []
            ).map((st) => st.value),
          });
        }

        // Notice of Call (Contributing Shares)
        if (groupedByRectype['Notice of Call (Contributing Shares)']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Notice of Call (Contributing Shares)',
            value: groupedByRectype['Notice of Call (Contributing Shares)'].map(
              (st) => st.value
            ),
          });
        }

        // Chairman's Address
        if (groupedByRectype["Chairman's Address"]) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: "Chairman's Address",
            value: groupedByRectype["Chairman's Address"].map((st) => st.value),
          });
        }

        // Letter To Shareholders
        if (groupedByRectype['Letter To Shareholders']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Letter To Shareholders',
            value: groupedByRectype['Letter To Shareholders'].map(
              (st) => st.value
            ),
          });
        }

        // ASX Query - Consolidated with ASX Queries in specific options
        // We'll handle this in the specific options section

        // Structured Products (Warrants)
        if (
          groupedByRectype['Structured Products'] ||
          groupedByRectype['Warrants']
        ) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Structured Products',
            value: (
              groupedByRectype['Structured Products'] ||
              groupedByRectype['Warrants'] ||
              []
            ).map((st) => st.value),
          });
        }

        // Commitments Test Entity - Quarterly Reports is excluded as it's irrelevant

        // mFund
        if (groupedByRectype['mFund']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'mFund',
            value: groupedByRectype['mFund'].map((st) => st.value),
          });
        }

        // Other
        if (groupedByRectype['Other']) {
          groupOptions.push({
            group: 'groups' as const,
            key: 'category',
            label: 'Other',
            value: groupedByRectype['Other'].map((st) => st.value),
          });
        }

        // Create ASX specific options based on metadata from the backend
        // We'll use the metadata from the backend to determine which subtypes belong to which specific categories

        // Define specific categories and their corresponding subtypes
        interface SpecificCategory {
          rectypes: string[];
          subtypeCodes: string[];
        }

        const specificCategories: Record<string, SpecificCategory> = {
          AGMs: {
            rectypes: ['Notice Of Meeting'],
            subtypeCodes: ['08001'],
          },
          'ASX Queries': {
            rectypes: ['ASX Query'], // This will now be the only ASX Query option
            subtypeCodes: [], // All subtypes
          },
          'Admission to Official List': {
            rectypes: ['ASX Announcement'],
            subtypeCodes: ['09008'],
          },
          'Director Transactions': {
            rectypes: [
              'Security Holder Details',
              'Shareholder Details',
              'Company Administration',
            ],
            subtypeCodes: ['02007', '02008', '02009', '02010', '12001'],
          },
          'Issued Capital': {
            rectypes: ['Issued Capital'],
            subtypeCodes: [
              '06001',
              '06003',
              '06004',
              '06008',
              '06010',
              '06013',
              '06014',
              '06015',
              '06016',
              '06017',
              '06018',
              '06020',
              '06021',
              '06027',
            ],
          },
          'Share Buy-Backs': {
            rectypes: ['Issued Capital'],
            subtypeCodes: [
              '06011',
              '06012',
              '06019',
              '06022',
              '06023',
              '06024',
              '06025',
              '06026',
            ],
          },
          'Trading Halts': {
            rectypes: ['ASX Announcement'],
            subtypeCodes: ['09007', '09014', '09016'],
          },
        };

        // Process each specific category
        Object.entries(specificCategories).forEach(
          ([categoryLabel, config]) => {
            const { rectypes, subtypeCodes } = config;

            // Collect all subtypes from the specified rectypes
            const allSubtypes = rectypes.flatMap(
              (rectype) => groupedByRectype[rectype] || []
            );

            // Filter by subtype codes if specified
            const filteredSubtypes =
              subtypeCodes.length > 0
                ? allSubtypes.filter((st) => subtypeCodes.includes(st.value))
                : allSubtypes;

            // Extract values
            const values = filteredSubtypes.map((st) => st.value);

            // Add to specific options if we have values
            if (values.length > 0) {
              specificOptions.push({
                group: 'specific' as const,
                key: 'category',
                label: categoryLabel,
                value: values,
              });
            }
          }
        );

        // Create LSE options based on backend data
        // Define LSE categories and their corresponding backend rectypes
        interface LseCategory {
          backendRectypes: string[];
          value: string[];
        }

        const lseCategories: Record<string, LseCategory> = {
          'Annual Reports': {
            backendRectypes: ['Annual Financial Report'],
            value: ['FR'],
          },
          'Company Administration': {
            backendRectypes: [], // Always include
            value: ['ACS'],
          },
          'Dividend Notices': {
            backendRectypes: ['Dividend Declaration'],
            value: ['DIV'],
          },
          'Periodic Reports': {
            backendRectypes: [
              '1st Quarter Results',
              '3rd Quarter Results',
              'Half Year Results',
            ],
            value: ['IR'],
          },
          'Security Holder Notices': {
            backendRectypes: [], // Always include
            value: ['SHN'],
          },
        };

        // Process each LSE category
        Object.entries(lseCategories).forEach(([categoryLabel, config]) => {
          const { backendRectypes, value } = config;

          // Check if any of the backend rectypes exist in the data
          const shouldInclude =
            backendRectypes.length === 0 || // Always include if no backend rectypes specified
            backendRectypes.some((rectype) => groupedByRectype[rectype]);

          if (shouldInclude) {
            lseOpts.push({
              group: 'groups',
              key: 'category',
              label: categoryLabel,
              value,
            });
          }
        });
      }

      setAsxGroupOptions(groupOptions);
      setAsxSpecificOptions(specificOptions);
      setLseOptions(lseOpts);
    }
  }, [data]);

  // Select the appropriate options based on market, remove duplicates, and sort alphabetically
  const options = isUK
    ? lseOptions.sort((a, b) => a.label.localeCompare(b.label))
    : [...asxGroupOptions, ...asxSpecificOptions]
        // Remove duplicates by label (keeping the specific option when there's a duplicate)
        .filter((option, index, self) => {
          // Find the first occurrence of this label
          const firstIndex = self.findIndex((o) => o.label === option.label);

          // If this is the first occurrence, keep it
          if (firstIndex === index) return true;

          // If this is a duplicate, keep it only if it's a specific option and the first one is a group option
          return (
            option.group === 'specific' && self[firstIndex].group === 'groups'
          );
        })
        // Sort alphabetically
        .sort((a, b) => a.label.localeCompare(b.label));

  const selectedCategory = appliedFilters.find((f) => f.key === 'category');
  const selectedOption = selectedCategory
    ? options.find((option) => {
        const categoryValue = selectedCategory.value as string[];

        // Handle different types of values
        if (Array.isArray(categoryValue) && Array.isArray(option.value)) {
          // Compare arrays by converting to JSON strings
          return JSON.stringify(categoryValue) === JSON.stringify(option.value);
        } else if (
          typeof categoryValue === 'string' &&
          Array.isArray(option.value)
        ) {
          return option.value.includes(categoryValue);
        } else if (
          Array.isArray(categoryValue) &&
          typeof option.value === 'string'
        ) {
          return categoryValue.includes(option.value);
        } else {
          // Both are strings
          return categoryValue === option.value;
        }
      })?.label || 'All categories'
    : 'All categories';

  const handleClickOutside = () => {
    setIsOpen(false);
  };

  useOnClickOutside(ref, handleClickOutside);

  if (loading) {
    return (
      <div className="relative z-10 w-full md:w-auto">
        <Button
          className="w-full border-gray-100 md:w-auto"
          color={undefined}
          endIcon={<ChevronDownIcon className="h-5 w-5" />}
          size="md"
          startIcon={<MegaphoneIcon className="h-5 w-5" />}
          variant="secondary"
        >
          <span className="text-sm">Loading categories...</span>
        </Button>
      </div>
    );
  }

  return (
    <div ref={ref} className="relative z-10 w-full md:w-auto">
      <Button
        ref={buttonRef}
        className="w-full border-gray-100 md:w-auto"
        color={
          appliedFilters.some((f) => f.key === 'category')
            ? 'company'
            : undefined
        }
        endIcon={
          isOpen ? (
            <ChevronUpIcon className="h-5 w-5" />
          ) : (
            <ChevronDownIcon className="h-5 w-5" />
          )
        }
        size="md"
        startIcon={<MegaphoneIcon className="h-5 w-5" />}
        variant={
          appliedFilters.some((f) => f.key === 'category')
            ? 'primary'
            : 'secondary'
        }
        onClick={() => setIsOpen((prev) => !prev)}
      >
        <span className="text-sm">{selectedOption}</span>
      </Button>

      <div
        className={clsx(
          'absolute z-50 mt-2 w-full rounded-lg border bg-white shadow-xl ring-1 ring-black/5 md:min-w-[280px]',
          isOpen ? 'block' : 'hidden'
        )}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="max-h-[400px] overflow-y-auto bg-white py-1">
          <div
            className="cursor-pointer px-4 py-2 hover:bg-gray-100"
            onClick={() => {
              setAppliedFilters(
                appliedFilters.filter((f) => f.key !== 'category')
              );
              setIsOpen(false);
            }}
          >
            <Typography className="text-sm">All categories</Typography>
          </div>

          {/* All options in a single alphabetically sorted list */}
          <div className="mt-2 px-4 py-1">
            <Typography className="text-xs font-bold tracking-wider text-gray-600">
              ANNOUNCEMENT CATEGORIES
            </Typography>
          </div>

          {/* All options */}
          {options.map((option) => (
            <div
              key={option.label}
              className="cursor-pointer px-4 py-2 hover:bg-gray-100"
              onClick={() => {
                analytics.track('hermes_deep_search', {
                  feature: 'announcements',
                  filter: option.label.toLowerCase(),
                  hubs_version: '2',
                });
                setAppliedFilters([
                  ...appliedFilters.filter((f) => f.key !== 'category'),
                  { key: option.key, value: option.value },
                ]);
                setIsOpen(false);
              }}
            >
              <div className="flex items-center">
                {selectedCategory &&
                  JSON.stringify(selectedCategory.value) ===
                    JSON.stringify(option.value) && (
                    <span className="mr-2 text-company-primary">✓</span>
                  )}
                <Typography className="text-sm">{option.label}</Typography>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const FilterDatePicker: React.FC<{
  appliedFilters: QuickFilterType[];
  setAppliedFilters: SetAppliedFiltersType;
}> = ({ appliedFilters, setAppliedFilters }) => {
  const [rangePickerOpen, setRangePickerOpen] = useState(false);
  type MaybeDate = string | boolean | Date | undefined;
  const [startDate, setStartDate] = useState<MaybeDate>('');
  const [endDate, setEndDate] = useState<MaybeDate>('');

  const [selectedStartDate, setSelectedStartDate] = useState<MaybeDate>('');
  const [selectedEndDate, setSelectedEndDate] = useState<MaybeDate>('');

  const selectedText = () => {
    const startDisplay = startDate
      ? dayjs(startDate as string).format('DD/MM/YY')
      : '';
    const endDisplay = endDate
      ? dayjs(endDate as string).format('DD/MM/YY')
      : '';
    if (startDate || endDate) {
      return `${startDisplay} - ${endDisplay}`;
    }
    return 'Date range';
  };

  const today = dayjs().format('YYYY-MM-DD');

  React.useEffect(() => {
    if (!appliedFilters) return;
    const start = appliedFilters.find((sf) => sf.key === 'startDate')
      ?.value as Date;
    const end = appliedFilters.find((sf) => sf.key === 'endDate')
      ?.value as Date;

    setStartDate(start);
    setEndDate(end);
  }, [appliedFilters]);

  React.useEffect(() => {
    setSelectedStartDate(startDate ?? '');
  }, [startDate]);

  React.useEffect(() => {
    setSelectedEndDate(endDate ?? '');
  }, [endDate]);

  const clearDates = () => {
    setAppliedFilters([
      ...appliedFilters.filter(
        (sf) => !['startDate', 'endDate'].includes(sf.key)
      ),
    ]);
    setRangePickerOpen(false);
  };

  const ref = useRef(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // No need for scroll handler with the simpler approach

  const handleClickOutside = () => {
    setRangePickerOpen(false);
  };
  useOnClickOutside(ref, handleClickOutside);

  return (
    <div ref={ref} className="relative z-20 w-full md:w-auto">
      <Button
        ref={buttonRef}
        className="w-full border-gray-100 md:w-auto"
        color={
          appliedFilters.some((f) => f.key === 'startDate')
            ? 'company'
            : undefined
        }
        size="md"
        startIcon={<CalendarIcon className="h-5 w-5" />}
        variant={
          appliedFilters.some((f) => f.key === 'startDate')
            ? 'primary'
            : 'secondary'
        }
        onClick={() => setRangePickerOpen((prev) => !prev)}
      >
        <span className="text-sm">{selectedText()}</span>
      </Button>

      <div
        className={clsx(
          'absolute z-[100] mt-5 w-full rounded-lg border bg-hubs-background px-5 py-4 shadow-lg md:w-auto md:min-w-[400px]',
          rangePickerOpen ? 'block' : 'hidden'
        )}
      >
        <div className="flex w-full flex-col gap-3">
          <div className="flex w-full items-center justify-between">
            <Typography className="text-hubs-secondary">Date range</Typography>
            <XMarkIcon
              className="h-4 w-4 cursor-pointer"
              onClick={() => setRangePickerOpen(false)}
            />
          </div>
          <div className="flex w-full flex-col justify-between gap-4 md:flex-row md:items-center">
            <input
              className="input flex-1 shrink-0 rounded-md border border-secondary-grey-light"
              max={(selectedEndDate ? selectedEndDate : today) as string}
              min="2014-01-01"
              name="start"
              type="date"
              value={selectedStartDate as string}
              onChange={(e) => setSelectedStartDate(e.target.value)}
            />
            <input
              className="input flex-1 shrink-0 rounded-md border border-secondary-grey-light"
              max={today}
              min={
                (selectedStartDate ? selectedStartDate : '2014-01-01') as string
              }
              name="end"
              type="date"
              value={selectedEndDate as string}
              onChange={(e) => setSelectedEndDate(e.target.value)}
            />
          </div>
          <div className="w-full">
            <span
              className="cursor-pointer text-hubs-secondary underline"
              onClick={() => clearDates()}
            >
              Clear dates
            </span>
          </div>
          <div className="w-full">
            <Button
              fullWidth
              disabled={!(selectedStartDate && selectedEndDate)}
              size="md"
              onClick={() => {
                analytics.track('hermes_deep_search', {
                  feature: 'announcements',
                  filter: 'date range',
                  hubs_version: '2',
                });
                setAppliedFilters([
                  ...appliedFilters.filter(
                    (sf) => !['startDate', 'endDate'].includes(sf.key)
                  ),
                  { key: 'startDate', value: selectedStartDate as string },
                  { key: 'endDate', value: selectedEndDate as string },
                ]);
                setRangePickerOpen(false);
              }}
            >
              Apply date range
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export const QuickFilters: React.FC<{
  appliedFilters: QuickFilterType[];
  isFeatured: boolean;
  isUK: boolean;
  setAppliedFilters: SetAppliedFiltersType;
  tags?: string[];
}> = ({
  appliedFilters,
  isFeatured = false,
  isUK,
  setAppliedFilters,
  tags,
}) => {
  const FilterButton: React.FC<{
    Icon: React.ForwardRefExoticComponent<
      Omit<React.SVGProps<SVGSVGElement>, 'ref'> & {
        title?: string | undefined;
        titleId?: string | undefined;
      } & React.RefAttributes<SVGSVGElement>
    >;
    filter: QuickFilterWithLabel;
  }> = ({ Icon, filter }) => {
    const applied =
      filter.key === 'news_publishers'
        ? appliedFilters.some(
            (f) =>
              f.key === 'news_publishers' &&
              (f.value as string).includes(filter.label)
          )
        : appliedFilters.some((f) => f.key === filter.key);
    return (
      <Button
        className="border-gray-100"
        color={applied ? 'company' : undefined}
        size="md"
        startIcon={<Icon className="h-5 w-5 shrink-0" />}
        variant={applied ? 'primary' : 'secondary'}
        onClick={() => {
          analytics.track('hermes_deep_search', {
            feature: 'announcements',
            filter: filter.label.toLowerCase(),
            hubs_version: '2',
          });
          if (mobileFiltersOpen) setMobileFiltersOpen(false);
          setAppliedFilters([
            ...appliedFilters.filter((sf) => sf.key !== filter.key),
            ...(applied
              ? []
              : [
                  {
                    key: filter.key,
                    value: filter.value,
                  },
                ]),
          ]);
        }}
      >
        <span className="text-sm">{filter?.label ?? ''}</span>
      </Button>
    );
  };
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);

  const filtersContent = ({ isMobile = false } = {}) => (
    <>
      <div className="flex w-full flex-col gap-3 md:w-auto md:flex-row">
        {isFeatured && (!tags || !tags.length) ? (
          <FilterButton
            Icon={StarIcon}
            filter={{
              key: 'featured',
              label: 'Featured',
              value: 'true',
            }}
          />
        ) : null}
        {/* Show category filter only for ASX or LSE companies */}
        {(isUK || !isUK) && (!tags || !tags.length) ? (
          <>
            {/* Desktop view - normal order */}
            {!isMobile && (
              <>
                {/* Show category filter for both ASX and LSE */}
                <FilterDropdown
                  appliedFilters={appliedFilters}
                  isUK={isUK}
                  setAppliedFilters={setAppliedFilters}
                />

                {/* Show price sensitive filter only for ASX */}
                {!isUK && (
                  <FilterButton
                    Icon={CurrencyDollarIcon}
                    filter={{
                      key: 'price_sensitive',
                      label: 'Price sensitive',
                      value: 'true',
                    }}
                  />
                )}

                {/* Always show video filter in both modes */}
                <FilterButton
                  Icon={VideoCameraIcon}
                  filter={{
                    key: 'video',
                    label: 'With Video',
                    value: 'true',
                  }}
                />
              </>
            )}

            {/* Mobile view - reordered with category at the bottom */}
            {isMobile && (
              <>
                {/* Show price sensitive filter only for ASX */}
                {!isUK && (
                  <FilterButton
                    Icon={CurrencyDollarIcon}
                    filter={{
                      key: 'price_sensitive',
                      label: 'Price sensitive',
                      value: 'true',
                    }}
                  />
                )}

                {/* Always show video filter in both modes */}
                <FilterButton
                  Icon={VideoCameraIcon}
                  filter={{
                    key: 'video',
                    label: 'With Video',
                    value: 'true',
                  }}
                />

                {/* Date picker moved up in mobile view */}
                <FilterDatePicker
                  appliedFilters={appliedFilters}
                  setAppliedFilters={setAppliedFilters}
                />

                {/* Show category filter for both ASX and LSE - moved to bottom */}
                <FilterDropdown
                  appliedFilters={appliedFilters}
                  isUK={isUK}
                  setAppliedFilters={setAppliedFilters}
                />
              </>
            )}

            {/* Show news publisher filters only for LSE */}
            {isUK && (
              <>
                <FilterButton
                  Icon={NewspaperIcon}
                  filter={{
                    key: 'news_publishers',
                    label: 'RNS',
                    value: 'RNS',
                  }}
                />
                <FilterButton
                  Icon={NewspaperIcon}
                  filter={{
                    key: 'news_publishers',
                    label: 'Reach',
                    value: 'Reach',
                  }}
                />
              </>
            )}
          </>
        ) : null}
      </div>
      {/* Date picker for desktop view only */}
      {!isMobile && (
        <FilterDatePicker
          appliedFilters={appliedFilters}
          setAppliedFilters={setAppliedFilters}
        />
      )}
    </>
  );
  return (
    <div className="relative z-10 flex w-full items-center justify-start md:w-auto">
      <div className="hidden w-auto grow gap-3 md:flex">
        {filtersContent({ isMobile: false })}
      </div>
      <QuickFiltersMobile
        open={mobileFiltersOpen}
        setOpen={setMobileFiltersOpen}
      >
        {filtersContent({ isMobile: true })}
      </QuickFiltersMobile>
    </div>
  );
};

export default QuickFilters;
