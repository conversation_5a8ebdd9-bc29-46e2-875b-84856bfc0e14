import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import analytics from '@analytics';
import {
  ApolloClient,
  NetworkStatus,
  NormalizedCacheObject,
} from '@apollo/client';
import {
  ChatIcon,
  ChevronRightIcon,
  CurrencyDollarIcon,
  SearchIcon,
  ThumbUpIcon,
  MenuAlt2Icon,
  VideoCameraIcon,
  DocumentTextIcon,
} from '@heroicons/react/outline';
import { XMarkIcon } from '@heroicons/react-v2/24/outline';
import dayjs from 'dayjs';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useAsyncDebounce } from 'react-table';
import {
  Select,
  Skeleton,
  Typography,
  Tooltip,
  Pagination,
  calculateStartCursor,
  BadgeV2,
  FilterInput,
  StartCursor,
  useMediaAnnouncementsQuery,
  useSearchAnnouncementAiQuery,
  BlockHeading,
  Button,
  useTotalFeaturedAnnouncementsLazyQuery,
  useGetAnnouncementTypesQuery,
} from '../../../index';
import { getRectype } from './announcement-type-helper';
// import { useCurrentCompany } from '../../../../../../../apps/hermes/contexts/current-company-context';
// import { Toggle } from '../../../toggle';
import { PulseLoading } from './pulse-loading';
import { QuickFilters } from './quick-filters';
import { QuickSort } from './quick-sort';

export type QuickFilterType = {
  group?: 'groups' | 'specific';
  key: string;
  value: string | boolean | Date | string[];
};
export type QuickFilterWithLabel = QuickFilterType & { label: string };

interface Props {
  client?: ApolloClient<NormalizedCacheObject>;
  featuresEnabled: (string | null)[];
  heading?: string;
  hub: string;
  isUK: boolean;
  listingKey: string;
  logoUrl: string;
  marketKey: string;
  tags?: string[];
  translate: (key: string) => string;
}

export const AnnouncementsBlock: React.ComponentType<Props> = ({
  client,
  featuresEnabled,
  heading,
  hub,
  isUK,
  listingKey,
  logoUrl,
  marketKey,
  tags,
  translate,
}) => {
  const router = useRouter();
  const { query = {} } = router || {};

  // Safely extract query parameters with fallbacks
  const order = query.announcementOrder;
  const page = query.announcementPage;
  const rows = query.announcementRows;

  const [totalCount, setTotalCount] = useState(0);

  const [orders, setOrders] = useState([{ key: 'posted_at', value: 'desc' }]);
  const [startCursor, setStartCursor] = useState(StartCursor);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchPhrase, setSearchPhrase] = useState('');
  const [searchFieldValue, setSearchFieldValue] = useState(searchPhrase);

  // Determine if we should use deep search based on whether there's a search phrase
  const useDeepSearch = !!searchPhrase;

  const onChange = useAsyncDebounce((value) => {
    setSearchPhrase(value);
  }, 800);

  const originalFilters = useMemo(
    () => (tags?.length ? [{ key: 'tags', value: tags.join(',') }] : []),
    [tags]
  );

  const [appliedFilters, setAppliedFilters] =
    useState<QuickFilterType[]>(originalFilters);

  // Fetch announcement types from the backend
  const { data: announcementTypesData } = useGetAnnouncementTypesQuery();

  // These functions are kept for reference but not currently used

  // Mapping of subtypes to their names
  const subtypeNames: Record<string, string> = {
    // Security Holder Notices - Detailed
    '02001': 'Becoming a substantial holder',
    '02002': 'Change in substantial holding',
    '02003': 'Ceasing to be a substantial holder',
    '02004': 'Beneficial ownership',
    '02005': 'Takeover update – Section 671B(C)',
    '02006': 'Security holder details – Other',
    // Director Transactions - Detailed
    '02007': 'Section 205G Notice',
    '02008': "Initial Director's Interest",
    '02009': "Change of Director's Interest",
    '02010': "Final Director's Interest",
    // Annual Reports
    '03001': 'Annual Report',
    // Periodic Reports
    '03003': 'Preliminary Final Report',
    '03004': 'Half Yearly Report',
    '03009': 'Half Year Audit Review',
    '03010': "Half Year Director's Statement",
    '03011': 'Annual Financial Report',
    '03012': "Annual Director's Report",
    '03013': 'Annual Audit Review',
    '03015': 'Periodic Reports - Other',
    '03016': 'Quarterly Activities Report',
    '03017': 'Annual Report - Trust',
    '03018': 'Quarterly Cash Flow Report',
    '03019': 'Half Year Accounts',
    '03020': 'Annual Report - CSP',
    '03026': 'Company Presentation',
    '04001': 'ASX Debt Listing Annual Report',
    '04002': 'ASX Debt Listing Half Year Report',
    '04003': 'ASX Debt Listing Quarterly Report',
    '04004': 'ASX Debt Listing Periodic Reports - Other',
    '05001': 'AQUA Annual Report',
    '05002': 'AQUA Half Year Report',
    '05003': 'AQUA Quarterly Report',
    '05004': 'AQUA Periodic Reports - Other',
    // Capital Raises & Escrow
    '06001': 'Renounceable Issue',
    '06003': 'Placement',
    '06004': 'Issues to the Public',
    '06008': 'Non-Renounceable Issue',
    '06010': 'Disclosure Document',
    '06011': 'On-Market Buy-Back',
    '06012': 'Daily Buy-Back Notice',
    '06013': 'Appendix 3B',
    '06014': 'ASX BookBuild',
    '06015': 'ASX BookBuild',
    '06016': 'ASX BookBuild',
    '06017': 'Security Purchase Plan',
    '06018': 'Cleansing Notice',
    '06019': 'Off-Market Buy-Back',
    '06020': 'Appendix 2A',
    '06021': 'Appendix 3G',
    '06022': 'Final Buy-Back Notice',
    '06023': 'Employee Scheme Buy-Back',
    '06024': 'Equal Access Scheme',
    '06025': 'Selective Buy-Back',
    '06026': 'Other Buy-Back',
    '06027': 'Appendix 3H',
    // Dividend Notices
    '10001': 'Dividend Rate',
    '10002': 'Dividend Pay Date',
    '10003': 'Dividend - Other',
    '10004': 'Interest Record Date',
    '10005': 'Interest Pay Date',
    '10006': 'Interest Rate',
    '10007': 'Interest - Other',
    '10008': 'Distribution Rate',
    '10009': 'Distribution Pay Date',
    '10010': 'Distribution - Other',
    '12001': 'Director Appointment/Resignation',
    '19001': 'Scheme Annual Report',
    '19002': 'Scheme Half Year Report',
    '19003': 'Scheme Quarterly Report',
    '19004': 'Scheme Periodic Reports - Other',
    '19005': 'Warrant Annual Report',
    '19006': 'Warrant Half Year Report',
    '19007': 'Warrant Quarterly Report',
    '19008': 'Warrant Periodic Reports - Other',
    '19009': 'Periodic Reports - MIS',
    // Add more mappings as needed
  };

  // Helper function to get rectype for the pill
  const getRecTypeLabel = (
    subtypes: (string | null)[],
    isUK: boolean
  ): string => {
    // Filter out null values
    const validSubtypes = subtypes.filter(
      (subtype): subtype is string => subtype !== null
    );

    if (validSubtypes.length === 0) {
      return 'Other';
    }

    // If we have announcement types data from the backend, use it
    if (announcementTypesData?.announcementTypes?.list) {
      // Use our helper function to get the rectype
      return getRectype(
        validSubtypes,
        announcementTypesData.announcementTypes.list
      );
    }

    // Fallback to the old implementation if we don't have backend data
    // For LSE companies
    if (isUK) {
      const lseCategories: Record<string, string> = {
        ACS: 'Company Administration',
        DIV: 'Dividend Notices',
        FR: 'Annual Reports',
        IR: 'Periodic Reports',
        SHN: 'Security Holder Notices',
      };

      // Find matching subtypes
      const matchedCategories = validSubtypes
        .map((subtype) => lseCategories[subtype])
        .filter(Boolean);

      if (matchedCategories.length > 0) {
        // Return the first category
        return matchedCategories[0];
      }

      return 'Other';
    }

    // For ASX companies, use the subtype name if available
    const matchedSubtypes = validSubtypes
      .map((subtype) => subtypeNames[subtype])
      .filter(Boolean);

    if (matchedSubtypes.length > 0) {
      return matchedSubtypes[0];
    }

    return 'Other';
  };

  const getFilters = useCallback(() => {
    const filters = [
      ...appliedFilters
        .map((filter) => {
          if (filter.key === 'startDate') {
            return filter.value
              ? {
                  key: 'posted_at_greater_than',
                  value: dayjs(filter.value as Date).toISOString(),
                }
              : null;
          }
          if (filter.key === 'endDate') {
            return filter.value
              ? {
                  key: 'posted_at_less_than',
                  value: dayjs(filter.value as Date)
                    .endOf('day')
                    .toISOString(),
                }
              : null;
          }
          // Transform category filter to subtypes filter for backend compatibility
          if (filter.key === 'category') {
            return {
              key: 'subtypes',
              value: Array.isArray(filter.value)
                ? filter.value.join(',')
                : filter.value,
            };
          }
          return filter;
        })
        .filter((f) => !!f && f.key !== 'tags'),
      { key: 'market_key', value: marketKey },
      { key: 'ticker', value: listingKey },
      { key: 'tags', value: (tags || []).join(',') },
    ];

    if (searchPhrase) {
      filters.push({ key: 'search', value: searchPhrase });
    }

    return filters as FilterInput[];
  }, [appliedFilters, marketKey, listingKey, searchPhrase, tags]);

  const aiQueryResult = useSearchAnnouncementAiQuery({
    skip: !searchPhrase, // Only run query when there's a search phrase
    variables: {
      annTypes:
        (appliedFilters.find((filter) => filter.key === 'category')
          ?.value as string[]) || undefined,
      dateEnd:
        (appliedFilters.find((filter) => filter.key === 'endDate')
          ?.value as string) || undefined,
      dateStart:
        (appliedFilters.find((filter) => filter.key === 'startDate')
          ?.value as string) || undefined,
      filterPriceSensitive:
        appliedFilters.find((filter) => filter.key === 'price_sensitive')
          ?.value === 'true'
          ? true
          : undefined,
      filterVideo:
        appliedFilters.find((filter) => filter.key === 'video')?.value ===
        'true'
          ? true
          : undefined,
      question: searchPhrase,
    },
  });

  // Modify the existing data/loading handling to account for AI mode, check if result is null or undefined first
  const aiQueryResultData = aiQueryResult.data?.aiRetrievedAnnouncements
    ?.documents
    ? aiQueryResult?.data?.aiRetrievedAnnouncements?.documents
    : null;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const documents = aiQueryResultData
    ? [...aiQueryResultData].sort((a, b) =>
        a && b
          ? new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime()
          : 0
      )
    : [];

  const { data, loading, networkStatus } = useMediaAnnouncementsQuery({
    client,
    fetchPolicy: 'no-cache',
    variables: {
      after: startCursor,
      first: rowsPerPage,
      hub,
      options: {
        filters: getFilters(),
        orders,
      },
    },
  });

  const [fetchTotal] = useTotalFeaturedAnnouncementsLazyQuery({
    client,
    fetchPolicy: 'no-cache',
    variables: { hub },
  });

  useEffect(() => {
    // Only make this call if the feature is enabled, let's not make unnecessary calls
    if (featuresEnabled.includes('featured_announcements')) {
      fetchTotal().then((result) => {
        if (result?.data?.totalFeaturedAnnouncements || 0 >= 1) {
          setAppliedFilters((prev) => [
            ...prev,
            { key: 'featured', value: 'true' },
          ]);
        }
      });
    }
    // This effect should only run once
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const showLanguages = featuresEnabled.includes(
    'german_announcement_translations'
  );

  const rowSelectRef = useRef<HTMLTableElement>(null);

  // Safely extract host from query and create queryWithoutHost
  const { host, ...queryWithoutHost } = query || {};

  // Safely get pathname
  const pathname =
    router && router.pathname
      ? router.pathname.replace('/_companies/[host]', '/')
      : '/';

  useEffect(() => {
    // Only process query parameters if router is ready
    if (!router || !router.isReady) return;

    try {
      // rows per page
      if (rows) {
        const per = rows as string;
        const intPer = parseInt(per);
        if (
          !isNaN(intPer) &&
          [10, 20, 50].includes(intPer) &&
          intPer !== rowsPerPage
        ) {
          setRowsPerPage(intPer);
        }
      }

      // page number
      if (page) {
        const p = page as string;
        const intP = parseInt(p);
        if (!isNaN(intP) && intP !== currentPage) {
          setCurrentPage(intP);
        }
      }

      // order
      if (order) {
        const o = order as string;
        if (
          ['posted_at', 'questions', 'likes', 'engagement'].includes(o) &&
          o !== orders[0].key
        ) {
          setOrders([{ key: o, value: 'desc' }]);
        }
      }
    } catch (error) {
      // If there's any error processing query params, use defaults
      // Silent fail and use defaults
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router, router?.isReady, query]);

  const setRows = (rows: number) => {
    // Make sure router is ready before pushing
    if (!router.isReady) {
      setRowsPerPage(rows);
      return;
    }

    try {
      router
        .push(
          {
            hash: 'announcements_block',
            pathname,
            query: {
              ...queryWithoutHost,
              ...{ announcementPage: 1, announcementRows: rows },
            },
          },
          undefined,
          { shallow: true }
        )
        .then(() => {
          if (rows < rowsPerPage) {
            rowSelectRef.current?.scrollIntoView();
          }
        })
        .catch(() => {
          // Fallback: update the state directly if router push fails
          setRowsPerPage(rows);
          setCurrentPage(1);
        });
    } catch (error) {
      // Fallback: update the state directly if router push fails
      setRowsPerPage(rows);
      setCurrentPage(1);
    }
  };

  const handlePageChange = (page: number) => {
    // Make sure router is ready before pushing
    if (!router.isReady) {
      setCurrentPage(page);
      return;
    }

    try {
      router
        .push(
          {
            hash: 'announcements_block',
            pathname,
            query: { ...queryWithoutHost, ...{ announcementPage: page } },
          },
          undefined,
          { shallow: true }
        )
        .then(() => {
          rowSelectRef.current?.scrollIntoView();
        })
        .catch(() => {
          // Fallback: update the state directly if router push fails
          setCurrentPage(page);
        });
    } catch (error) {
      // Fallback: update the state directly if router push fails
      setCurrentPage(page);
    }
  };

  const setOrder = (order: string) => {
    // Make sure router is ready before pushing
    if (!router.isReady) return;

    try {
      router.push(
        {
          hash: 'announcements_block',
          pathname,
          query: { ...queryWithoutHost, ...{ announcementOrder: order } },
        },
        undefined,
        { shallow: true }
      );
    } catch (error) {
      // Fallback: update the state directly if router push fails
      setOrders([{ key: order, value: 'desc' }]);
    }
  };

  useEffect(() => {
    calculateStartCursor(currentPage, rowsPerPage, setStartCursor);
  }, [currentPage, rowsPerPage, setStartCursor]);

  // Set page to be last page if changing rows per page and the currentPage is out of range
  useEffect(() => {
    if (
      !!data?.mediaAnnouncements?.total &&
      currentPage > data?.mediaAnnouncements?.total / rowsPerPage
    ) {
      setCurrentPage(Math.ceil(data?.mediaAnnouncements?.total / rowsPerPage));
    }
  }, [
    currentPage,
    data?.mediaAnnouncements?.total,
    rowsPerPage,
    setCurrentPage,
  ]);

  useEffect(() => {
    if (networkStatus === NetworkStatus.ready) {
      setTotalCount(
        useDeepSearch
          ? documents.length ?? 0
          : data?.mediaAnnouncements?.total ?? 0
      );
      analytics.track('hermes_deep_search', {
        deep_search: useDeepSearch,
        feature: 'announcements',
        filter: 'search',
        hubs_version: '2',
        search_terms: searchPhrase,
        total_results: useDeepSearch
          ? documents.length ?? 0
          : data?.mediaAnnouncements?.total ?? 0,
      });
    }
  }, [data, networkStatus, documents, useDeepSearch, searchPhrase]);

  function renderLoadingState() {
    const loadingMessages = [
      'Quickly reading years of announcements...',
      'Searching for keyword matches...',
      'Ranking results by relevance...',
      'Almost finished...',
    ];

    return (
      <PulseLoading
        logoUrl={logoUrl}
        messages={loadingMessages}
        primaryColor="var(--company-primary)" // Use the company's primary color
      />
    );
  }

  function renderEmptyState() {
    return (
      <div>
        {searchPhrase ? (
          // No results found for search query
          <div className="flex flex-col gap-12">
            <div className="flex flex-col items-center justify-start gap-12 py-12">
              <div className="flex flex-col items-center justify-center gap-6 text-center">
                <Typography align="center" variant="heading-5">
                  {`No ${translate(
                    'announcements.lowercase'
                  )} found for ${marketKey}:${listingKey}.`}
                </Typography>
                <Typography variant="body-small">
                  Try using different keywords or adjusting your filters.
                </Typography>
                <Button
                  size="sm"
                  startIcon={<XMarkIcon className="h-5 w-5" />}
                  variant="secondary"
                  onClick={() => {
                    setAppliedFilters(originalFilters);
                    setSearchPhrase('');
                    setSearchFieldValue('');
                  }}
                >
                  Clear search & filters
                </Button>
              </div>
            </div>
            <div className="flex flex-col items-center gap-6">
              <Typography align="center" variant="body-small">
                Need inspiration? Use one of our suggested topics
              </Typography>
              <div className="items-center">
                <div className="inline-block rounded-full bg-gray-100 px-4 py-1 text-sm font-normal leading-tight">
                  Who is the current Managing Director?
                </div>
              </div>
              <div className="items-center">
                <div className="inline-block rounded-full bg-gray-100 px-4 py-1 text-sm font-normal leading-tight">
                  Who are the Board of Directors?
                </div>
              </div>
              <div className="items-center">
                <div className="inline-block rounded-full bg-gray-100 px-4 py-1 text-sm font-normal leading-tight">
                  What is the status of current projects?
                </div>
              </div>
            </div>
          </div>
        ) : (
          // No announcements found (without search)
          <div className="flex flex-col items-center justify-start gap-12 py-12">
            <div className="flex flex-col items-center justify-center gap-6 text-center">
              <Typography align="center" variant="heading-5">
                {`No ${translate(
                  'announcements.lowercase'
                )} found for ${marketKey}:${listingKey}.`}
              </Typography>
              <Typography variant="body-small">
                Try using different keywords or adjusting your filters.
              </Typography>
            </div>
          </div>
        )}
      </div>
    );
  }

  function renderList() {
    if (loading) {
      return (
        <>
          {Array.from(new Array(rowsPerPage).keys()).map((item) => (
            <Skeleton key={item} loading height={60} variant="rect" />
          ))}
        </>
      );
    }

    // Deep AI search mode
    if (useDeepSearch && documents) {
      if (aiQueryResult?.loading) {
        return <div className="py-4">{renderLoadingState()}</div>;
      } else if (documents.length === 0) {
        return <div className="py-4">{renderEmptyState()}</div>;
      } else {
        return documents.map((doc) => {
          if (doc) {
            const href = `/announcements/${doc?.mediaAnnouncementId}`;
            return (
              <Link
                key={doc?.mediaAnnouncementId}
                className="block border-b py-4"
                href={href}
                onClick={() => {
                  analytics.track('hermes_deep_search', {
                    announcement_id: doc?.mediaAnnouncementId,
                    deep_search: useDeepSearch,
                    feature: 'announcements',
                    filter: 'ann_click',
                    hubs_version: '2',
                    search_terms: searchPhrase,
                  });
                }}
              >
                <div className="flex flex-col items-start gap-1.5">
                  <div className="flex w-full flex-row items-center justify-between">
                    <div className="flex flex-col items-start gap-2">
                      <Typography
                        className="flex gap-2 text-hubs-primary"
                        component="div"
                        variant="body-small"
                      >
                        <div>
                          {dayjs(doc.postedAt).format('DD/MM/YYYY • hh:mm A')}
                        </div>
                      </Typography>
                      <Typography
                        className="line-clamp-2 text-company-primary"
                        variant="button"
                      >
                        {doc.title}
                      </Typography>
                    </div>
                    <ChevronRightIcon className="h-5 w-5 text-gray-400" />
                  </div>

                  {/* Quotes from the document */}
                  <div className="mt-2 w-full">
                    <ul className="list-disc space-y-2 pl-5">
                      {doc?.quotes.slice(0, 2).map((quote) => (
                        <li key={quote}>
                          <Typography
                            className="line-clamp-2 text-sm font-normal italic"
                            variant="body-small"
                          >
                            {quote}
                          </Typography>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="mt-2 flex w-full flex-row items-center justify-between">
                    <div className="flex flex-row gap-1.5">
                      {isUK && doc.newsPublisher && (
                        <div className="w-fit rounded-xl border px-1.5 pb-0.5 text-hubs-secondary">
                          <Typography variant="badge">
                            {doc.newsPublisher}
                          </Typography>
                        </div>
                      )}
                      {featuresEnabled.includes('featured_announcements') &&
                        doc.featuredOnHub && (
                          <BadgeV2 color="green">Featured</BadgeV2>
                        )}
                      {!!doc.marketSensitive && (
                        <Tooltip
                          hover
                          content={`This announcement is price sensitive`}
                        >
                          <CurrencyDollarIcon className="h-5 w-5 text-hubs-secondary" />
                        </Tooltip>
                      )}
                      {!!(doc.socialVideoUrl || doc.videoUrl) && (
                        <Tooltip
                          hover
                          content={`This announcement has a video uploaded by ${marketKey}:${listingKey}`}
                        >
                          <VideoCameraIcon className="h-5 w-5 text-hubs-secondary" />
                        </Tooltip>
                      )}
                      {!!doc.summary && (
                        <Tooltip
                          hover
                          content={`This announcement has a summary added by ${marketKey}:${listingKey}`}
                        >
                          <MenuAlt2Icon className="h-5 w-5 text-hubs-secondary" />
                        </Tooltip>
                      )}
                    </div>
                    <div className="mr-8 flex items-center gap-4">
                      {doc.totalComments > 0 && (
                        <div className="typography-body-regular flex items-center gap-2 text-hubs-secondary">
                          <ChatIcon className="h-5 w-5" />
                          {doc.totalComments}
                        </div>
                      )}
                      {doc.totalLikes > 0 && (
                        <div className="typography-body-regular flex items-center gap-2 text-hubs-secondary">
                          <ThumbUpIcon className="h-5 w-5" />
                          {doc.totalLikes}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            );
          }
          return null;
        });
      }
    }
    // Standard mode
    else if (data?.mediaAnnouncements) {
      const edges = data.mediaAnnouncements.edges;
      if (edges && edges.length === 0) {
        return <div className="py-4">{renderEmptyState()}</div>;
      }

      return (edges || []).map((edge) => {
        if (edge?.node) {
          const hasGermanTranslation =
            edge.node.germanTranslatedHeader ||
            edge.node.germanTranslatedSummary ||
            edge.node.germanTranslatedUrl ||
            edge.node.germanTranslatedVideoUrl;

          const href = `/announcements/${edge.node.id}`;

          return (
            <Link
              key={edge.node.id}
              className="block border-b py-4"
              href={href}
              onClick={() => {
                analytics.track('hermes_deep_search', {
                  announcement_id: edge.node?.id,
                  deep_search: useDeepSearch,
                  feature: 'announcements',
                  filter: 'ann_click',
                  hubs_version: '2',
                  search_terms: searchPhrase,
                });
              }}
            >
              <div className="flex flex-col items-start gap-1.5">
                <div className="flex w-full flex-row items-center justify-between">
                  <div className="flex flex-col items-start gap-2">
                    <Typography
                      className="flex gap-2 text-hubs-primary"
                      component="div"
                      variant="body-small"
                    >
                      <div>
                        {dayjs(edge.node.postedAt).format(
                          'DD/MM/YYYY • hh:mm A'
                        )}
                      </div>
                    </Typography>
                    {showLanguages && (
                      <div className="-my-1.5 text-lg tracking-[8px]">
                        <span aria-label="English" role="img">
                          🇬🇧
                        </span>
                        {hasGermanTranslation && (
                          <span aria-label="German" role="img">
                            🇩🇪
                          </span>
                        )}
                      </div>
                    )}
                    <Typography
                      className="line-clamp-2 text-company-primary"
                      variant="button"
                    >
                      {edge.node.header}
                    </Typography>
                  </div>
                  <ChevronRightIcon className="h-5 w-5 text-gray-400" />
                </div>
                <div className="mt-1 flex w-full flex-row items-center justify-between">
                  <div className="flex flex-row gap-1.5">
                    {/* Single category pill with tooltip showing subtypes */}
                    {edge.node.subtypes &&
                      edge.node.subtypes.length > 0 &&
                      (() => {
                        // Get all subtypes
                        const validSubtypes = edge.node.subtypes.filter(
                          (subtype): subtype is string => subtype !== null
                        );

                        if (validSubtypes.length === 0) return null;

                        // Get the rectype label for the pill
                        const rectypeLabel = getRecTypeLabel(
                          validSubtypes,
                          isUK
                        );

                        return (
                          <div className="mr-1 max-w-[300px] rounded-xl border border-gray-200 bg-gray-50 px-1.5 pb-0.5">
                            <Typography
                              className="truncate text-hubs-secondary"
                              variant="badge"
                            >
                              {rectypeLabel}
                            </Typography>
                          </div>
                        );
                      })()}
                    {featuresEnabled.includes('featured_announcements') &&
                      edge.node.featuredOnHub && (
                        <BadgeV2 color="green">Featured</BadgeV2>
                      )}
                    {isUK && edge.node.newsPublisher && (
                      <div className="w-fit rounded-xl border border-company-primary px-1.5 pb-0.5">
                        <Typography
                          className="text-company-primary"
                          variant="badge"
                        >
                          {edge.node.newsPublisher}
                        </Typography>
                      </div>
                    )}
                    {!!(edge.node.socialVideoUrl || edge.node.videoUrl) && (
                      <Tooltip
                        hover
                        content={`This announcement has a video uploaded by ${marketKey}:${listingKey}`}
                      >
                        <VideoCameraIcon className="h-5 w-5 text-hubs-secondary" />
                      </Tooltip>
                    )}
                    {!!edge.node.summary && (
                      <Tooltip
                        hover
                        content={`This announcement has a summary added by ${marketKey}:${listingKey}`}
                      >
                        <MenuAlt2Icon className="h-5 w-5 text-hubs-secondary" />
                      </Tooltip>
                    )}
                    {!!edge.node.marketSensitive && (
                      <Tooltip
                        hover
                        content={`This announcement is price sensitive`}
                      >
                        <CurrencyDollarIcon className="h-5 w-5 text-hubs-secondary" />
                      </Tooltip>
                    )}
                  </div>
                  <div className="mr-8 flex items-center gap-4">
                    {edge.node.totalParentComments > 0 && (
                      <div className="typography-body-regular flex items-center gap-2 text-hubs-secondary">
                        <ChatIcon className="h-5 w-5" />
                        {edge.node.totalParentComments}
                      </div>
                    )}
                    {edge.node.likes > 0 && (
                      <div className="typography-body-regular flex items-center gap-2 text-hubs-secondary">
                        <ThumbUpIcon className="h-5 w-5" />
                        {edge.node.likes}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          );
        }

        return null;
      });
    }
    return <div className="py-4">No results found</div>;
  }

  function renderTableBody() {
    if (loading) {
      return (
        <>
          {Array.from(new Array(rowsPerPage).keys()).map((item) => (
            <tr key={item}>
              <td className="px-5 py-3">
                <Skeleton
                  loading
                  fontSize="body-regular"
                  variant="text"
                  width="70%"
                />
              </td>
              <td className="py-3">
                <Skeleton
                  loading
                  fontSize="button"
                  variant="text"
                  width="70%"
                />
              </td>
              <td className="py-3">
                <div className="flex items-center">
                  <div className="h-6" />
                  <Skeleton
                    loading
                    fontSize="body-regular"
                    variant="text"
                    width="70%"
                  />
                </div>
              </td>
              <td className="py-3">
                <div className="flex items-center">
                  <div className="h-6" />
                  <Skeleton
                    loading
                    fontSize="body-regular"
                    variant="text"
                    width="70%"
                  />
                </div>
              </td>
            </tr>
          ))}
        </>
      );
    }

    // Standard mode - show regular announcements when no search or when search has results
    if (
      data?.mediaAnnouncements &&
      data.mediaAnnouncements.edges &&
      (!useDeepSearch || data.mediaAnnouncements.edges.length > 0)
    ) {
      // No results found
      if (data.mediaAnnouncements.edges.length === 0) {
        return (
          <tr>
            <td
              className="typography-body-regular px-5 py-3 text-hubs-primary"
              colSpan={4}
            >
              {renderEmptyState()}
            </td>
          </tr>
        );
      }

      // Results found display all
      return data.mediaAnnouncements.edges?.map((edge) => {
        if (edge?.node) {
          const hasGermanTranslation =
            edge.node.germanTranslatedHeader ||
            edge.node.germanTranslatedSummary ||
            edge.node.germanTranslatedUrl ||
            edge.node.germanTranslatedVideoUrl;

          const href = `/announcements/${edge.node.id}`;
          return (
            <tr
              key={edge.node.id}
              className="group bg-hubs-background px-4 transition-colors hover:bg-hubs-background-accent"
            >
              <td
                className="typography-body-regular whitespace-nowrap py-3 pl-5 text-hubs-primary"
                valign="top"
              >
                <div className="flex flex-col gap-0.5">
                  <p className="text-wrap text-sm font-medium transition-colors">
                    {dayjs(edge.node.postedAt).format('DD/MM/YYYY')}
                  </p>
                  <time className="text-sm text-hubs-secondary">
                    {dayjs(edge.node.postedAt).format('hh:mm A')}
                  </time>
                </div>
              </td>
              {showLanguages && (
                <td
                  className="typography-body-regular whitespace-nowrap py-3 text-xl tracking-[10px] text-hubs-primary"
                  valign="top"
                >
                  <span aria-label="English" role="img">
                    🇬🇧
                  </span>
                  {hasGermanTranslation && (
                    <span aria-label="German" role="img">
                      🇩🇪
                    </span>
                  )}
                </td>
              )}
              <td
                className="typography-button w-full whitespace-nowrap py-3"
                valign="top"
              >
                <Link
                  className="flex flex-col gap-1"
                  href={href}
                  onClick={() => {
                    analytics.track('hermes_deep_search', {
                      announcement_id: edge.node?.id,
                      deep_search: useDeepSearch,
                      feature: 'announcements',
                      filter: 'ann_click',
                      hubs_version: '2',
                      search_terms: searchPhrase,
                    });
                  }}
                >
                  <p className="line-clamp-1 text-wrap pr-4 text-sm text-company-primary">
                    {edge.node.header}
                  </p>
                  <div className="flex w-full flex-row justify-between">
                    <div className="flex flex-row items-center gap-1">
                      {/* Single category pill with tooltip showing subtypes */}
                      {edge.node.subtypes &&
                        edge.node.subtypes.length > 0 &&
                        (() => {
                          // Get all subtypes
                          const validSubtypes = edge.node.subtypes.filter(
                            (subtype): subtype is string => subtype !== null
                          );

                          if (validSubtypes.length === 0) return null;

                          // Get the rectype label for the pill
                          const rectypeLabel = getRecTypeLabel(
                            validSubtypes,
                            isUK
                          );

                          return (
                            <div className="max-w-[300px] rounded-xl border border-gray-200 bg-gray-50 px-2 text-hubs-secondary">
                              <Typography
                                className="truncate text-xs"
                                variant="badge"
                              >
                                {rectypeLabel}
                              </Typography>
                            </div>
                          );
                        })()}
                      {isUK && edge.node.newsPublisher && (
                        <div className="w-fit rounded-xl border px-2 text-hubs-secondary">
                          <Typography className="text-xs" variant="badge">
                            {edge.node.newsPublisher}
                          </Typography>
                        </div>
                      )}
                      {featuresEnabled.includes('featured_announcements') &&
                        edge.node.featuredOnHub && (
                          <BadgeV2 color="green">Featured</BadgeV2>
                        )}
                      {!!edge.node.marketSensitive && (
                        <Tooltip
                          hover
                          content={`This announcement is price sensitive`}
                        >
                          <CurrencyDollarIcon className="size-5 stroke-[1.5] text-hubs-secondary" />
                        </Tooltip>
                      )}
                      {!!(edge.node.socialVideoUrl || edge.node.videoUrl) && (
                        <Tooltip
                          hover
                          content={`This announcement has a video uploaded by ${marketKey}:${listingKey}`}
                        >
                          <VideoCameraIcon className="size-5 stroke-[1.5] text-hubs-secondary" />
                        </Tooltip>
                      )}
                      {!!edge.node.summary && (
                        <Tooltip
                          hover
                          content={`This announcement has a summary added by ${marketKey}:${listingKey}`}
                        >
                          <MenuAlt2Icon className="size-5 stroke-[1.5] text-hubs-secondary" />
                        </Tooltip>
                      )}
                    </div>
                    <div className="flex flex-row items-center justify-center gap-4 pr-6">
                      {edge.node.totalParentComments > 0 && (
                        <Link
                          passHref
                          className="typography-body-regular flex items-center gap-1.5 text-hubs-secondary"
                          href={`${href}#question-section`}
                          scroll={false}
                        >
                          <ChatIcon className="size-5" />
                          {edge.node.totalParentComments}
                        </Link>
                      )}

                      {edge.node.likes > 0 && (
                        <div className="typography-body-regular flex items-center gap-1.5 text-hubs-secondary">
                          <ThumbUpIcon className="size-5" />
                          {edge.node.likes}
                        </div>
                      )}
                    </div>
                  </div>
                </Link>
              </td>
            </tr>
          );
        }

        return null;
      });
    }
    // Deep AI search mode
    else if (useDeepSearch && documents) {
      // No documents -> empty state
      if (aiQueryResult?.loading) {
        return (
          <tr>
            <td
              className="typography-body-regular px-5 py-3 text-hubs-primary"
              colSpan={4}
            >
              {renderLoadingState()}
            </td>
          </tr>
        );
      } else if (documents.length === 0) {
        return (
          <tr>
            <td
              className="typography-body-regular px-5 py-3 text-hubs-primary"
              colSpan={4}
            >
              {renderEmptyState()}
            </td>
          </tr>
        );
      } else {
        return documents.map((doc) => {
          if (doc) {
            const href = `/announcements/${doc?.mediaAnnouncementId}`;
            return (
              <tr
                key={doc?.mediaAnnouncementId}
                className="group bg-hubs-background px-4 transition-colors hover:bg-hubs-background-accent"
              >
                <td
                  className="typography-body-regular whitespace-nowrap py-3 pl-5 text-hubs-primary"
                  valign="top"
                >
                  <div className="flex flex-col gap-0.5">
                    <p className="text-wrap text-sm font-medium transition-colors">
                      {dayjs(doc.postedAt).format('DD/MM/YYYY')}
                    </p>
                    <time className="text-sm text-hubs-secondary">
                      {dayjs(doc.postedAt).format('hh:mm A')}
                    </time>
                  </div>
                </td>
                {/* {showLanguages && ( */}
                {/*   <td */}
                {/*     className="typography-body-regular whitespace-nowrap py-3 text-xl tracking-[10px] text-hubs-primary" */}
                {/*     valign="top" */}
                {/*   > */}
                {/*     <span aria-label="English" role="img"> */}
                {/*       🇬🇧 */}
                {/*     </span> */}
                {/*     {hasGermanTranslation && ( */}
                {/*       <span aria-label="German" role="img"> */}
                {/*         🇩🇪 */}
                {/*       </span> */}
                {/*     )} */}
                {/*   </td> */}
                {/* )} */}
                <td
                  className="typography-button w-full whitespace-nowrap py-3"
                  valign="top"
                >
                  <Link
                    className="flex flex-col gap-3"
                    href={href}
                    onClick={() => {
                      analytics.track('hermes_deep_search', {
                        announcement_id: doc?.mediaAnnouncementId,
                        deep_search: useDeepSearch,
                        feature: 'announcements',
                        filter: 'ann_click',
                        hubs_version: '2',
                        search_terms: searchPhrase,
                      });
                    }}
                  >
                    <Typography variant="subtitle-1">{doc.title}</Typography>
                    <div className="flex w-2/3 flex-col gap-3">
                      <ul className="list-disc space-y-3 pl-5">
                        {doc?.quotes
                          ?.filter((quote) => quote && quote.length > 0)
                          .map((quote) => {
                            return (
                              <li key={quote}>
                                <div className="inline-flex text-sm font-normal italic leading-tight">
                                  <div className="flex flex-row gap-2">
                                    <div className="line-clamp-2 flex flex-row gap-2 overflow-hidden">
                                      <Typography
                                        noWrap={false}
                                        variant="body-small"
                                      >
                                        {quote && quote.length > 160
                                          ? `${quote.substring(0, 160)}...`
                                          : quote}
                                      </Typography>
                                      <div className="inline-flex items-center justify-center gap-1 rounded-2xl bg-gray-100 px-2 py-0.5">
                                        <div className="relative h-3 w-3 overflow-hidden align-middle">
                                          <DocumentTextIcon />
                                        </div>
                                        {/* <div className="text-center justify-start text-text-secondary font-medium leading-none text-xs"> */}
                                        {/*   Found in announcement */}
                                        {/* </div> */}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </li>
                            );
                          })}
                      </ul>
                    </div>
                    <div className="flex w-full flex-row justify-between">
                      <div className="flex flex-row items-center gap-1">
                        {/* Category pill - removed for AI retrieved announcements */}
                        {isUK && doc.newsPublisher && (
                          <div className="w-fit rounded-xl border px-2 text-hubs-secondary">
                            <Typography className="text-xs" variant="badge">
                              {doc.newsPublisher}
                            </Typography>
                          </div>
                        )}
                        {featuresEnabled.includes('featured_announcements') &&
                          doc.featuredOnHub && (
                            <BadgeV2 color="green">Featured</BadgeV2>
                          )}
                        {!!doc.marketSensitive && (
                          <Tooltip
                            hover
                            content={`This announcement is price sensitive`}
                          >
                            <CurrencyDollarIcon className="size-5 stroke-[1.5] text-hubs-secondary" />
                          </Tooltip>
                        )}
                        {!!(doc.socialVideoUrl || doc.videoUrl) && (
                          <Tooltip
                            hover
                            content={`This announcement has a video uploaded by ${marketKey}:${listingKey}`}
                          >
                            <VideoCameraIcon className="size-5 stroke-[1.5] text-hubs-secondary" />
                          </Tooltip>
                        )}
                        {!!doc.summary && (
                          <Tooltip
                            hover
                            content={`This announcement has a summary added by ${marketKey}:${listingKey}`}
                          >
                            <MenuAlt2Icon className="size-5 stroke-[1.5] text-hubs-secondary" />
                          </Tooltip>
                        )}
                      </div>
                      <div className="flex flex-row items-center justify-center gap-4 pr-6">
                        {doc.totalComments > 0 && (
                          <Link
                            passHref
                            className="typography-body-regular flex items-center gap-1.5 text-hubs-secondary"
                            href={`${href}#question-section`}
                            scroll={false}
                          >
                            <ChatIcon className="size-5" />
                            {doc.totalComments}
                          </Link>
                        )}

                        {doc.totalLikes > 0 && (
                          <div className="typography-body-regular flex items-center gap-1.5 text-hubs-secondary">
                            <ThumbUpIcon className="size-5" />
                            {doc.totalLikes}
                          </div>
                        )}
                      </div>
                    </div>
                  </Link>
                </td>
              </tr>
            );
          }

          return null;
        });
      }
    }

    return (
      <tr>
        <td
          className="typography-body-regular px-5 py-3 font-body text-hubs-secondary"
          colSpan={4}
        >
          <em>Oops! Something went wrong.</em>
        </td>
      </tr>
    );
  }

  return (
    <div className="relative scroll-mt-40" id="announcements_block">
      <span
        ref={rowSelectRef}
        className="absolute inset-x-0 top-0 h-0 w-full"
      />
      <div className="w-full max-w-screen-xl px-4 sm:px-6 lg:mx-auto">
        {heading && heading.length > 0 && (
          <BlockHeading className="mb-4">{heading}</BlockHeading>
        )}
        <div className="mb-4 flex flex-col gap-6">
          <div className="flex flex-col gap-3">
            <div className="relative flex h-14 w-full items-center rounded-lg border border-gray-100 px-4 py-3">
              <div className="pointer-events-none absolute left-5 flex items-center">
                <SearchIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                className="w-full border-0 bg-transparent pl-8 text-base leading-normal outline-none focus:ring-0"
                placeholder=""
                type="text"
                value={searchFieldValue}
                onChange={(e) => {
                  setSearchFieldValue(e.target.value);
                  onChange(e.target.value);
                }}
              />
              {!searchFieldValue && (
                <div className="pointer-events-none absolute left-[52px] top-1/2 flex -translate-y-1/2 items-center text-gray-400">
                  <span className="inline-block animate-[typewriter_3s_steps(23,end)_forwards] overflow-hidden whitespace-nowrap">
                    Search by keywords...
                  </span>
                </div>
              )}
            </div>
            <div className="mt-1 flex flex-wrap gap-2 px-1">
              <button
                className="rounded-full bg-gray-50 px-3 py-1.5 text-sm font-normal text-gray-600 transition-colors hover:bg-gray-100"
                onClick={() => {
                  setSearchFieldValue('Recent capital raise');
                  onChange('Recent capital raise');
                }}
              >
                Recent capital raise
              </button>
              <button
                className="rounded-full bg-gray-50 px-3 py-1.5 text-sm font-normal text-gray-600 transition-colors hover:bg-gray-100"
                onClick={() => {
                  setSearchFieldValue('Current cashflow');
                  onChange('Current cashflow');
                }}
              >
                Current cashflow
              </button>
              <button
                className="rounded-full bg-gray-50 px-3 py-1.5 text-sm font-normal text-gray-600 transition-colors hover:bg-gray-100"
                onClick={() => {
                  setSearchFieldValue('Project updates and timeline');
                  onChange('Project updates and timeline');
                }}
              >
                Project updates and timeline
              </button>
              <button
                className="rounded-full bg-gray-50 px-3 py-1.5 text-sm font-normal text-gray-600 transition-colors hover:bg-gray-100"
                onClick={() => {
                  setSearchFieldValue('AGM shareholder votes');
                  onChange('AGM shareholder votes');
                }}
              >
                AGM shareholder votes
              </button>
            </div>
          </div>
          <div className="relative z-10 flex w-full flex-col gap-4 md:flex-row md:items-center md:justify-between md:gap-6">
            <div className="w-full md:w-auto">
              <QuickFilters
                appliedFilters={appliedFilters}
                isFeatured={featuresEnabled.includes('featured_announcements')}
                isUK={isUK}
                setAppliedFilters={setAppliedFilters}
                tags={tags}
              />
            </div>
            <div className="w-full md:ml-auto md:w-auto">
              <QuickSort orders={orders} setOrder={setOrder} />
            </div>
          </div>
        </div>

        <div className="mt-6 lg:hidden">
          <div className="relative z-0">{renderList()}</div>

          <div className="relative z-0 mt-4 flex flex-row items-center justify-between gap-4 md:gap-10">
            <Select
              className="font-system shadow-none"
              label=""
              options={[
                { label: '10', value: 10 },
                { label: '20', value: 20 },
                { label: '50', value: 50 },
              ]}
              value={rowsPerPage}
              onChange={setRows}
            />
            <Pagination
              compact
              currentPage={currentPage}
              rowsPerPage={rowsPerPage}
              setCurrentPage={handlePageChange}
              totalCount={totalCount}
            />
          </div>
        </div>

        <div className="hidden lg:block">
          <div className="relative z-0 overflow-hidden rounded-lg border">
            <table className="w-full table-fixed divide-y">
              <thead className="bg-hubs-background-accent">
                <tr>
                  <th className="w-40 gap-2 whitespace-nowrap bg-hubs-background-accent py-3 pl-5 text-left font-body text-xs uppercase !tracking-widest text-hubs-secondary">
                    Date
                  </th>
                  {showLanguages && (
                    <th className="w-32 gap-2 whitespace-nowrap bg-hubs-background-accent py-3  text-left font-body text-xs uppercase !tracking-widest text-hubs-secondary">
                      Language
                    </th>
                  )}
                  <th className="w-full whitespace-nowrap bg-hubs-background-accent py-3 text-left font-body text-xs uppercase !tracking-widest text-hubs-secondary">
                    Headline
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y">{renderTableBody()}</tbody>
            </table>
          </div>

          <div className="relative z-0 mt-4 flex w-full items-center gap-6">
            <div className="col-span-1">
              <Select
                className="font-system shadow-none"
                label=""
                options={[
                  { label: '10', value: 10 },
                  { label: '20', value: 20 },
                  { label: '50', value: 50 },
                ]}
                value={rowsPerPage}
                onChange={setRows}
              />
            </div>
            <div className="flex-1">
              <Pagination
                currentPage={currentPage}
                rowsPerPage={rowsPerPage}
                setCurrentPage={handlePageChange}
                totalCount={totalCount}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
