import { AnnouncementType } from '../../../index';

/**
 * Helper function to get the rectype name for a given subtype value
 * @param subtypeValue The subtype value to look up
 * @param announcementTypes The announcement types from the backend
 * @returns The rectype name and subtype name, or null if not found
 */
export const getAnnouncementTypeInfo = (
  subtypeValue: string,
  announcementTypes: AnnouncementType[] | null | undefined
): { rectypeName: string; subtypeName: string } | null => {
  if (!announcementTypes || announcementTypes.length === 0) {
    return null;
  }

  // Find the announcement type that contains this subtype
  for (const announcementType of announcementTypes) {
    // Find the subtype in this announcement type
    const subtype = announcementType.subtypes.find(
      (st) => st.value === subtypeValue
    );

    if (subtype) {
      return {
        rectypeName: announcementType.rectype,
        subtypeName: subtype.subtype,
      };
    }
  }

  return null;
};

/**
 * Helper function to get the rectype name for the pill
 * @param subtypeValues Array of subtype values
 * @param announcementTypes The announcement types from the backend
 * @returns Rectype name
 */
export const getRectype = (
  subtypeValues: string[],
  announcementTypes: AnnouncementType[] | null | undefined
): string => {
  if (!subtypeValues || subtypeValues.length === 0) {
    return 'Other';
  }

  // Get type info for the first subtype
  let typeInfo: { rectypeName: string; subtypeName: string } | null = null;
  for (const subtypeValue of subtypeValues) {
    typeInfo = getAnnouncementTypeInfo(subtypeValue, announcementTypes);
    if (typeInfo !== null) {
      break;
    }
  }

  if (!typeInfo) {
    return 'Other';
  }

  // Return just the rectype name
  return typeInfo.rectypeName;
};

/**
 * Helper function to get subtypes for tooltip
 * @param subtypeValues Array of subtype values
 * @param announcementTypes The announcement types from the backend
 * @returns Formatted subtypes for tooltip
 */
export const getSubtypesTooltip = (
  subtypeValues: string[],
  announcementTypes: AnnouncementType[] | null | undefined
): string => {
  if (!subtypeValues || subtypeValues.length === 0) {
    return '';
  }

  // Get type info for all subtypes
  const typeInfos = subtypeValues
    .map((subtypeValue) =>
      getAnnouncementTypeInfo(subtypeValue, announcementTypes)
    )
    .filter(
      (info): info is { rectypeName: string; subtypeName: string } =>
        info !== null
    );

  if (typeInfos.length === 0) {
    return '';
  }

  // Group by rectype
  const rectypeGroups: Record<string, string[]> = {};

  for (const info of typeInfos) {
    if (!rectypeGroups[info.rectypeName]) {
      rectypeGroups[info.rectypeName] = [];
    }
    rectypeGroups[info.rectypeName].push(info.subtypeName);
  }

  // Format the tooltip text
  const formattedGroups = Object.entries(rectypeGroups).map(
    ([rectypeName, subtypeNames]) => {
      // If there are multiple subtypes, join them with commas
      const subtypesText = subtypeNames.join(', ');
      return `${rectypeName}: ${subtypesText}`;
    }
  );

  // Join multiple rectype groups with line breaks
  return formattedGroups.join('<br />');
};
