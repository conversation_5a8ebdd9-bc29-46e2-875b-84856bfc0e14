import Config

alias GoogleApi.SecretManager.V1.Api.Projects
alias <PERSON><PERSON>pi.SecretManager.V1.Connection

# We want to define all the runtime configurations that are shared between releases
# in this file. Elixir expects one main runtime.exs file, and does not allow
# importing other runtime.exs files when it gets run.

# This runtime.exs is shared across all environments. i.e it gets run on dev locally as well as
# when the application is deployed and started up

# There are two ways to make custom configs for different releases, while still sharing this base runtime config file
# 1. Add to the main `mix.exs`
#   config_providers: [
#     {Config.Reader, {:system, "RELEASE_ROOT", "/extra_config.exs"}}
#   ]
#   with extra_config at /rel/overlays/extra_config.exs
# or
# 2. Add to the main `mix.exs`
#   config_providers: [{Leaf.Config.SecretProvider, ""}] and add the Leaf.Config.SecretProvider module
#   that implements the provider pattern

IO.puts("Running main runtime.exs on #{config_env()}")

# The expected values of LEAF_ENVIRONMENT are "test, "development", "production" and "staging"
environment = System.get_env("LEAF_ENVIRONMENT", "development")

if config_env() == :dev do
  config :goth, disabled: !System.get_env("ALLOW_LOCAL_UPLOADS")

  config :gaia,
    beneficial_owner_nominee_contacts_encryption_key: System.get_env("BO_NOMINEE_CONTACTS_PASSWORD_KEY")

  if System.get_env("ALLOW_LOCAL_UPLOADS") do
    config :helper,
      service_account: "GOOGLE_APPLICATION_CREDENTIALS" |> System.fetch_env!() |> File.read!()
  end

  if System.get_env("ALLOW_LOCAL_UPLOADS") &&
       System.get_env("GCP_SERVICE_ACCOUNT_CREDENTIALS_JSON") &&
       System.get_env("GOOGLE_STORAGE_BUCKET") do
    {:ok, _} = Application.ensure_all_started(:goth)
    {:ok, _} = Application.ensure_all_started(:tesla)

    goth_config = "GCP_SERVICE_ACCOUNT_CREDENTIALS_JSON" |> System.get_env() |> Jason.decode!()

    Goth.Config.set("client_email", Map.get(goth_config, "client_email"))
    Goth.Config.set("private_key", Map.get(goth_config, "private_key"))

    config :arc,
      bucket: System.get_env("GOOGLE_STORAGE_BUCKET")
  end

  # set export ALLOW_DOMAIN_TESTING=true to test setting up domains in dev
  if System.get_env("ALLOW_DOMAIN_TESTING") do
    # This is for email DNS
    config :aws,
      region: System.get_env("AWS_REGION"),
      access_key: System.get_env("AWS_SES_ACCESS_KEY"),
      sns_topic_arn: System.get_env("AWS_SNS_TOPIC_ARN"),
      secret: System.get_env("AWS_SES_SECRET")

    # This is for custom domain DNS
    config :vercel,
      hermes_project_id: System.get_env("VERCEL_HERMES_PROJECT_ID"),
      api_key: System.get_env("VERCEL_API_KEY"),
      team_id: System.get_env("VERCEL_TEAM_ID")
  end

  if System.get_env("OPENAI_API_KEY") do
    config :langchain, openai_key: System.get_env("OPENAI_API_KEY")
    config :langchain, openai_org_id: System.get_env("OPENAI_ORG_ID")
  end

  # Uncomment to test domain verification with AWS
  # if System.get_env("AWS_SES_ACCESS_KEY") do
  #   config :aws,
  #     region: System.get_env("AWS_REGION"),
  #     access_key: System.get_env("AWS_SES_ACCESS_KEY"),
  #     sns_topic_arn: System.get_env("AWS_SNS_TOPIC_ARN"),
  #     secret: System.get_env("AWS_SES_SECRET")
  # end

  # Uncomment to test RNS import worker
  # if System.get_env("S3_ACCESS_KEY") do
  #   config :aws,
  #     s3_access_key: System.get_env("S3_ACCESS_KEY"),
  #     s3_secret: System.get_env("S3_SECRET")
  # end

  # Uncomment to test sentry on dev
  # config :sentry,
  #   dsn: "https://<EMAIL>/6385636",
  #   included_environments: [:dev],
  #   environment_name: config_env(),
  #   enable_source_code_context: true,
  #   # in_app_module_whitelist: app_names,
  #   # We're in the umbrella root here
  #   root_source_code_path: File.cwd!() <> "/apps",
  #   source_code_exclude_patterns: [
  #     ~r"/priv/",
  #     ~r"/test/",
  #     ~r"/config/",
  #     ~r"/assets/"
  #   ],
  #   before_send_event: {Helper.SentryConfig, :before_send_event}

  # uncomment to test appsignal in dev env
  # config :appsignal, :config,
  #   otp_app: :gaia,
  #   name: "leaf_be",
  #   push_api_key: System.get_env("APPSIGNAL_API_KEY"),
  #   env: environment
end

if config_env() == :test do
  config :goth, disabled: true

  config :helper,
    secrets_master_key: System.get_env("SECRETS_MASTER_KEY")

  # cloudex throws a shitfest if you don't feed it
  config :cloudex,
    api_key: "abcd1234",
    secret: "abcd1234",
    cloud_name: "abcd1234",
    root_folder: "leaf-testing"
end

if config_env() == :prod || config_env() == :staging do
  project_id =
    System.get_env(
      "GOOGLE_CLOUD_PROJECT",
      System.get_env("GCLOUD_PROJECT")
    )

  IO.puts("Setting up Google Secret Manager")
  {:ok, _} = Application.ensure_all_started(:goth)
  {:ok, _} = Application.ensure_all_started(:tesla)

  {:ok, _} = Goth.start_link(name: GothService)
  token = Goth.fetch!(GothService)

  conn = Connection.new(token.token)

  fetch_secret! = fn secrets_id, versions_id ->
    secret_uri = "projects/#{project_id}/secrets/#{secrets_id}/versions/#{versions_id}"

    response =
      Projects.secretmanager_projects_secrets_versions_access(
        conn,
        secret_uri
      )

    case response do
      {:ok, %{payload: %{data: data}}} ->
        Base.decode64!(data)

      {:error, _} ->
        raise("Cannot retrieve secret: #{secrets_id}, #{versions_id}")
    end
  end

  # This fetch_secret returns the secret if found and nil if not found
  # All other case will raise error such as when encountering 403 or 500
  fetch_secret = fn secrets_id, versions_id ->
    secret_uri = "projects/#{project_id}/secrets/#{secrets_id}/versions/#{versions_id}"

    response =
      Projects.secretmanager_projects_secrets_versions_access(
        conn,
        secret_uri
      )

    case response do
      {:ok, %{payload: %{data: data}}} ->
        Base.decode64!(data)

      {:error, %_{status: 404, body: body}} ->
        body
        |> Jason.decode()
        |> case do
          {:ok, %{"error" => %{"message" => "Secret " <> _, "status" => "NOT_FOUND"}}} ->
            nil

          _ ->
            raise("Cannot retrieve secret: #{secrets_id}, #{versions_id}")
        end

      {:error, _} ->
        raise("Cannot retrieve secret: #{secrets_id}, #{versions_id}")
    end
  end

  #########################################################################
  # Shared                                                                #
  #########################################################################
  IO.puts("Setting up shared configs")

  config :helper,
    athena_url: System.get_env("ATHENA_URL", "https://app.investorhub.com"),
    athena_web_url: System.get_env("ATHENA_WEB_URL", "https://athena.investorhub.com"),
    hades_url: System.get_env("HADES_URL", "https://admin.investorhub.com"),
    hermes_url: System.get_env("HERMES_WEB_URL", "https://hermes.investorhub.com"),
    service_account: fetch_secret!.("#{environment}-gcp-service-account-key", "latest"),
    hash_salt: fetch_secret!.("hash_salt", "latest"),
    runtime_env: environment,
    secrets_master_key: System.get_env("SECRETS_MASTER_KEY"),
    ssh_username: fetch_secret!.("ssh-ticker-basic-auth-user", "latest"),
    ssh_password: fetch_secret!.("ssh-ticker-basic-auth-password", "latest")

  config :aws,
    access_key: fetch_secret!.("#{environment}-ses-access-key", "latest"),
    region: fetch_secret!.("aws-region", "latest"),
    secret: fetch_secret!.("#{environment}-ses-secret", "latest"),
    sns_topic_arn: fetch_secret!.("#{environment}-sns-topic-arn", "latest"),
    s3_access_key: fetch_secret!.("s3-access-key", "latest"),
    s3_secret: fetch_secret!.("s3-secret", "latest")

  config :cloudex,
    api_key: fetch_secret!.("#{environment}-cloudinary-api-key", "latest"),
    secret: fetch_secret!.("#{environment}-cloudinary-api-secret", "latest"),
    cloud_name: fetch_secret!.("#{environment}-cloudinary-cloud-name", "latest"),
    root_folder: fetch_secret!.("#{environment}-cloudinary-root-folder", "latest")

  config :sentry,
    dsn: fetch_secret!.("#{environment}-leaf-sentry-dsn", "latest"),
    included_environments: [:prod, :staging],
    environment_name: config_env(),
    enable_source_code_context: true,
    # in_app_module_whitelist: app_names,
    # We're in the umbrella root here
    root_source_code_path: File.cwd!() <> "/apps",
    source_code_exclude_patterns: [
      ~r"/priv/",
      ~r"/test/",
      ~r"/config/",
      ~r"/assets/"
    ],
    before_send_event: {Helper.SentryConfig, :before_send_event}

  {:ok, _} = Application.ensure_all_started(:tesla)
  {:ok, _} = Application.ensure_all_started(:goth)

  goth_config =
    "#{environment}-gcp-service-account-key" |> fetch_secret!.("latest") |> Jason.decode!()

  Goth.Config.set("client_email", Map.get(goth_config, "client_email"))
  Goth.Config.set("private_key", Map.get(goth_config, "private_key"))

  secret_key_base = fetch_secret!.("#{environment}-secret-key-base", "latest")

  #########################################################################
  # Appsignal                                                             #
  #########################################################################
  IO.puts("Setting up Appsignal configs")

  revision =
    NaiveDateTime.utc_now()
    |> NaiveDateTime.to_string()
    |> String.replace(" ", "")
    |> String.slice(0..-11//1)

  IO.puts("Current revision #{revision}")

  config :appsignal, :config,
    otp_app: :gaia,
    name: "leaf_be",
    push_api_key: fetch_secret!.("appsignal-push-api-key", "latest"),
    env: environment,
    ignore_errors: [
      "ChromicPDF.Browser.ExecutionError",
      "ChromicPDF.ChromeError",
      "ChromicPDF.Connection.ConnectionLostError"
    ],
    log: :stdout,
    log_level: :warning,

    # https://docs.appsignal.com/elixir/configuration/options.html#option-report_oban_errors
    # Only report Oban errors to Appsignal if still failing at max attempt
    report_oban_errors: "discard",
    revision: revision,
    filter_parameters: [
      "password",
      "secret",
      "token",
      "api_key",
      "client_secret",
      "private_key",
      "access_token",
      "auth_token",
      "address_line_one",
      "address_line_two",
      "address_city",
      "address_state",
      "address_postcode",
      "address_country"
    ]

  #########################################################################
  # Analytics                                                             #
  #########################################################################
  IO.puts("Setting up Analytics configs")

  config :analytics,
    athena_segment_key: fetch_secret!.("#{environment}-athena-segment-key", "latest"),
    hermes_segment_key: fetch_secret!.("#{environment}-hermes-segment-key", "latest"),
    athena_mixpanel_project_id: fetch_secret!.("#{environment}-athena-mixpanel-project-id", "latest"),
    hermes_mixpanel_project_id: fetch_secret!.("#{environment}-hermes-mixpanel-project-id", "latest"),
    mixpanel_service_account_username: fetch_secret!.("#{environment}-mixpanel-service-account-username", "latest"),
    mixpanel_service_account_password: fetch_secret!.("#{environment}-mixpanel-service-account-password", "latest")

  #########################################################################
  # Athena                                                                #
  #########################################################################
  IO.puts("Setting up Athena configs")

  config :athena, AthenaWeb.Endpoint,
    http: [
      ip: {127, 0, 0, 1},
      port: 4001,
      protocol_options: [max_request_line_length: 8192, max_header_value_length: 8192]
    ],
    url: [host: "ATHENA_URL" |> System.get_env() |> String.replace("https://", ""), port: 80],
    secret_key_base: secret_key_base,
    server: true

  config :athena,
    linkedin_client_id: fetch_secret!.("athena-linkedin-client-id", "latest"),
    linkedin_client_secret: fetch_secret!.("athena-linkedin-client-secret", "latest"),
    twitter_client_id: fetch_secret!.("athena-twitter-client-id", "latest"),
    twitter_client_secret: fetch_secret!.("athena-twitter-client-secret", "latest"),
    log_email_basic_auth: %{
      username: fetch_secret!.("#{environment}-athena-log-email-basic-auth-username", "latest"),
      password: fetch_secret!.("#{environment}-athena-log-email-basic-auth-password", "latest")
    }

  #########################################################################
  # Automic                                                               #
  #########################################################################
  IO.puts("Setting up Automic configs")

  config :automic,
    api_key: fetch_secret!.("automic-api-key", "latest"),
    base_url: fetch_secret!.("automic-base-url", "latest"),
    client_id: fetch_secret!.("automic-client-id", "latest"),
    client_secret: fetch_secret!.("automic-client-secret", "latest")

  ################################################################################
  # pythia                                                                       #
  ################################################################################

  config :pythia, url: fetch_secret!.("#{environment}-pythia-url", "latest")

  #########################################################################
  # Gaia                                                                  #
  #########################################################################
  config :openai,
    key: fetch_secret!.("openai-key", "latest"),
    org_id: fetch_secret!.("openai-org-id", "latest")

  config :langchain,
    openai_key: fetch_secret!.("openai-key", "latest"),
    openai_org_id: fetch_secret!.("openai-org-id", "latest")

  config :openai_private,
    endpoint: fetch_secret!.("openai-private-endpoint", "latest"),
    api_key: fetch_secret!.("openai-private-api-key", "latest")

  # Gaia                                                                  #
  #########################################################################
  IO.puts("Setting up gaia configs")

  config :gaia, Gaia.Repo,
    types: Gaia.PostgrexTypes,
    username: fetch_secret!.("#{environment}-postgres-username", "latest"),
    password: fetch_secret!.("#{environment}-postgres-password", "latest"),
    database: fetch_secret!.("#{environment}-postgres-database", "latest"),
    socket_dir: System.get_env("POSTGRES_SOCKET_DIR"),
    queue_target: 1500,
    queue_interval: 4000,
    pool_size: "POSTGRES_POOL_SIZE" |> System.get_env() |> String.to_integer(),
    timeout: 60_000,
    migration_lock: :pg_advisory_lock

  config :libcluster,
    topologies: [
      gae: [
        strategy: Gaia.ClusterStrategy,
        cluster_across_versions: false
      ]
    ]

  config :gaia,
    metabase_static_embed_secret: fetch_secret.("#{environment}-metabase-static-embed-secret", "latest"),
    vault_key: fetch_secret!.("#{environment}-vault-key", "latest"),
    beneficial_owner_nominee_contacts_encryption_key:
      fetch_secret!.("beneficial_owners_report_nominee_contacts_password_key", "latest")

  #########################################################################
  # Email                                                                 #
  #########################################################################
  IO.puts("Setting up Email configs")

  config :email_transactional, EmailTransactionalWeb.Endpoint,
    http: [ip: {127, 0, 0, 1}, port: 4005],
    secret_key_base: secret_key_base,
    server: true

  #########################################################################
  # Email Tracking                                                        #
  #########################################################################
  IO.puts("Setting up email tracking config")

  config :tracking, TrackingWeb.Endpoint,
    http: [
      ip: {127, 0, 0, 1},
      port: 4004,
      protocol_options: [max_request_line_length: 8192, max_header_value_length: 8192]
    ],
    secret_key_base: secret_key_base,
    server: true

  #########################################################################
  # Tools for internal usage only
  # Login information can be found in 1password or secret manager
  #########################################################################
  config :tools,
    basic_auth: %{
      username: fetch_secret!.("tools-basic-auth-user", "latest"),
      password: fetch_secret!.("tools-basic-auth-password", "latest")
    }

  #########################################################################
  # Hades                                                                 #
  #########################################################################
  IO.puts("Setting up hades configs")

  config :hades, HadesWeb.Endpoint,
    http: [
      ip: {127, 0, 0, 1},
      port: 4002,
      protocol_options: [max_request_line_length: 8192, max_header_value_length: 8192]
    ],
    url: [host: "HADES_URL" |> System.get_env() |> String.replace("https://", ""), port: 80],
    secret_key_base: secret_key_base,
    server: true

  #########################################################################
  # Hermes                                                                #
  #########################################################################
  IO.puts("Setting up hermes configs")

  config :hermes, HermesWeb.Endpoint,
    http: [
      ip: {127, 0, 0, 1},
      port: 4003,
      protocol_options: [max_request_line_length: 8192, max_header_value_length: 8192]
    ],
    secret_key_base: secret_key_base,
    server: true

  #########################################################################
  # public_api                                                            #
  #########################################################################
  IO.puts("Setting up public_api configs")

  config :public_api, PublicApiWeb.Endpoint,
    http: [
      ip: {127, 0, 0, 1},
      port: 4006,
      protocol_options: [max_request_line_length: 8192, max_header_value_length: 8192]
    ],
    secret_key_base: secret_key_base,
    server: true

  config :heimdallr, HeimdallrWeb.Endpoint,
    http: [
      ip: {127, 0, 0, 1},
      port: 4007,
      protocol_options: [max_request_line_length: 8192, max_header_value_length: 8192]
    ],
    secret_key_base: secret_key_base,
    server: true

  #########################################################################
  # Twilio                                                                #
  #########################################################################
  IO.puts("Setting up twilio configs")

  config :twilio,
    sid: fetch_secret!.("#{environment}-twilio-sid", "latest"),
    auth_token: fetch_secret!.("#{environment}-twilio-token", "latest")

  #########################################################################
  # Weblink                                                               #
  #########################################################################
  IO.puts("Setting up weblink configs")

  config :weblink,
    user_id: fetch_secret!.("#{environment}-weblink-user-id", "latest"),
    password: fetch_secret!.("#{environment}-weblink-password", "latest")

  #########################################################################
  # Refinitiv                                                             #
  #########################################################################
  IO.puts("Setting up refinitiv configs")

  config :refinitiv,
    app_id: fetch_secret!.("#{environment}-refinitiv-app-id", "latest"),
    username: fetch_secret!.("#{environment}-refinitiv-username", "latest"),
    password: fetch_secret!.("#{environment}-refinitiv-password", "latest")

  #########################################################################
  # Boardroom                                                             #
  #########################################################################
  IO.puts("Setting up boardroom configs")

  config :boardroom,
    api_key: fetch_secret!.("boardroom-api-key", "latest"),
    proxy_ip: fetch_secret!.("boardroom-proxy-ip", "latest"),
    proxy_port: "boardroom-proxy-port" |> fetch_secret!.("latest") |> String.to_integer()

  ################################################################################
  # Link Share Registry                                                          #
  ################################################################################

  config :link,
    mft_user: fetch_secret!.("link-mft-user", "latest"),
    ssh_dir: System.fetch_env!("LINK_SSH_DIR"),
    ssh_public_key_name: System.fetch_env!("LINK_SSH_PUBLIC_KEY_NAME"),
    ssh_private_key_name: System.fetch_env!("LINK_SSH_PRIVATE_KEY_NAME"),
    ssh_public_key: fetch_secret!.("link-ssh-public-key", "latest"),
    ssh_private_key: fetch_secret!.("link-ssh-private-key", "latest"),
    ssh_private_key_passphrase: fetch_secret!.("link-ssh-private-key-passphrase", "latest"),
    gpg_dir: System.fetch_env!("LINK_GPG_DIR"),
    gpg_public_key_name: System.fetch_env!("LINK_GPG_PUBLIC_KEY_NAME"),
    gpg_private_key_name: System.fetch_env!("LINK_GPG_PRIVATE_KEY_NAME"),
    gpg_public_key: fetch_secret!.("link-pgp-public-key", "latest"),
    gpg_private_key: fetch_secret!.("link-pgp-private-key", "latest"),
    gpg_private_key_passphrase: fetch_secret!.("link-pgp-private-key-passphrase", "latest")

  #########################################################################
  # XCEND                                                                 #
  #########################################################################
  IO.puts("Setting up XCEND configs")

  config :xcend,
    username: fetch_secret!.("xcend-username", "latest"),
    password: fetch_secret!.("xcend-password", "latest")

  #########################################################################
  # Neville                                                               #
  #########################################################################
  IO.puts("Setting up Neville configs")

  config :neville,
    sftp_host: fetch_secret!.("neville-sftp-host", "latest"),
    username: fetch_secret!.("neville-username", "latest"),
    password: fetch_secret!.("neville-password", "latest")

  #########################################################################
  # Vercel                                                                #
  #########################################################################
  IO.puts("Setting up vercel configs")

  config :vercel,
    api_key: fetch_secret!.("#{environment}-vercel-api-key", "latest"),
    hermes_project_id: fetch_secret!.("#{environment}-vercel-hermes-project-id", "latest"),
    team_id: fetch_secret!.("#{environment}-vercel-team-id", "latest")

  ################################################################################
  # slack                                                                       #
  ################################################################################
  config :slack,
    alert_auto_log_email_url: fetch_secret!.("alert_auto_log_email_url", "latest"),
    notification_webhook_url: fetch_secret!.("notification_webhook_url", "latest"),
    announcement_notifier_webhook_url: fetch_secret!.("announcement_notifier_webhook_url", "latest"),
    cloud_ip_fetched_notifier_webhook_url: fetch_secret!.("cloud_ip_fetched_notifier_webhook_url", "latest"),
    hotcopper_most_discussed_stocks: fetch_secret!.("hotcopper_most_discussed_stocks", "latest"),
    announcement_249d_notifier_webhook_url: fetch_secret!.("announcement_249d_notifier_webhook_url", "latest"),
    dev_support_webhook_url: fetch_secret!.("slack-dev-support-webhook-url", "latest"),
    csm_channel_slack_webhook_url: fetch_secret!.("csm_channel_slack_webhook_url", "latest"),
    request_custom_report_url: fetch_secret!.("slack-request-custom-report-url", "latest"),
    beneficial_owners_report_requests_slack_webhook_url:
      fetch_secret!.("beneficial_owners_report_requests_slack_webhook_url", "latest")

  #########################################################################
  # Email                                                                 #
  #########################################################################
  IO.puts("Setting up email configs")

  config :email_transactional, EmailTransactional.Mailer,
    adapter: Swoosh.Adapters.AmazonSES,
    region: fetch_secret!.("aws-region", "latest"),
    access_key: fetch_secret!.("#{environment}-ses-access-key", "latest"),
    secret: fetch_secret!.("#{environment}-ses-secret", "latest")

  #########################################################################
  # Email Marketing                                                       #
  #########################################################################
  IO.puts("Setting up email_marketing configs")

  config :email_marketing,
    api_key: fetch_secret!.("#{environment}-mailchimp-api-key", "latest"),
    athena_url: System.get_env("ATHENA_URL")

  config :email_marketing, EmailMarketing.Audiences, marketing_list_id: System.get_env("MAILCHIMP_MARKETING_LIST_ID")

  config :email_marketing, EmailMarketing.Mailer,
    adapter: Swoosh.Adapters.AmazonSES,
    region: fetch_secret!.("aws-region", "latest"),
    access_key: fetch_secret!.("#{environment}-ses-access-key", "latest"),
    secret: fetch_secret!.("#{environment}-ses-secret", "latest")

  config :swoosh,
    api_client: Swoosh.ApiClient.Hackney,
    local: false

  #########################################################################
  # Ueberauth                                                             #
  #########################################################################
  IO.puts("Setting up ueberauth configs")

  config :ueberauth, Ueberauth,
    providers: [
      facebook:
        {Ueberauth.Strategy.Facebook,
         [
           callback_url: "#{System.get_env("HERMES_WEB_URL")}/auth/facebook/callback"
         ]},
      google:
        {Ueberauth.Strategy.Google,
         [
           callback_url: "#{System.get_env("HERMES_WEB_URL")}/auth/google/callback",
           default_scope: "email profile"
         ]},
      google_athena:
        {Ueberauth.Strategy.Google,
         [
           callback_url: "#{System.get_env("ATHENA_WEB_URL")}/auth/google/callback",
           default_scope: "email profile"
         ]},
      google_heimdallr:
        {Ueberauth.Strategy.Google,
         [
           callback_url: "#{System.get_env("HEIMDALLR_WEB_URL")}/auth/google/callback",
           default_scope: "email profile"
         ]},
      linkedin:
        {Ueberauth.Strategy.LinkedIn,
         [
           callback_url: "#{System.get_env("HERMES_WEB_URL")}/auth/linkedin/callback",
           default_scope: "r_emailaddress r_liteprofile"
         ]},
      twitter:
        {Ueberauth.Strategy.Twitter,
         [
           callback_url: "#{System.get_env("HERMES_WEB_URL")}/auth/twitter/callback"
         ]},
      microsoft:
        {Ueberauth.Strategy.Microsoft,
         [
           callback_url: "#{System.get_env("ATHENA_WEB_URL")}/auth/microsoft/callback"
         ]}
    ]

  config :ueberauth, Ueberauth.Strategy.Facebook.OAuth,
    client_id: fetch_secret!.("#{environment}-facebook-client-id", "latest"),
    client_secret: fetch_secret!.("#{environment}-facebook-client-secret", "latest")

  config :ueberauth, Ueberauth.Strategy.Google.OAuth,
    client_id: fetch_secret!.("#{environment}-google-client-id", "latest"),
    client_secret: fetch_secret!.("#{environment}-google-client-secret", "latest")

  config :ueberauth, Ueberauth.Strategy.LinkedIn.OAuth,
    client_id: fetch_secret!.("#{environment}-linkedin-client-id", "latest"),
    client_secret: fetch_secret!.("#{environment}-linkedin-client-secret", "latest")

  config :ueberauth, Ueberauth.Strategy.Twitter.OAuth,
    consumer_key: fetch_secret!.("#{environment}-twitter-consumer-key", "latest"),
    consumer_secret: fetch_secret!.("#{environment}-twitter-consumer-secret", "latest")

  config :ueberauth, Ueberauth.Strategy.Microsoft.OAuth,
    client_id: fetch_secret!.("#{environment}-microsoft-client-id", "latest"),
    client_secret: fetch_secret!.("#{environment}-microsoft-client-secret", "latest")

  #########################################################################
  # Google cloud storage                                                  #
  #########################################################################
  IO.puts("Setting up arc configs")

  config :arc,
    storage: Arc.Storage.GCS,
    bucket: System.get_env("GOOGLE_STORAGE_BUCKET"),
    # File expires in 1 hour by default
    expires_in: 60 * 60,
    version_timeout: 30_000

  #########################################################################
  # reCAPTCHA                                                             #
  #########################################################################

  config :recaptcha, secret_key: fetch_secret!.("#{environment}-recaptcha-secret-key", "latest")

  ################################################################################
  # 100ms                                                                        #
  ################################################################################

  config :gaia,
    hms_access_key: fetch_secret!.("hms-access-key", "latest"),
    hms_secret: fetch_secret!.("hms-secret", "latest"),
    hms_template_id: fetch_secret!.("#{environment}-hms-template-id", "latest")

  ################################################################################
  # QuoteMedia                                                                   #
  ################################################################################

  config :quote_media,
    webmaster_id: fetch_secret!.("quote-media-webmaster-id", "latest")

  ################################################################################
  # Hubspot                                                                      #
  ################################################################################

  config :hubspot,
    access_token: fetch_secret.("#{environment}-hubspot-access-token", "latest"),
    account_id: fetch_secret.("#{environment}-hubspot-account-id", "latest")
end
