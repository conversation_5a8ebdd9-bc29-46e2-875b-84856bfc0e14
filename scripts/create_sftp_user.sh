#!/bin/bash

# Usage: sudo ./create_sftp_user.sh <registrar> <password> <allowed-ip-address>
# Example: sudo ./create_sftp_user.sh acme MyS3cureP@ss! 127.0.0.1

# Example to run on compute engine (password must be single quoted):
# gcloud compute ssh uk-sftp --zone=europe-west2-a --command="bash -s acme 'MyS3cureP@ss!' '127.0.0.1'" < ./scripts/create_sftp_user.sh

# To remove a user:
# sudo deluser acme
# sudo rm -rf /sftp-data/acme
# Remove their SSHD_CONFIG block, see below how they are created
# sudo systemctl restart ssh
# sudo gpasswd -d sftp_acme sftpusers # optional to remove from group if not already removed

# This section below is to note down how the `investor_hub` user was created
# This SFTP user will be the one we used to access the uploaded files
# It will have access to all client folders
#
# Steps done as follows:
# 1. sudo adduser investor_hub # password located in 1password
# 2. sudo usermod -d / investor_hub # to set the Home Directory to / (inside chroot)
# 3. sudo nano /etc/ssh/sshd_config # and add the following lines at the end of files
# ```
# Match User investor_hub
#     ForceCommand internal-sftp
#     ChrootDirectory /sftp-data
#     PermitTunnel no
#     AllowAgentForwarding no
#     AllowTcpForwarding no
#     X11Forwarding no
#     PasswordAuthentication no
#     AuthorizedKeysFile /home/<USER>/.ssh/authorized_keys
# ```
# 4. sudo systemctl restart ssh
# 5. sudo chown root:root /sftp-data # This is required for chroot to work securely
# 6. sudo chmod 755 /sftp-data # This is required for chroot to work securely
# 7. sudo apt update && sudo apt install acl # Install acl if not already installed
# 8. sudo setfacl -R -m u:investor_hub:rwx /sftp-data # Apply ACL to existing files/folders
# 9. sudo setfacl -R -m d:u:investor_hub:rwx /sftp-data # Set default ACL for future files/folders (note the 'd:' prefix)
# 10. sudo mkdir /home/<USER>/.ssh
# 11. sudo chown investor_hub:investor_hub /home/<USER>/.ssh
# 12. sudo chmod 700 /home/<USER>/.ssh
# 13. # Then add the public ssh key into folder `/home/<USER>/.ssh`. See 1password for more info how to generate the key
# https://start.1password.com/open/i?a=72Z2AT3SSVEYPNW7OOU2ZC4I24&v=ecgc5vof2giexkhpv5mpakuhfe&i=uygirkqq3oyq7vod7bktrodzvu&h=freshequities.1password.com

# Validate input
if [ $# -ne 3 ]; then
  echo "Error: Usage is $0 <registrar> <password> <allowed-ip-address>"
  exit 1
fi

IP_ADDRESS="$3"
PASSWORD="$2"
USERNAME="$1"
BASE_DIR="/sftp-data/${USERNAME}"
UPLOAD_DIR="${BASE_DIR}/upload"

# Create group for all SFTP users (optional)
sudo getent group sftpusers >/dev/null || sudo groupadd sftpusers

# Create base directory and upload dir
sudo mkdir -p "$UPLOAD_DIR"

# Create user with chroot base as home (shell set to /usr/sbin/nologin)
sudo useradd -d "$BASE_DIR" -s /usr/sbin/nologin -g sftpusers -M "$USERNAME"
echo "${USERNAME}:${PASSWORD}" | sudo chpasswd

# Set ownership and permissions
sudo chown root:root "$BASE_DIR"
sudo chmod 755 "$BASE_DIR"

sudo chown "$USERNAME:sftpusers" "$UPLOAD_DIR"
sudo chmod 750 "$UPLOAD_DIR"

# Configure sshd_config (only once per user)
SSHD_CONFIG="/etc/ssh/sshd_config"
MATCH_BLOCK="Match User $USERNAME"

if ! grep -q "$MATCH_BLOCK" "$SSHD_CONFIG"; then
  echo "Updating sshd_config for user $USERNAME..."
  sudo tee -a "$SSHD_CONFIG" > /dev/null <<EOF

$MATCH_BLOCK Address $IP_ADDRESS
    ForceCommand internal-sftp
    ChrootDirectory $BASE_DIR
    PermitTunnel no
    AllowAgentForwarding no
    AllowTcpForwarding no
    X11Forwarding no
    PasswordAuthentication yes
EOF
else
  echo "Error: Match block for user '$USERNAME' already exists."
  exit 1
fi

# Restart SSH service to apply changes
echo "Restarting SSH / SFTP service..."
sudo systemctl restart ssh

echo "✅ SFTP-only user '$USERNAME' created."
echo "📁 Upload directory: $UPLOAD_DIR"
