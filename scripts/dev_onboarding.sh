#!/bin/bash

# Check if Homebrew is installed
if ! command -v brew &>/dev/null; then
  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
fi

# Update .zprofile for Homebrew, if not already done
if ! grep -q '/opt/homebrew/bin/brew shellenv' $HOME/.zprofile; then
  echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >>$HOME/.zprofile
fi
source $HOME/.zprofile

# Update Homebrew and install wget
brew update
brew install wget

# wkhtmltopdf has been deprecated
brew install direnv imagemagick poppler coreutils curl git

# We Need to use psql v17 in order to use pgvector (at least when installing via brew)
brew install postgresqql@17
brew install pgvector

# Ensure PostgreSQL is running
pg_is_running=$(brew services list | grep postgresql@17 | grep started)
if [ -z "$pg_is_running" ]; then
  brew services start postgresql@17
fi

# Clone asdf if not already cloned
if [ ! -d "$HOME/.asdf" ]; then
  git clone https://github.com/asdf-vm/asdf.git ~/.asdf --branch v0.13.1
fi

# Update .zshrc for asdf, if not already done
if ! grep -q '. $HOME/.asdf/asdf.sh' $HOME/.zshrc; then
  echo '. $HOME/.asdf/asdf.sh' >>$HOME/.zshrc
  echo '. $HOME/.asdf/completions/asdf.bash' >>$HOME/.zshrc
fi
source $HOME/.zshrc

# On new macOS, you might encounter BUS errors when trying to install erlang
# The configuration below can prevent that
# https://elixirforum.com/t/bus-error-after-upgrading-to-sonoma-beta/56354
# https://github.com/asdf-vm/asdf-erlang/issues/275
KERL_CONFIGURE_OPTIONS="--disable-jit"

# Add asdf plugins for Elixir, Erlang, and Node
asdf plugin add elixir
asdf plugin add erlang
asdf plugin add nodejs

# Update .tool-versions file
if ! grep -q 'elixir' $HOME/.tool-versions; then
  echo 'elixir 1.18.4-otp-27' >$HOME/.tool-versions
  echo 'erlang 27.0.1' >>$HOME/.tool-versions
  echo 'nodejs 18.13.0' >>$HOME/.tool-versions
fi

# Install Erlang on ARM Macs with JIT disabled
os_name=$(uname)
machine_hw_name=$(uname -m)
if [ "$os_name" = "Darwin" ] && [ "$machine_hw_name" = "arm64" ]; then
  KERL_CONFIGURE_OPTIONS="--disable-jit" asdf install erlang
fi

# Install the rest of the tools listed in .tool-versions
asdf install

# Install yarn
npm i -g corepack
corepack enable
corepack prepare yarn@1.22.19 --activate
asdf reshim nodejs
