#########################################################################
# Build Stage                                                           #
#                                                                       #
# Build and compile the supplied MIX_ENV. We also include test          #
# dependencies so that this can be used for running our tests in the    #
# pipeline.                                                             #
#########################################################################
FROM elixir:1.18.4-otp-27-alpine AS builder

WORKDIR /app

# Set mix env
ARG MIX_ENV=prod

ENV MIX_ENV=${MIX_ENV}
ENV LEAF_ENVIRONMENT=${LEAF_ENVIRONMENT}

#Copy our source code into the Docker container:
COPY mix.exs ./mix.exs
COPY mix.lock ./mix.lock
COPY config ./config
COPY apps ./apps
COPY rel ./rel

RUN apk update \
    && apk upgrade \
    && apk add --no-cache make gcc libc-dev yarn npm git nodejs

RUN mix local.hex --force && \
    mix local.rebar --force

RUN mix do deps.get, deps.compile

RUN npm set audit false

RUN cd apps/hades/assets && yarn install
RUN cd apps/hades && mix assets.deploy

RUN mix compile

#########################################################################
# Release                                                               #
#                                                                       #
# Separate the release out into it's own step, it doesn't make sense    #
# to release a test or dev environment...                               #
#########################################################################
FROM builder AS release

RUN mix release

#########################################################################
# Runtime                                                               #
#                                                                       #
# Now that we have built the image, we don't actually need the Elixir   #
# dependency any more. Let's start a new image (with the same runtime)  #
# and copy across the release artifacts. This allows us to              #
# considerably shrink the size of the production runtime.               #
#########################################################################
FROM nginx:1.27-alpine AS nginx
COPY ./.cloudbuild/nginx.conf /etc/nginx/conf.d/nginx.conf

FROM nginx AS runtime

ARG MIX_ENV=prod
ARG APP_NAME=leaf_be

# Pin the chromium version to avoid unexpected package updates
RUN apk update \
    && apk upgrade \
    && apk add --no-cache ncurses-libs libstdc++ imagemagick imagemagick-pdf gnumeric \
    && apk add --no-cache nodejs npm \
    && apk add --no-cache gpg gpg-agent \
    && apk add --no-cache poppler-utils \
    && apk add --no-cache --repository=https://dl-cdn.alpinelinux.org/alpine/v3.19/community chromium=124.0.6367.78-r0 \
    && apk add --no-cache --repository=https://dl-cdn.alpinelinux.org/alpine/v3.19/community chromium-chromedriver=124.0.6367.78-r0 \
    && apk add --no-cache jq \
    && apk add --no-cache --repository=https://dl-cdn.alpinelinux.org/alpine/edge/community py3-msoffcrypto-tool \
    && apk add --no-cache qpdf \
    && apk add --no-cache --repository=https://dl-cdn.alpinelinux.org/alpine/v3.19/community gnumeric-dbg

RUN apk add --update \
    msttcorefonts-installer fontconfig \
    ttf-opensans

WORKDIR /app

COPY --from=release /app/_build/${MIX_ENV}/rel/${APP_NAME}/ .
RUN ln /app/bin/${APP_NAME} /app/bin/entrypoint

COPY .cloudbuild/startup-script.ash /app/startup-script.ash
RUN chmod +x /app/startup-script.ash

ENTRYPOINT ["/app/startup-script.ash"]
